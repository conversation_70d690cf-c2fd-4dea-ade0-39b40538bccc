function [OM] = add_source2dA(OM,source,it)
% ADD_SOURCE2DA Summary of this function goes here

som  = source.iom;
invMsg12 = source.invMsg12;
invMsg21 = source.invMsg21;
Fit = source.Ft(it);

OM(som).state.dU2dtt12 = OM(som).state.dU2dtt12 + Fit*invMsg12;
OM(som).state.dU2dtt21 = OM(som).state.dU2dtt21 + Fit*invMsg21;

for iom = 1:length(OM)
    Jac12 = OM(iom).Jac12;
    Jac21 = OM(iom).Jac21;
    rho12 = OM(iom).rho12;
    rho21 = OM(iom).rho21;
    OM(iom).state.dU2dtt12 = OM(iom).state.dU2dtt12./Jac12./rho12;
    OM(iom).state.dU2dtt21 = OM(iom).state.dU2dtt21./Jac21./rho21;
end

end

