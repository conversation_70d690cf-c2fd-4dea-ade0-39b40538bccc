function [OM] = compute_KS2dA(OM)
%COMPUTE_KS Summary of this function goes here

  for iom = 1:length(OM)

    % kk
    kkx12     = OM(iom).kkx12; 
    kkz12     = OM(iom).kkz12; 
    kkx21     = OM(iom).kkx21; 
    kkz21     = OM(iom).kkz21; 

    % invL
    invLx11   = OM(iom).invLx11;
    invLz11   = OM(iom).invLz11;
    invLx22   = OM(iom).invLx22;
    invLz22   = OM(iom).invLz22;
    invLxT11  = OM(iom).invLxT11;
    invLzT11  = OM(iom).invLzT11;
    invLxT22  = OM(iom).invLxT22;
    invLzT22  = OM(iom).invLzT22;

    % S11
    Sxx11 = OM(iom).state.Sxx11;
    Szz11 = OM(iom).state.Szz11;
    % S22
    Sxx22 = OM(iom).state.Sxx22;
    Szz22 = OM(iom).state.Szz22;

    % Smo
    Sxx11mo = OM(iom).state.Sxx11mo; 
    Sxx22mo = OM(iom).state.Sxx22mo;
    % Spo
    Sxx11po = OM(iom).state.Sxx11po;  
    Sxx22po = OM(iom).state.Sxx22po; 
    
    % Som
    Szz11om = OM(iom).state.Szz11om;  
    Szz22om = OM(iom).state.Szz22om;
    % Sop
    Szz11op = OM(iom).state.Szz11op;
    Szz22op = OM(iom).state.Szz22op;

    % size(kkx21)
    % size(Sxx11)
    % size(invLxT11)
    % size(invLx22)
    % size(Sxx11mo)
    % size(Sxx11po)

    % % dSdxyzp 
    % dSdxp    
    dSxxdxp21 = dFdxp(kkx21, Sxx11, invLxT11, invLx22, Sxx11mo, Sxx11po);
    dSxxdxp12 = dFdxp(kkx12, Sxx22, invLxT22, invLx11, Sxx22mo, Sxx22po);

    % dSdzp
    dSzzdzp21 = dFdzp(kkz12, Szz22, invLzT22, invLz11, Szz22om, Szz22op);
    dSzzdzp12 = dFdzp(kkz21, Szz11, invLzT11, invLz22, Szz11om, Szz11op);
    
    % internal forces
    OM(iom).state.dU2dtt21 = dSxxdxp21 + dSzzdzp21;
    OM(iom).state.dU2dtt12 = dSxxdxp12 + dSzzdzp12;  

     
  end


end


function dUdxp11 = dFdxp(kkx12,U21,invLx22T,invLx11,U21mo,U21po)

     % to obtain dUdx111 with U211
     % from orth base to bsplie
     Ub21    = pagemtimes(invLx22T,U21);
     dUdxp11 = pagemtimes(-kkx12,Ub21);
     % impose the boundary 
     dUdxp11(1,:)   = dUdxp11(1,:)   - reshape(U21mo,1,length(U21mo));
     dUdxp11(end,:) = dUdxp11(end,:) + reshape(U21po,1,length(U21po));
     % back to orth base
     dUdxp11 = pagemtimes(invLx11,dUdxp11);

end




function dUdzp11 = dFdzp(kkz12,U12,invLzT22,invLz11,U12om,U12op)

    % to obtain dUdzp11 with U12
    U12     = permute(U12,[2,1]);
    Ub12    = pagemtimes(invLzT22,U12);
    dUdzp11 = pagemtimes(-kkz12,Ub12);
    dUdzp11 = permute(dUdzp11,[2,1]);
    % impose the boundary 
    dUdzp11(:,1)   = dUdzp11(:,1)   - reshape(U12om,length(U12om),1);
    dUdzp11(:,end) = dUdzp11(:,end) + reshape(U12op,length(U12op),1);
    %
    dUdzp11 = permute(dUdzp11,[2,1]);
    dUdzp11 = pagemtimes(invLz11,dUdzp11);
    dUdzp11 = permute(dUdzp11,[2,1]);

end

