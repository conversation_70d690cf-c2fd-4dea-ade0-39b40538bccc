function [OM] = compute_KU2dA(OM)
%COMPUTE_INTERNAL_FORCES Summary of this function goes here

  for iom = 1:length(OM)

    % U
    U12 = OM(iom).state.U12;
    U21 = OM(iom).state.U21;

    % Umo
    U12mo = OM(iom).state.U12mo;
    U21mo = OM(iom).state.U21mo;

    % Upo
    U12po = OM(iom).state.U12po;
    U21po = OM(iom).state.U21po;
  
    % Uom
    U12om = OM(iom).state.U12om;
    U21om = OM(iom).state.U21om;

    % Uop
    U12op = OM(iom).state.U12op;
    U21op = OM(iom).state.U21op;
  

    % kk
    kkx12     = OM(iom).kkx12; 
    kkz12     = OM(iom).kkz12; 
    kkx21     = OM(iom).kkx21; 
    kkz21     = OM(iom).kkz21; 
    % invL
    invLx11   = OM(iom).invLx11;
    invLz11   = OM(iom).invLz11;
    invLx22   = OM(iom).invLx22;
    invLz22   = OM(iom).invLz22;
    invLxT11  = OM(iom).invLxT11;
    invLzT11  = OM(iom).invLzT11;
    invLxT22  = OM(iom).invLxT22;
    invLzT22  = OM(iom).invLzT22;

    % dxzpdx
    dxpdx11 = OM(iom).dxpdx11;
    dxpdx22 = OM(iom).dxpdx22;
    dzpdx11 = OM(iom).dzpdx11;
    dzpdx22 = OM(iom).dzpdx22;

    % dxzpdz
    dxpdz11 = OM(iom).dxpdz11;
    dxpdz22 = OM(iom).dxpdz22;
    dzpdz11 = OM(iom).dzpdz11;
    dzpdz22 = OM(iom).dzpdz22;

    % Jac
    Jac11 = OM(iom).Jac11;
    Jac22 = OM(iom).Jac22;

    % mu
    mu11 = OM(iom).mu11;
    mu22 = OM(iom).mu22;

    % dUdxp
    dUdxp11 = dFdxp(kkx12,U21,invLxT22,invLx11,U21mo,U21po);
    dUdxp22 = dFdxp(kkx21,U12,invLxT11,invLx22,U12mo,U12po);

    % dUdzp
    dUdzp11 = dFdzp(kkz12,U12,invLzT22,invLz11,U12om,U12op);
    dUdzp22 = dFdzp(kkz21,U21,invLzT11,invLz22,U21om,U21op);

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    % % Jacobian
    % dUdx
    dUdx11 = dUdxp11.*dxpdx11 + dUdzp11.*dzpdx11;
    dUdx22 = dUdxp22.*dxpdx22 + dUdzp22.*dzpdx22;
    % dUdz
    dUdz11 = dUdxp11.*dxpdz11 + dUdzp11.*dzpdz11;
    dUdz22 = dUdxp22.*dxpdz22 + dUdzp22.*dzpdz22;
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
   

    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    % four grids contributions
    % txx
    txx11 = mu11.*dUdx11;
    txx22 = mu22.*dUdx22;
    % tzz
    tzz11 = mu11.*dUdz11;
    tzz22 = mu22.*dUdz22;
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    OM(iom).state.Sxx11 = (txx11 .* dxpdx11 + tzz11 .* dxpdz11).*Jac11; % dx
    OM(iom).state.Szz11 = (txx11 .* dzpdx11 + tzz11 .* dzpdz11).*Jac11; % dz
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
    OM(iom).state.Sxx22 = (txx22 .* dxpdx22 + tzz22 .* dxpdz22).*Jac22; % dx
    OM(iom).state.Szz22 = (txx22 .* dzpdx22 + tzz22 .* dzpdz22).*Jac22; % dz
    %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

  end


end

function dUdxp11 = dFdxp(kkx12,U21,invLx22T,invLx11,U21mo,U21po)

     % to obtain dUdx111 with U211
     % from orth base to bsplie
     Ub21    = pagemtimes(invLx22T,U21);
     dUdxp11 = pagemtimes(-kkx12,Ub21);
     % impose the boundary 
     dUdxp11(1,:)   = dUdxp11(1,:)   - reshape(U21mo,1,length(U21mo));
     dUdxp11(end,:) = dUdxp11(end,:) + reshape(U21po,1,length(U21po));
     % back to orth base
     dUdxp11 = pagemtimes(invLx11,dUdxp11);

end




function dUdzp11 = dFdzp(kkz12,U12,invLzT22,invLz11,U12om,U12op)

    % to obtain dUdzp11 with U12
    U12     = permute(U12,[2,1]);
    Ub12    = pagemtimes(invLzT22,U12);
    dUdzp11 = pagemtimes(-kkz12,Ub12);
    dUdzp11 = permute(dUdzp11,[2,1]);
    % impose the boundary 
    dUdzp11(:,1)   = dUdzp11(:,1)   - reshape(U12om,length(U12om),1);
    dUdzp11(:,end) = dUdzp11(:,end) + reshape(U12op,length(U12op),1);
    %
    dUdzp11 = permute(dUdzp11,[2,1]);
    dUdzp11 = pagemtimes(invLz11,dUdzp11);
    dUdzp11 = permute(dUdzp11,[2,1]);

end

