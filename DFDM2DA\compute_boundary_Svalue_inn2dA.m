function [OM] = compute_boundary_Svalue_inn2dA(OM)

ndomain = length(OM);

for iom = 1:ndomain

    %
    invLxT11 = OM(iom).invLxT11;
    invLzT11 = OM(iom).invLzT11;
    invLxT22 = OM(iom).invLxT22;
    invLzT22 = OM(iom).invLzT22;

    %
    Sxx11 = OM(iom).state.Sxx11;
    Szz11 = OM(iom).state.Szz11;
    Sxx22 = OM(iom).state.Sxx22;
    Szz22 = OM(iom).state.Szz22;

    [Sxx11mo, Sxx11po, Sxx11om, Sxx11op] = compute_boundary_Svalue_inn_elem(invLxT11,invLzT11,Sxx11);
    [Sxx22mo, Sxx22po, Sxx22om, Sxx22op] = compute_boundary_Svalue_inn_elem(invLxT22,invLzT22,Sxx22);
    [Szz11mo, Szz11po, Szz11om, Szz11op] = compute_boundary_Svalue_inn_elem(invLxT11,invLzT11,Szz11);
    [Szz22mo, Szz22po, Szz22om, Szz22op] = compute_boundary_Svalue_inn_elem(invLxT22,invLzT22,Szz22);

    OM(iom).state.Sxx11mo_inn = Sxx11mo;
    OM(iom).state.Sxx11po_inn = Sxx11po;
    OM(iom).state.Sxx22mo_inn = Sxx22mo;
    OM(iom).state.Sxx22po_inn = Sxx22po;

    OM(iom).state.Szz11om_inn = Szz11om;
    OM(iom).state.Szz11op_inn = Szz11op;
    OM(iom).state.Szz22om_inn = Szz22om;
    OM(iom).state.Szz22op_inn = Szz22op;

    OM(iom).state.Sxx11om_inn = Sxx11om;
    OM(iom).state.Sxx11om_inn = Sxx11op;
    OM(iom).state.Sxx22om_inn = Sxx22om;
    OM(iom).state.Sxx22op_inn = Sxx22op;
    
    OM(iom).state.Szz11mo_inn = Szz11mo;
    OM(iom).state.Szz11po_inn = Szz11po;
    OM(iom).state.Szz22mo_inn = Szz22mo;
    OM(iom).state.Szz22po_inn = Szz22po;

     % rotation
     iNbr_mo = OM(iom).iNbr_mo;
     if iNbr_mo~=0
         rot_mo = OM(iom).rot_mo;
         [Sxx11mo,Szz11mo] = rotate_sij(Sxx11mo,Szz11mo,rot_mo);
         [Sxx22mo,Szz22mo] = rotate_sij(Sxx22mo,Szz22mo,rot_mo);
     end 

     iNbr_po = OM(iom).iNbr_po;
     if iNbr_po~=0
         rot_po = OM(iom).rot_po;
         [Sxx11po,Szz11po] = rotate_sij(Sxx11po,Szz11po,rot_po);
         [Sxx22po,Szz22po] = rotate_sij(Sxx22po,Szz22po,rot_po);
     end 

     iNbr_om = OM(iom).iNbr_om;
     if iNbr_om~=0
         rot_om = OM(iom).rot_om;
         [Sxx11om,Szz11om] = rotate_sij(Sxx11om,Szz11om,rot_om);
         [Sxx22om,Szz22om] = rotate_sij(Sxx22om,Szz22om,rot_om);
     end 
     iNbr_op = OM(iom).iNbr_op;
     if iNbr_op~=0
         rot_op = OM(iom).rot_op;
         [Sxx11op,Szz11op] = rotate_sij(Sxx11op,Szz11op,rot_op);
         [Sxx22op,Szz22op] = rotate_sij(Sxx22op,Szz22op,rot_op);
     end 

     OM(iom).state.Sxx11mo_innr = Sxx11mo;
     OM(iom).state.Sxx11po_innr = Sxx11po;
     OM(iom).state.Sxx22mo_innr = Sxx22mo;
     OM(iom).state.Sxx22po_innr = Sxx22po;
 
     OM(iom).state.Szz11om_innr = Szz11om;
     OM(iom).state.Szz11op_innr = Szz11op;
     OM(iom).state.Szz22om_innr = Szz22om;
     OM(iom).state.Szz22op_innr = Szz22op;
 
     OM(iom).state.Sxx11om_innr = Sxx11om;
     OM(iom).state.Sxx11op_innr = Sxx11op;
     OM(iom).state.Sxx22om_innr = Sxx22om;
     OM(iom).state.Sxx22op_innr = Sxx22op;
     
     OM(iom).state.Szz11mo_innr = Szz11mo;
     OM(iom).state.Szz11po_innr = Szz11po;
     OM(iom).state.Szz22mo_innr = Szz22mo;
     OM(iom).state.Szz22po_innr = Szz22po;

    

end


end

function [Sxx11mo,Sxx11po,Sxx11om,Sxx11op] = compute_boundary_Svalue_inn_elem(invLxT11,invLzT11,Sxx11)
    tmp     = invLxT11*Sxx11;
    Sxx11mo = tmp(1,:);
    Sxx11po = tmp(end,:);
    tmp     = Sxx11';
    tmp     = invLzT11*tmp;
    tmp     = tmp';
    Sxx11om = tmp(:,1);
    Sxx11op = tmp(:,end);
end

% function [Umo,Upo,Uom,Uop] = compute_boundary_Svalue_inn_elem(invLxT,invLzT,U)
% % x direction
% Umid0 = pagemtimes(invLxT,U);
% Umid1 = Umid0(1,:);  
% Umid1 = squeeze(Umid1);
% Umid2 = invLzT*Umid1;
% Umid3 = Umid2';
% Umid4 = invLzT*Umid3;
% Umo  = Umid4';
% 
% Umid1 = Umid0(end,:);
% Umid1 = squeeze(Umid1);
% Umid2 = invLyT*Umid1;
% Umid3 = Umid2';
% Umid4 = invLzT*Umid3;
% Upo  = Umid4';
% 
% % y direction
% Umid  = permute(U,[2,1]);
% Umid0 = pagemtimes(invLyT,Umid);
% Umid0 = permute(Umid0,[2,1]);
% 
% Umid1 = Umid0(:,1,:);  
% Umid1 = squeeze(Umid1);
% Umid2 = invLxT*Umid1;
% Umid3 = Umid2';
% Umid4 = invLzT*Umid3;
% Uom  = Umid4';
% 
% Umid1 = Umid0(:,end,:); 
% Umid1 = squeeze(Umid1);
% Umid2 = invLxT*Umid1;
% Umid3 = Umid2';
% Umid4 = invLzT*Umid3;
% Uop  = Umid4';
% 
% end

% function [Umo,Upo,Uom,Uop] = compute_boundary_value_inn_elem(invLxT,invLzT,U)
% % x direction
% Umid0 = pagemtimes(invLxT,U);
% Umid1 = invLzT*Umid0';
% Umid1 = Umid1';
% 
% Umo  = Umid1(1,:);
% Upo  = Umid1(end,:);
% Uom  = Umid1(:,1);
% Uop  = Umid1(:,end);
% 
% Umo  = reshape(Umo,length(Umo),1);  % left    
% Upo  = reshape(Upo,length(Upo),1);  % right
% Uom  = reshape(Uom,length(Uom),1);  % bottom
% Uop  = reshape(Uop,length(Uop),1);  % upper
% 
% % Very important, to be checked.
% % there is another way only take boundary values to participate the calculation, after multiplication with invL
% end
% 
% 
% function [sxx, szz] = rotate_sij(sxx, szz, rot_mat)
%     % ROTATE_SIJ Rotates the stress components using the given rotation matrix
%     % Parameters:
%     %   sxx, sxz, szx, szz: 2D stress component matrices
%     %   rot: 2D rotation matrix
% 
%     [nx, nz] = size(sxx);
%     sij = zeros(2, 2);
% 
%     for k = 1:nz
%         for i = 1:nx
%             % Assign the stress components to the sij matrix
%             sij(1,1) = sxx(i,k);
%             % sij(1,2) = 0;
%             % sij(2,1) = 0;
%             sij(2,2) = szz(i,k);
%             % Perform the rotation using matrix multiplication
% 
%             sij = rot_mat* sij * inv(rot_mat);
%             
%             % Assign the rotated stress components back to the original matrices
%             sxx(i,k) = sij(1,1);
%             % sxz(i,k) = sij(1,2);
%             % szx(i,k) = sij(2,1);
%             szz(i,k) = sij(2,2);
%         end
%     end
%     
% end

