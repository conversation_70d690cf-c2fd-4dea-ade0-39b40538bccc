function [OM] = compute_boundary_Svalue_out2dA(OM)
 
    for iom = 1:length(OM)
        
        iNbr_mo = OM(iom).iNbr_mo;
        iNbr_po = OM(iom).iNbr_po;
        iNbr_om = OM(iom).iNbr_om;
        iNbr_op = OM(iom).iNbr_op;

        invLx11_iom  = OM(iom).invLx11;
        invLx22_iom  = OM(iom).invLx22;
        invLz11_iom  = OM(iom).invLz11;
        invLz22_iom  = OM(iom).invLz22;

        if iNbr_mo~=0
    
            jFace = OM(iom).iFace_mo;
            %
            Sxx11mo_inn = OM(iNbr_mo).state.Sxx11mo_innr;
            Sxx11po_inn = OM(iNbr_mo).state.Sxx11po_innr;
            Sxx22mo_inn = OM(iNbr_mo).state.Sxx22mo_innr;
            Sxx22po_inn = OM(iNbr_mo).state.Sxx22po_innr;
            %
            Sxx11om_inn = OM(iNbr_mo).state.Sxx11om_innr;
            Sxx11op_inn = OM(iNbr_mo).state.Sxx11op_innr;
            Sxx22om_inn = OM(iNbr_mo).state.Sxx22om_innr;
            Sxx22op_inn = OM(iNbr_mo).state.Sxx22op_innr;
            %
            Sxx11mo_out_tmp = select_face(Sxx11mo_inn, Sxx11po_inn, Sxx11om_inn, Sxx11op_inn, jFace);  
            Sxx22mo_out_tmp = select_face(Sxx22mo_inn, Sxx22po_inn, Sxx22om_inn, Sxx22op_inn, jFace); 
            %
            if abs(jFace)==1 || abs(jFace)==2
                invLzT11 = OM(iNbr_mo).invLzT11;
                invLzT22 = OM(iNbr_mo).invLzT22;
                Sxx11mo_out = invLz11_iom*OM(iom).Dzz110mo*(invLzT11*Sxx11mo_out_tmp');  % Filt_1D_x(qx2m,FFilt_1D_y(L2y,Dy220mo,ux12mo_ext)), to be checked where the L2y is used
                Sxx22mo_out = invLz22_iom*OM(iom).Dzz220mo*(invLzT22*Sxx22mo_out_tmp');
            else
                % elem 1
                invLxT11 = OM(iNbr_mo).invLxT11;
                invLxT22 = OM(iNbr_mo).invLxT22;
                Sxx11mo_out = invLz11_iom*OM(iom).Dzx120mo*(invLxT22*Sxx22mo_out_tmp);  % 这里为什么是Sxx22?? 也许我们可以从1套网格开始行动。等把一个单元的bug解决以后再试试。
                Sxx22mo_out = invLz22_iom*OM(iom).Dzx210mo*(invLxT11*Sxx11mo_out_tmp);  % 这里为什么是Sxx11??
            end
            %
            OM(iom).state.Sxx11mo_out = reshape(Sxx11mo_out,1,length(Sxx11mo_out));  % Filt_1D_x(qx2m,FFilt_1D_y(L2y,Dy220mo,ux12mo_ext)), to be checked where the L2y is used
            OM(iom).state.Sxx22mo_out = reshape(Sxx22mo_out,1,length(Sxx22mo_out));

        end
    
        
        if iNbr_po~=0
    
            jFace = OM(iom).iFace_po;
            %
            Sxx11mo_inn = OM(iNbr_po).state.Sxx11mo_innr;
            Sxx11po_inn = OM(iNbr_po).state.Sxx11po_innr;
            Sxx22mo_inn = OM(iNbr_po).state.Sxx22mo_innr;
            Sxx22po_inn = OM(iNbr_po).state.Sxx22po_innr;
            %
            Sxx11om_inn = OM(iNbr_po).state.Sxx11om_innr;
            Sxx11op_inn = OM(iNbr_po).state.Sxx11op_innr;
            Sxx22om_inn = OM(iNbr_po).state.Sxx22om_innr;
            Sxx22op_inn = OM(iNbr_po).state.Sxx22op_innr;
            %
            Sxx11po_out_tmp = select_face(Sxx11mo_inn, Sxx11po_inn, Sxx11om_inn, Sxx11op_inn, jFace);  
            Sxx22po_out_tmp = select_face(Sxx22mo_inn, Sxx22po_inn, Sxx22om_inn, Sxx22op_inn, jFace); 
            %
            if abs(jFace)==1 || abs(jFace)==2
                invLzT11 = OM(iNbr_po).invLzT11;
                invLzT22 = OM(iNbr_po).invLzT22;
                Sxx11po_out = invLz11_iom*OM(iom).Dzz110po*(invLzT11*Sxx11po_out_tmp');
                Sxx22po_out = invLz22_iom*OM(iom).Dzz220po*(invLzT22*Sxx22po_out_tmp');
            else
                invLxT11 = OM(iNbr_po).invLxT11;
                invLxT22 = OM(iNbr_po).invLxT22;
                Sxx11po_out = invLz11_iom*OM(iom).Dzx120po*(invLxT22*Sxx22po_out_tmp);  % 这里为什么是Sxx22??
                Sxx22po_out = invLz22_iom*OM(iom).Dzx210po*(invLxT11*Sxx11po_out_tmp);  % 这里为什么是Sxx11??
            end
            %
            OM(iom).state.Sxx11po_out = reshape(Sxx11po_out,1,length(Sxx11po_out));
            OM(iom).state.Sxx22po_out = reshape(Sxx22po_out,1,length(Sxx22po_out));
    
        end
    
        
        if iNbr_om~=0
    
            jFace = OM(iom).iFace_om;
            
            Szz11om_inn = OM(iNbr_om).state.Szz11om_innr;
            Szz11op_inn = OM(iNbr_om).state.Szz11op_innr;
            Szz22om_inn = OM(iNbr_om).state.Szz22om_innr;
            Szz22op_inn = OM(iNbr_om).state.Szz22op_innr;
            %
            Szz11mo_inn = OM(iNbr_om).state.Szz11mo_innr;
            Szz11po_inn = OM(iNbr_om).state.Szz11po_innr;
            Szz22mo_inn = OM(iNbr_om).state.Szz22mo_innr;
            Szz22po_inn = OM(iNbr_om).state.Szz22po_innr;
            %
            Szz11om_out_tmp = select_face(Szz11mo_inn, Szz11po_inn, Szz11om_inn, Szz11op_inn, jFace);  
            Szz22om_out_tmp = select_face(Szz22mo_inn, Szz22po_inn, Szz22om_inn, Szz22op_inn, jFace); 
            %
            if abs(jFace)==3 || abs(jFace)==4
                invLxT11 = OM(iNbr_om).invLxT11;
                invLxT22 = OM(iNbr_om).invLxT22;
                Szz11om_out = invLx11_iom*OM(iom).Dxx110om*(invLxT11*Szz11om_out_tmp);
                Szz22om_out = invLx22_iom*OM(iom).Dxx220om*(invLxT22*Szz22om_out_tmp);
            else
                % elem 2
                invLzT11 = OM(iNbr_om).invLzT11;
                invLzT22 = OM(iNbr_om).invLzT22;
                Szz11om_out = invLx11_iom*OM(iom).Dxz120om*(invLzT22*Szz22om_out_tmp');  % 这里为什么是Szz22??
                Szz22om_out = invLx22_iom*OM(iom).Dxz210om*(invLzT11*Szz11om_out_tmp');  % 这里为什么是Szz11??
            end
            %
            OM(iom).state.Szz11om_out = reshape(Szz11om_out,length(Szz11om_out),1);
            OM(iom).state.Szz22om_out = reshape(Szz22om_out,length(Szz22om_out),1);
        end
    
        
        if iNbr_op~=0
    
            jFace = OM(iom).iFace_op;
            %
            Szz11om_inn = OM(iNbr_op).state.Szz11om_innr;
            Szz11op_inn = OM(iNbr_op).state.Szz11op_innr;
            Szz22om_inn = OM(iNbr_op).state.Szz22om_innr;
            Szz22op_inn = OM(iNbr_op).state.Szz22op_innr;
            %
            Szz11mo_inn = OM(iNbr_op).state.Szz11mo_innr;
            Szz11po_inn = OM(iNbr_op).state.Szz11po_innr;
            Szz22mo_inn = OM(iNbr_op).state.Szz22mo_innr;
            Szz22po_inn = OM(iNbr_op).state.Szz22po_innr;
            %
            Szz11op_out_tmp = select_face(Szz11mo_inn, Szz11po_inn, Szz11om_inn, Szz11op_inn, jFace);  
            Szz22op_out_tmp = select_face(Szz22mo_inn, Szz22po_inn, Szz22om_inn, Szz22op_inn, jFace); 
            %
            if abs(jFace)==3 || abs(jFace)==4
                invLxT11 = OM(iNbr_op).invLxT11;
                invLxT22 = OM(iNbr_op).invLxT22;
                Szz11op_out = invLx11_iom*OM(iom).Dxx110op*(invLxT11*Szz11op_out_tmp);
                Szz22op_out = invLx22_iom*OM(iom).Dxx220op*(invLxT22*Szz22op_out_tmp);
            else
                invLzT11 = OM(iNbr_op).invLzT11;
                invLzT22 = OM(iNbr_op).invLzT22;
                Szz11op_out = invLx11_iom*OM(iom).Dxz120op*(invLzT22*Szz22op_out_tmp');  % 这里为什么是Szz22??
                Szz22op_out = invLx22_iom*OM(iom).Dxz210op*(invLzT11*Szz11op_out_tmp');  % 这里为什么是Szz11??
            end
            %
            OM(iom).state.Szz11op_out = reshape(Szz11op_out,length(Szz11op_out),1);
            OM(iom).state.Szz22op_out = reshape(Szz22op_out,length(Szz22op_out),1);

        end


    end


    for iom = 1:length(OM)
    
        alpha_mo = OM(iom).alpha_mo;
        alpha_po = OM(iom).alpha_po;
        alpha_om = OM(iom).alpha_om;
        alpha_op = OM(iom).alpha_op;

        OM(iom).state.Sxx11mo =  alpha_mo * OM(iom).state.Sxx11mo_inn  + (1-alpha_mo) * OM(iom).state.Sxx11mo_out;
        OM(iom).state.Sxx22mo =  alpha_mo * OM(iom).state.Sxx22mo_inn  + (1-alpha_mo) * OM(iom).state.Sxx22mo_out;

        OM(iom).state.Sxx11po =  alpha_po * OM(iom).state.Sxx11po_inn  + (1-alpha_po) * OM(iom).state.Sxx11po_out; 
        OM(iom).state.Sxx22po =  alpha_po * OM(iom).state.Sxx22po_inn  + (1-alpha_po) * OM(iom).state.Sxx22po_out; 
        
        OM(iom).state.Szz11om =  alpha_om * OM(iom).state.Szz11om_inn  + (1-alpha_om) * OM(iom).state.Szz11om_out; 
        OM(iom).state.Szz22om =  alpha_om * OM(iom).state.Szz22om_inn  + (1-alpha_om) * OM(iom).state.Szz22om_out; 
        
        OM(iom).state.Szz11op =  alpha_op * OM(iom).state.Szz11op_inn  + (1-alpha_op) * OM(iom).state.Szz11op_out; 
        OM(iom).state.Szz22op =  alpha_op * OM(iom).state.Szz22op_inn  + (1-alpha_op) * OM(iom).state.Szz22op_out; 
    end

    
    end


  
    
    
   