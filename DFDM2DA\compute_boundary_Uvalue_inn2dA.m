function [OM] = compute_boundary_Uvalue_inn2dA(OM)
% to obtain the boundary values inside the element.
ndomain = length(OM);
for iom = 1:ndomain

    invLxT11 = OM(iom).invLxT11;
    invLzT11 = OM(iom).invLzT11;
    invLxT22 = OM(iom).invLxT22;
    invLzT22 = OM(iom).invLzT22;

    U21 = OM(iom).state.U21;
    U12 = OM(iom).state.U12;

    [U21mo,U21po,U21om,U21op] = compute_boundary_Uvalue_inn_elem(invLxT22,invLzT11,U21);
    [U12mo,U12po,U12om,U12op] = compute_boundary_Uvalue_inn_elem(invLxT11,invLzT22,U12);

    % 心无旁骛，万事可破
    OM(iom).state.U12mo_inn = U12mo;
    OM(iom).state.U12po_inn = U12po;
    OM(iom).state.U12om_inn = U12om;
    OM(iom).state.U12op_inn = U12op;

    OM(iom).state.U21mo_inn = U21mo;
    OM(iom).state.U21po_inn = U21po;
    OM(iom).state.U21om_inn = U21om;
    OM(iom).state.U21op_inn = U21op;

end


end

function [U12mo,U12po,U12om,U12op] = compute_boundary_Uvalue_inn_elem(invLxT11,invLzT22,U12)
% the input U12 is in orth bspline bases in both two directions.
    tmp     = invLxT11*U12;
    U12mo   = tmp(1,:);
    U12po   = tmp(end,:);

    tmp     = U12';
    tmp     = invLzT22*tmp;
    tmp     = tmp';
    U12om = tmp(:,1);
    U12op = tmp(:,end);
end


% function [Umo,Upo,Uom,Uop] = compute_boundary_value_inn_elem(invLxT,invLzT,U)
% % x direction
% Umid0 = pagemtimes(invLxT,U);
% Umid1 = invLzT*Umid0';
% Umid1 = Umid1';
% 
% Umo  = Umid1(1,:);    % left    
% Upo  = Umid1(end,:);  % right
% Uom  = Umid1(:,1);    % bottom
% Uop  = Umid1(:,end);  % upper
% 
% Umo  = reshape(Umo,length(Umo),1);  % left    
% Upo  = reshape(Upo,length(Upo),1);  % right
% Uom  = reshape(Uom,length(Uom),1);  % bottom
% Uop  = reshape(Uop,length(Uop),1);  % upper
% 
% 
% 
% % Very important, to be checked.
% % there is another way only take boundary values to participate the
% % calculation, after multiplication with invLxT
% % which is very important when the element have a lot of grids.
% end

