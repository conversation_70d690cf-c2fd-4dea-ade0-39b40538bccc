function [OM] = compute_boundary_Uvalue_out2dA(OM)
 

for iom = 1:length(OM)

    iNbr_mo = OM(iom).iNbr_mo;
    iNbr_po = OM(iom).iNbr_po;
    iNbr_om = OM(iom).iNbr_om;
    iNbr_op = OM(iom).iNbr_op;

    invLx11_iom  = OM(iom).invLx11;
    invLx22_iom  = OM(iom).invLx22;
    invLz11_iom  = OM(iom).invLz11;
    invLz22_iom  = OM(iom).invLz22;

    if iNbr_mo~=0

        jFace = OM(iom).iFace_mo;
        %
        U12mo_inn = OM(iNbr_mo).state.U12mo_inn;
        U12po_inn = OM(iNbr_mo).state.U12po_inn;
        U12om_inn = OM(iNbr_mo).state.U12om_inn;
        U12op_inn = OM(iNbr_mo).state.U12op_inn;
        %
        U21mo_inn = OM(iNbr_mo).state.U21mo_inn;
        U21po_inn = OM(iNbr_mo).state.U21po_inn;
        U21om_inn = OM(iNbr_mo).state.U21om_inn;
        U21op_inn = OM(iNbr_mo).state.U21op_inn;
        %
        U12mo_out_tmp = select_face(U12mo_inn, U12po_inn, U12om_inn, U12op_inn, jFace);  
        U21mo_out_tmp = select_face(U21mo_inn, U21po_inn, U21om_inn, U21op_inn, jFace); 
        %
        if abs(jFace)==1 || abs(jFace)==2
            % Filt_1D_x(qx2m,FFilt_1D_y(L2y,Dy220mo,ux12mo_ext)), to be checked where the L2y is used
            % 如果传递的是 bspline 的值，Dz220mo就是单位矩阵
            invLzT11 = OM(iNbr_mo).invLzT11;
            invLzT22 = OM(iNbr_mo).invLzT22;
            U12mo_out = invLz22_iom*OM(iom).Dzz220mo*(invLzT22*U12mo_out_tmp');   
            U21mo_out = invLz11_iom*OM(iom).Dzz110mo*(invLzT11*U21mo_out_tmp');
        else
            invLxT11 = OM(iNbr_mo).invLxT11;
            invLxT22 = OM(iNbr_mo).invLxT22;
            % size(U12mo_out_tmp)
            % size(invLxT11)
            % size(OM(iom).Dz210mo)
            U12mo_out = invLz22_iom*OM(iom).Dzx210mo*(invLxT11*U12mo_out_tmp);
            U21mo_out = invLz11_iom*OM(iom).Dzx120mo*(invLxT22*U21mo_out_tmp);
        end

        OM(iom).state.U12mo_out = reshape(U12mo_out,1,length(U12mo_out));
        OM(iom).state.U21mo_out = reshape(U21mo_out,1,length(U21mo_out));

    end


 
    if iNbr_po~=0

        jFace = OM(iom).iFace_po;
        
        %
        U12mo_inn = OM(iNbr_po).state.U12mo_inn;
        U12po_inn = OM(iNbr_po).state.U12po_inn;
        U12om_inn = OM(iNbr_po).state.U12om_inn;
        U12op_inn = OM(iNbr_po).state.U12op_inn;
        %
        U21mo_inn = OM(iNbr_po).state.U21mo_inn;
        U21po_inn = OM(iNbr_po).state.U21po_inn;
        U21om_inn = OM(iNbr_po).state.U21om_inn;
        U21op_inn = OM(iNbr_po).state.U21op_inn;
        %
        U12po_out_tmp = select_face(U12mo_inn, U12po_inn, U12om_inn, U12op_inn, jFace);  
        U21po_out_tmp = select_face(U21mo_inn, U21po_inn, U21om_inn, U21op_inn, jFace); 
        %
        if abs(jFace)==1 || abs(jFace)==2
            invLzT11 = OM(iNbr_po).invLzT11;
            invLzT22 = OM(iNbr_po).invLzT22;
            U12po_out = invLz22_iom*OM(iom).Dzz220po*(invLzT22*U12po_out_tmp');
            U21po_out = invLz11_iom*OM(iom).Dzz110po*(invLzT11*U21po_out_tmp');
        else
            invLxT11 = OM(iNbr_po).invLxT11;
            invLxT22 = OM(iNbr_po).invLxT22;
            U12po_out = invLz22_iom*OM(iom).Dzx210po*(invLxT11*U12po_out_tmp);
            U21po_out = invLz11_iom*OM(iom).Dzx120po*(invLxT22*U21po_out_tmp);
        end

        OM(iom).state.U12po_out = reshape(U12po_out,1,length(U12po_out));
        OM(iom).state.U21po_out = reshape(U21po_out,1,length(U21po_out));
    end
    
    
    if iNbr_om~=0

        jFace = OM(iom).iFace_om;
        %
        U12mo_inn = OM(iNbr_om).state.U12mo_inn;
        U12po_inn = OM(iNbr_om).state.U12po_inn;
        U12om_inn = OM(iNbr_om).state.U12om_inn;
        U12op_inn = OM(iNbr_om).state.U12op_inn;
        %
        U21mo_inn = OM(iNbr_om).state.U21mo_inn;
        U21po_inn = OM(iNbr_om).state.U21po_inn;
        U21om_inn = OM(iNbr_om).state.U21om_inn;
        U21op_inn = OM(iNbr_om).state.U21op_inn;
        %
        U12om_out_tmp = select_face(U12mo_inn, U12po_inn, U12om_inn, U12op_inn, jFace);  
        U21om_out_tmp = select_face(U21mo_inn, U21po_inn, U21om_inn, U21op_inn, jFace); 
        %
        if abs(jFace)==3 || abs(jFace)==4
            invLxT11 = OM(iNbr_om).invLxT11;
            invLxT22 = OM(iNbr_om).invLxT22;
            U12om_out = invLx11_iom*OM(iom).Dxx110om*(invLxT11*U12om_out_tmp);
            U21om_out = invLx22_iom*OM(iom).Dxx220om*(invLxT22*U21om_out_tmp);
        else
            invLzT11 = OM(iNbr_om).invLzT11;
            invLzT22 = OM(iNbr_om).invLzT22;
            % size(U12mo_out_tmp)
            % size(invLxT11)
            % size(OM(iom).Dx210mo)
            U12om_out = invLx11_iom*OM(iom).Dxz120om*(invLzT22*U12om_out_tmp');
            U21om_out = invLx22_iom*OM(iom).Dxz210om*(invLzT11*U21om_out_tmp');
        end
        %
        OM(iom).state.U12om_out = reshape(U12om_out,length(U12om_out),1);
        OM(iom).state.U21om_out = reshape(U21om_out,length(U21om_out),1);
    end



    if iNbr_op~=0

        jFace = OM(iom).iFace_op;
        %
        U12mo_inn = OM(iNbr_op).state.U12mo_inn;
        U12po_inn = OM(iNbr_op).state.U12po_inn;
        U12om_inn = OM(iNbr_op).state.U12om_inn;
        U12op_inn = OM(iNbr_op).state.U12op_inn;
        %
        U21mo_inn = OM(iNbr_op).state.U21mo_inn;
        U21po_inn = OM(iNbr_op).state.U21po_inn;
        U21om_inn = OM(iNbr_op).state.U21om_inn;
        U21op_inn = OM(iNbr_op).state.U21op_inn;
        %
        U12op_out_tmp = select_face(U12mo_inn, U12po_inn, U12om_inn, U12op_inn, jFace);  
        U21op_out_tmp = select_face(U21mo_inn, U21po_inn, U21om_inn, U21op_inn, jFace); 
        %
        if abs(jFace)==3 || abs(jFace)==4
            invLxT11 = OM(iNbr_op).invLxT11;
            invLxT22 = OM(iNbr_op).invLxT22;
            U12op_out = invLx11_iom*OM(iom).Dxx110op*(invLxT11*U12op_out_tmp);
            U21op_out = invLx22_iom*OM(iom).Dxx220op*(invLxT22*U21op_out_tmp);
        else
            invLzT11 = OM(iNbr_op).invLzT11;
            invLzT22 = OM(iNbr_op).invLzT22;
            U12op_out = invLx11_iom*OM(iom).Dxz120op*(invLzT22*U12op_out_tmp');
            U21op_out = invLx22_iom*OM(iom).Dxz210op*(invLzT11*U21op_out_tmp');
        end
        %
        OM(iom).state.U12op_out = reshape(U12op_out,length(U12op_out),1);
        OM(iom).state.U21op_out = reshape(U21op_out,length(U21op_out),1);

    end
   
end

for iom = 1:length(OM)

    alpha_mo = OM(iom).alpha_mo;
    alpha_po = OM(iom).alpha_po;
    alpha_om = OM(iom).alpha_om;
    alpha_op = OM(iom).alpha_op;

    OM(iom).state.U12mo = (1-alpha_mo) * OM(iom).state.U12mo_inn + alpha_mo * OM(iom).state.U12mo_out; 
    OM(iom).state.U21mo = (1-alpha_mo) * OM(iom).state.U21mo_inn + alpha_mo * OM(iom).state.U21mo_out;
    OM(iom).state.U12po = (1-alpha_po) * OM(iom).state.U12po_inn + alpha_po * OM(iom).state.U12po_out; 
    OM(iom).state.U21po = (1-alpha_po) * OM(iom).state.U21po_inn + alpha_po * OM(iom).state.U21po_out;
    
    OM(iom).state.U12om = (1-alpha_om) * OM(iom).state.U12om_inn + alpha_om * OM(iom).state.U12om_out; 
    OM(iom).state.U21om = (1-alpha_om) * OM(iom).state.U21om_inn + alpha_om * OM(iom).state.U21om_out;
    OM(iom).state.U12op = (1-alpha_op) * OM(iom).state.U12op_inn + alpha_op * OM(iom).state.U12op_out; 
    OM(iom).state.U21op = (1-alpha_op) * OM(iom).state.U21op_inn + alpha_op * OM(iom).state.U21op_out;

end

end


