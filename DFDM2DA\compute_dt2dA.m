function [dt,nt] = compute_dt2dA(OM,duration)
    dt  = 9999999;
    CFL_condition = 0.1;
    for iom = 1:length(OM)
        Nx1   = OM(iom).Nx1;
        Nz1   = OM(iom).Nz1;
        ix = ceil((Nx1+1)/2);
        iz = ceil((Nz1+1)/2);
        x1   = OM(iom).x2d11(ix,iz);
        z1   = OM(iom).z2d11(ix,iz);

        x2   = OM(iom).x2d11(ix+1,iz+1);
        z2   = OM(iom).z2d11(ix+1,iz+1);
        
        dxz = sqrt((x2-x1)^2 + (z2-z1)^2 );
        
        Vmax = 5000;
        
        tmp  = CFL_condition*dxz/Vmax; 
        dt = min(dt,tmp);
    end
    dt = 0.1;
    % dt = 0.15;
    nt = ceil(duration/dt);
    fprintf("Time step and number of time steps are: %d, %d,...\n",dt,nt)
end

