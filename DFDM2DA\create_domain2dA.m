function domain2dA = create_domain2dA()

struct_model2dA = struct('nx', [], ...
                         'nz', [], ...
                         'xa', [], ...
                         'za', [], ...
                         'xp', [], ...
                         'zp', [], ...
                         'rho',[], ...
                         'mu', []);

struct_basis = struct('nb'   ,[], ...
                      'pb'   ,[], ...
                      'xmin' ,[], ...
                      'xmax' ,[], ...
                      'x'    ,[], ...
                      'k'    ,[], ...
                      'mat'  ,[]);


struct_state = struct('dU2dtt12',     [], ...
                      'dU2dtt21',     [], ...
                      'U12_0',       [], ...   
                      'U12_1',       [], ...   
                      'U12',         [], ...
                      'U21_0',       [], ...
                      'U21_1',       [], ...   
                      'U21',         [], ...         
                      'U12mo',       [], ...
                      'U12po',       [], ...
                      'U12om',       [], ...
                      'U12op',       [], ...
                      'U12mo_inn',   [], ...
                      'U12po_inn',   [], ...
                      'U12om_inn',   [], ...
                      'U12op_inn',   [], ...
                      'U12mo_out',   [], ...
                      'U12po_out',   [], ...
                      'U12om_out',   [], ...
                      'U12op_out',   [], ...
                      'U21mo',       [], ...
                      'U21po',       [], ...
                      'U21om',       [], ...
                      'U21op',       [], ...
                      'U21mo_inn',   [], ...
                      'U21po_inn',   [], ...
                      'U21om_inn',   [], ...
                      'U21op_inn',   [], ...
                      'U21mo_out',   [], ...
                      'U21po_out',   [], ...
                      'U21om_out',   [], ...
                      'U21op_out',   [], ...
                      'Sxx11',       [], ...
                      'Sxx11mo',     [], ...
                      'Sxx11po',     [], ...
                      'Sxx11mo_inn', [], ...
                      'Sxx11po_inn', [], ...
                      'Sxx11mo_out', [], ...
                      'Sxx11po_out', [], ...
                      'Sxx22',       [], ...
                      'Sxx22mo',     [], ...
                      'Sxx22po',     [], ...
                      'Sxx22mo_inn', [], ...
                      'Sxx22po_inn', [], ...
                      'Sxx22mo_out', [], ...
                      'Sxx22po_out', [], ...
                      'Szz11',       [], ...
                      'Szz11om',     [], ...
                      'Szz11op',     [], ...
                      'Szz11om_inn', [], ...
                      'Szz11op_inn', [], ...
                      'Szz11om_out', [], ...
                      'Szz11op_out', [], ...
                      'Szz22',       [], ...
                      'Szz22om',     [], ...
                      'Szz22op',     [], ...
                      'Szz22om_inn', [], ...
                      'Szz22op_inn', [], ...
                      'Szz22om_out', [], ...
                      'Szz22op_out', [], ...
                      'Umid',        []);

domain2dA = struct('model2dA'  ,struct_model2dA, ...          
                   'state'     ,struct_state, ...
                    'Bx2'      ,struct_basis,...
                    'Bx1'      ,struct_basis,...
                    'Bz1'      ,struct_basis,...
                    'Bz2'      ,struct_basis,...
                    'px1'      ,[], ...
                    'pz1'      ,[], ...
                    'Nx1'      ,[], ...
                    'Nz1'      ,[], ...
                    'x2d11'    ,[], ...
                    'z2d11'    ,[], ...
                    'bxT1'     ,[], ...
                    'bxT2'     ,[], ...
                    'bzT1'     ,[], ...
                    'bzT2'     ,[], ...
                    'kkx12'    ,[], ...
                    'kkz12'    ,[], ...
                    'kkx21'    ,[], ...
                    'kkz21'    ,[], ...
                    'invLx11'  ,[], ...
                    'invLz11'  ,[], ...
                    'invLx22'  ,[], ...
                    'invLz22'  ,[], ...
                    'invLxT11' ,[], ...
                    'invLzT11' ,[], ...
                    'invLxT22' ,[], ...
                    'invLzT22' ,[], ...
                    'alpha_mo' ,[], ...
                    'alpha_po' ,[], ...
                    'alpha_om' ,[], ...
                    'alpha_op' ,[], ...
                    'iNbr_mo'  ,[], ...
                    'iNbr_po'  ,[], ...
                    'iNbr_om'  ,[], ...
                    'iNbr_op'  ,[], ...
                    'iFace_mo' ,[], ...
                    'iFace_po' ,[], ...
                    'iFace_om' ,[], ...
                    'iFace_op' ,[], ...
                    'rot_mo'   ,[], ...
                    'rot_po'   ,[], ...
                    'rot_om'   ,[], ...
                    'rot_op'   ,[], ...
                    'dxpdx11'  ,[], ...
                    'dzpdx11'  ,[], ...
                    'dxpdz11'  ,[], ...
                    'dzpdz11'  ,[], ...
                    'dxpdx12'  ,[], ...
                    'dzpdx12'  ,[], ...
                    'dxpdz12'  ,[], ...
                    'dzpdz12'  ,[], ...
                    'dxpdx21'  ,[], ...
                    'dzpdx21'  ,[], ...
                    'dxpdz21'  ,[], ...
                    'dzpdz21'  ,[], ...
                    'dxpdx22'  ,[], ...
                    'dzpdx22'  ,[], ...
                    'dxpdz22'  ,[], ...
                    'dzpdz22'  ,[], ...
                    'Jac11'    ,[], ...
                    'Jac22'    ,[], ...
                    'Jac12'    ,[], ...
                    'Jac21'    ,[], ...
                    'rho12'    ,[], ...
                    'rho21'    ,[], ...
                    'mu11'     ,[], ...
                    'mu22'     ,[]);

end

