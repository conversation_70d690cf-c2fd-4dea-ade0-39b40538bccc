%% 提取对比数据脚本
% 从当前工作区提取数据并保存，用于与C++对比

fprintf('=== 提取 MATLAB 对比数据 ===\n');

% 检查工作区中是否有 OM 数据
if ~exist('OM', 'var')
    error('工作区中没有 OM 数据，请先运行 main2dA');
end

% 创建输出目录
output_dir = 'output/matlab_vs_cpp';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% 选择第一个域进行对比
domain = OM(1);

fprintf('域 0 信息:\n');
fprintf('  Nx1 = %d, Nz1 = %d\n', domain.Nx1, domain.Nz1);
fprintf('  Nx2 = %d, Nz2 = %d\n', domain.Nx1-1, domain.Nz1-1);
fprintf('  px1 = %d, pz1 = %d\n', domain.px1, domain.pz1);

%% 1. 保存关键参数
fprintf('\n1. 保存参数文件...\n');
params_file = fullfile(output_dir, 'matlab_parameters.txt');
fid = fopen(params_file, 'w');
fprintf(fid, '# MATLAB Domain Parameters\n');
fprintf(fid, 'iom = 0\n');
fprintf(fid, 'region = 2\n');  % 默认值
fprintf(fid, 'Nx1 = %d\n', domain.Nx1);
fprintf(fid, 'Nz1 = %d\n', domain.Nz1);
fprintf(fid, 'Nx2 = %d\n', domain.Nx1 - 1);
fprintf(fid, 'Nz2 = %d\n', domain.Nz1 - 1);
fprintf(fid, 'px1 = %d\n', domain.px1);
fprintf(fid, 'pz1 = %d\n', domain.pz1);

% 计算域边界
if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
    x_min = min(domain.x2d11(:));
    x_max = max(domain.x2d11(:));
    z_min = min(domain.z2d11(:));
    z_max = max(domain.z2d11(:));
    fprintf('  域边界: x=[%.3f, %.3f] km, z=[%.3f, %.3f] km\n', ...
            x_min/1000, x_max/1000, z_min/1000, z_max/1000);
else
    x_min = 0; x_max = 0; z_min = 0; z_max = 0;
    fprintf('  警告: 没有找到坐标数据\n');
end

fprintf(fid, 'x_min = %.16e\n', x_min);
fprintf(fid, 'x_max = %.16e\n', x_max);
fprintf(fid, 'z_min = %.16e\n', z_min);
fprintf(fid, 'z_max = %.16e\n', z_max);

% 材料参数
if isfield(domain, 'mu11') && ~isempty(domain.mu11)
    mu_val = domain.mu11(1,1);
    fprintf(fid, 'mu = %.16e\n', mu_val);
    fprintf('  mu = %.2e\n', mu_val);
else
    fprintf(fid, 'mu = 0.0\n');
    fprintf('  警告: 没有找到 mu 数据\n');
end

if isfield(domain, 'rho11') && ~isempty(domain.rho11)
    rho_val = domain.rho11(1,1);
    fprintf(fid, 'rho = %.16e\n', rho_val);
    fprintf('  rho = %.2e\n', rho_val);
else
    fprintf(fid, 'rho = 0.0\n');
    fprintf('  警告: 没有找到 rho 数据\n');
end

fclose(fid);
fprintf('  参数文件已保存: %s\n', params_file);

%% 2. 保存坐标数据
fprintf('\n2. 保存坐标数据...\n');
if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
    csvwrite(fullfile(output_dir, 'matlab_x2d11.csv'), domain.x2d11);
    csvwrite(fullfile(output_dir, 'matlab_z2d11.csv'), domain.z2d11);
    fprintf('  x2d11, z2d11: %dx%d\n', size(domain.x2d11));
else
    fprintf('  警告: x2d11, z2d11 不存在\n');
end

if isfield(domain, 'x2d22') && ~isempty(domain.x2d22)
    csvwrite(fullfile(output_dir, 'matlab_x2d22.csv'), domain.x2d22);
    csvwrite(fullfile(output_dir, 'matlab_z2d22.csv'), domain.z2d22);
    fprintf('  x2d22, z2d22: %dx%d\n', size(domain.x2d22));
else
    fprintf('  x2d22, z2d22 不存在\n');
end

%% 3. 保存基函数矩阵
fprintf('\n3. 保存基函数矩阵...\n');
basis_fields = {'bxT1', 'bxT2', 'bzT1', 'bzT2'};
for i = 1:length(basis_fields)
    field = basis_fields{i};
    if isfield(domain, field) && ~isempty(domain.(field))
        filename = sprintf('matlab_%s.csv', field);
        csvwrite(fullfile(output_dir, filename), domain.(field));
        fprintf('  %s: %dx%d\n', field, size(domain.(field)));
    else
        fprintf('  %s: 不存在\n', field);
    end
end

%% 4. 保存坐标变换矩阵
fprintf('\n4. 保存坐标变换矩阵...\n');
jacobian_fields = {'dxpdx11', 'dxpdx22', 'dxpdz11', 'dxpdz22', ...
                   'dzpdx11', 'dzpdx22', 'dzpdz11', 'dzpdz22'};

for i = 1:length(jacobian_fields)
    field = jacobian_fields{i};
    if isfield(domain, field) && ~isempty(domain.(field))
        filename = sprintf('matlab_%s.csv', field);
        csvwrite(fullfile(output_dir, filename), domain.(field));
        matrix = domain.(field);
        fprintf('  %s: %dx%d, 值范围 [%.6e, %.6e]\n', ...
                field, size(matrix), min(matrix(:)), max(matrix(:)));
        
        % 检查特殊情况
        if all(matrix(:) == 1.0)
            fprintf('    警告: 全部为 1.0\n');
        elseif all(matrix(:) == 0.0)
            fprintf('    警告: 全部为 0.0\n');
        end
    else
        fprintf('  %s: 不存在\n', field);
    end
end

%% 5. 保存域结构体字段信息
fprintf('\n5. 保存调试信息...\n');
debug_file = fullfile(output_dir, 'matlab_debug_info.txt');
fid = fopen(debug_file, 'w');
fprintf(fid, '# MATLAB 域结构体调试信息\n');
fprintf(fid, '# 生成时间: %s\n\n', datestr(now));

fields = fieldnames(domain);
fprintf(fid, '## 域结构体字段列表 (共 %d 个字段):\n', length(fields));

% 同时在命令窗口显示字段信息
fprintf('  MATLAB 域结构体字段:\n');

for i = 1:length(fields)
    field = fields{i};
    if isnumeric(domain.(field)) || islogical(domain.(field))
        if isempty(domain.(field))
            fprintf(fid, '%s: 空矩阵\n', field);
            fprintf('    %s: 空矩阵\n', field);
        else
            fprintf(fid, '%s: %s, 大小: %s', field, class(domain.(field)), mat2str(size(domain.(field))));
            fprintf('    %s: %s, 大小: %s', field, class(domain.(field)), mat2str(size(domain.(field))));
            if numel(domain.(field)) <= 4
                fprintf(fid, ', 值: %s\n', mat2str(domain.(field)));
                fprintf(', 值: %s\n', mat2str(domain.(field)));
            else
                fprintf(fid, ', 值范围: [%.6e, %.6e]\n', min(domain.(field)(:)), max(domain.(field)(:)));
                fprintf(', 值范围: [%.6e, %.6e]\n', min(domain.(field)(:)), max(domain.(field)(:)));
            end
        end
    else
        fprintf(fid, '%s: %s\n', field, class(domain.(field)));
        fprintf('    %s: %s\n', field, class(domain.(field)));
    end
end

% 检查关键字段是否存在
fprintf('\n  关键字段检查:\n');
key_fields = {'Nx2', 'Nz2', 'x2d22', 'z2d22', 'region'};
for i = 1:length(key_fields)
    field = key_fields{i};
    if isfield(domain, field)
        fprintf('    %s: 存在\n', field);
        fprintf(fid, '\n## 关键字段 %s: 存在\n', field);
    else
        fprintf('    %s: 不存在 ❌\n', field);
        fprintf(fid, '\n## 关键字段 %s: 不存在\n', field);
    end
end

fclose(fid);
fprintf('  调试信息已保存: %s\n', debug_file);

%% 6. 读取并对比 C++ 数据
fprintf('\n=== 读取 C++ 数据进行对比 ===\n');

% C++ 数据路径
cpp_dir = 'D:\project\ware\output_cpp\domain_0';
cpp_params_file = fullfile(cpp_dir, 'parameters.txt');

% 读取 C++ 参数
cpp_params = struct();
if exist(cpp_params_file, 'file')
    fprintf('读取 C++ 参数文件...\n');
    fid = fopen(cpp_params_file, 'r');
    while ~feof(fid)
        line = fgetl(fid);
        if ischar(line) && contains(line, '=') && ~startsWith(line, '#')
            parts = split(line, '=');
            if length(parts) == 2
                key = strtrim(parts{1});
                value = str2double(strtrim(parts{2}));
                if ~isnan(value)
                    cpp_params.(key) = value;
                end
            end
        end
    end
    fclose(fid);

    % 显示参数对比
    fprintf('\n=== 参数对比 ===\n');
    fprintf('  Nx1: MATLAB=%d, C++=%d %s\n', domain.Nx1, cpp_params.Nx1, ...
            iif(domain.Nx1 == cpp_params.Nx1, '✅', '❌'));
    fprintf('  Nz1: MATLAB=%d, C++=%d %s\n', domain.Nz1, cpp_params.Nz1, ...
            iif(domain.Nz1 == cpp_params.Nz1, '✅', '❌'));
    fprintf('  Nx2: MATLAB=%d, C++=%d %s\n', domain.Nx1-1, cpp_params.Nx2, ...
            iif(domain.Nx1-1 == cpp_params.Nx2, '✅', '❌'));
    fprintf('  Nz2: MATLAB=%d, C++=%d %s\n', domain.Nz1-1, cpp_params.Nz2, ...
            iif(domain.Nz1-1 == cpp_params.Nz2, '✅', '❌'));

    % 域边界对比
    fprintf('\n=== 域边界对比 ===\n');
    fprintf('  x_min: MATLAB=%.3e, C++=%.3e, 差异=%.1f%%\n', ...
            x_min, cpp_params.x_min, abs(x_min-cpp_params.x_min)/abs(x_min)*100);
    fprintf('  x_max: MATLAB=%.3e, C++=%.3e, 差异=%.1f%%\n', ...
            x_max, cpp_params.x_max, abs(x_max-cpp_params.x_max)/abs(x_max)*100);
    fprintf('  z_min: MATLAB=%.3e, C++=%.3e, 差异=%.1f%%\n', ...
            z_min, cpp_params.z_min, abs(z_min-cpp_params.z_min)/abs(z_min)*100);
    fprintf('  z_max: MATLAB=%.3e, C++=%.3e, 差异=%.1f%%\n', ...
            z_max, cpp_params.z_max, abs(z_max-cpp_params.z_max)/abs(z_max)*100);
else
    fprintf('❌ 未找到 C++ 参数文件: %s\n', cpp_params_file);
end

% 对比雅可比矩阵
compare_jacobian_matrices(domain, cpp_dir, output_dir);

fprintf('\n✅ 所有对比数据已保存到: %s\n', output_dir);
fprintf('=== 提取完成 ===\n');

% 辅助函数
function result = iif(condition, true_val, false_val)
    if condition
        result = true_val;
    else
        result = false_val;
    end
end

function compare_jacobian_matrices(domain, cpp_dir, output_dir)
% 对比 MATLAB 和 C++ 的雅可比矩阵

    fprintf('\n=== 雅可比矩阵对比 ===\n');

    % 要对比的雅可比矩阵列表
    jacobian_fields = {'dxpdx11', 'dxpdx22', 'dxpdz11', 'dxpdz22', ...
                       'dzpdx11', 'dzpdx22', 'dzpdz11', 'dzpdz22'};

    comparison_results = struct();

    for i = 1:length(jacobian_fields)
        field = jacobian_fields{i};

        % 检查 MATLAB 数据
        if isfield(domain, field) && ~isempty(domain.(field))
            matlab_matrix = domain.(field);

            % 读取 C++ 数据
            cpp_file = fullfile(cpp_dir, [field '.txt']);
            if exist(cpp_file, 'file')
                try
                    cpp_matrix = load(cpp_file);

                    % 检查维度
                    if isequal(size(matlab_matrix), size(cpp_matrix))
                        % 计算差异
                        diff_matrix = matlab_matrix - cpp_matrix;
                        max_abs_diff = max(abs(diff_matrix(:)));
                        rel_diff = max_abs_diff / max(abs(matlab_matrix(:))) * 100;

                        % 计算统计信息
                        matlab_range = [min(matlab_matrix(:)), max(matlab_matrix(:))];
                        cpp_range = [min(cpp_matrix(:)), max(cpp_matrix(:))];

                        fprintf('  %s: %dx%d\n', field, size(matlab_matrix));
                        fprintf('    MATLAB 范围: [%.6e, %.6e]\n', matlab_range);
                        fprintf('    C++    范围: [%.6e, %.6e]\n', cpp_range);
                        fprintf('    最大绝对差异: %.6e\n', max_abs_diff);
                        fprintf('    最大相对差异: %.2f%%\n', rel_diff);

                        % 保存差异矩阵
                        diff_file = fullfile(output_dir, ['diff_' field '.csv']);
                        csvwrite(diff_file, diff_matrix);

                        % 保存对比结果
                        comparison_results.(field) = struct(...
                            'matlab_range', matlab_range, ...
                            'cpp_range', cpp_range, ...
                            'max_abs_diff', max_abs_diff, ...
                            'max_rel_diff', rel_diff, ...
                            'dimensions_match', true);

                        if rel_diff < 1.0
                            fprintf('    状态: ✅ 差异很小\n');
                        elseif rel_diff < 5.0
                            fprintf('    状态: ⚠️ 差异中等\n');
                        else
                            fprintf('    状态: ❌ 差异较大\n');
                        end

                    else
                        fprintf('  %s: ❌ 维度不匹配\n', field);
                        fprintf('    MATLAB: %dx%d, C++: %dx%d\n', ...
                                size(matlab_matrix), size(cpp_matrix));

                        comparison_results.(field) = struct(...
                            'dimensions_match', false, ...
                            'matlab_size', size(matlab_matrix), ...
                            'cpp_size', size(cpp_matrix));
                    end

                catch ME
                    fprintf('  %s: ❌ 读取 C++ 文件失败: %s\n', field, ME.message);
                end
            else
                fprintf('  %s: ❌ C++ 文件不存在: %s\n', field, cpp_file);
            end
        else
            fprintf('  %s: ❌ MATLAB 数据不存在\n', field);
        end
        fprintf('\n');
    end

    % 保存对比结果
    results_file = fullfile(output_dir, 'jacobian_comparison_results.mat');
    save(results_file, 'comparison_results');
    fprintf('  对比结果已保存: %s\n', results_file);
end
