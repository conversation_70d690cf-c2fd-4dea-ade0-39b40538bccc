function OM=find_connections2dA(OM)
    ndomain = length(OM);
    xtmp = zeros(4,ndomain);
    ztmp = zeros(4,ndomain);

    
    for iom = 1:ndomain
        xa = OM(iom).model2dA.xa;
        za = OM(iom).model2dA.za;
        [nx,nz] = size(xa);    
        xtmp(1,iom)= xa(1,1);
        xtmp(2,iom)= xa(nx,1);
        xtmp(3,iom)= xa(1,nz);
        xtmp(4,iom)= xa(nx,nz);
    
        ztmp(1,iom)= za(1,1);
        ztmp(2,iom)= za(nx,1);
        ztmp(3,iom)= za(1,nz);
        ztmp(4,iom)= za(nx,nz);
    
    end

    % find neighboring domains
    % ndomain_nbrs
    % 3  4
    %
    % 1  2
    ndomain_iNbrs = zeros(4,ndomain);
    for idm = 1:ndomain
        fprintf('...find the neighbor idomain of the %d domain...\n',idm);
        for iFace=1:4
            if ndomain_iNbrs(iFace,idm)==0
                switch iFace
                    case 1
                        pt1 = 1;
                        pt2 = 3;
                    case 2
                        pt1 = 2;
                        pt2 = 4;
                    case 3
                        pt1 = 1;
                        pt2 = 2;
                    case 4
                        pt1 = 3;
                        pt2 = 4;
                end

                xmF = xtmp(pt1,idm);  xpF = xtmp(pt2,idm);
                zmF = ztmp(pt1,idm);  zpF = ztmp(pt2,idm);

                xm_mid = (xmF+xpF)/2;
                zm_mid = (zmF+zpF)/2;

                for jdm = 1:ndomain
                    if jdm~=idm
                        for jFace=1:4

                            switch jFace
                                case 1
                                    pt1 = 1;
                                    pt2 = 3;
                                case 2
                                    pt1 = 2;
                                    pt2 = 4;
                                case 3
                                    pt1 = 1;
                                    pt2 = 2;
                                case 4
                                    pt1 = 3;
                                    pt2 = 4;
                            end

                            xmFNbr = xtmp(pt1,jdm); xpFNbr = xtmp(pt2,jdm);
                            zmFNbr = ztmp(pt1,jdm); zpFNbr = ztmp(pt2,jdm);

                            xmNbr_mid = (xmFNbr+xpFNbr)/2;
                            zmNbr_mid = (zmFNbr+zpFNbr)/2;

                            if abs(xm_mid-xmNbr_mid)+abs(zm_mid-zmNbr_mid) <50
                                ndomain_iNbrs(jFace,jdm)=idm;
                                ndomain_iNbrs(iFace,idm)=jdm;
                                % break
                            end

                        end
                    end
                end
            end

        end
        OM(idm).iNbr_mo  = ndomain_iNbrs(1,idm);
        OM(idm).iNbr_po  = ndomain_iNbrs(2,idm);
        OM(idm).iNbr_om  = ndomain_iNbrs(3,idm);
        OM(idm).iNbr_op  = ndomain_iNbrs(4,idm);
        % add iCPU_mo ***
    end

    ndomain_iFaces = zeros(4,ndomain);
    for idm = 1:ndomain
            % find the matched Face of the neighboring element with current element
        fprintf('...find the matched Face of the %d domain...\n',idm);
        for iFace=1:4
            dist = realmax;
            switch iFace

                case 1
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 1;
                    pt2  = 3;
                case 2
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 2;
                    pt2  = 4;
                case 3
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 1;
                    pt2  = 2;
                case 4
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 3;
                    pt2  = 4;
            end
            if iNbr~=0
            xmF = xtmp(pt1,idm); xpF = xtmp(pt2,idm);

                zmF = ztmp(pt1,idm); zpF = ztmp(pt2,idm);

                for jFace=1:4
                
                    switch jFace

                        case 1
                            pt1 = 1;
                            pt2 = 3;
                        case 2
                            pt1 = 2;
                            pt2 = 4;
                        case 3
                            pt1 = 1;
                            pt2 = 2;
                        case 4
                            pt1 = 3;
                            pt2 = 4;
                    end
                    xmFNbr = xtmp(pt1,iNbr); xpFNbr = xtmp(pt2,iNbr);
                    zmFNbr = ztmp(pt1,iNbr); zpFNbr = ztmp(pt2,iNbr);

                    dist1 = (xmF-xmFNbr)^2 + (xpF-xpFNbr)^2 + (zmF-zmFNbr)^2 +(zpF-zpFNbr)^2; 

                    if dist1 < dist
                        dist = dist1;
                        switch iFace
                        case 1
                            ndomain_iFaces(iFace,idm) = jFace;
                        case 2
                            ndomain_iFaces(iFace,idm) = jFace;
                        case 3
                            ndomain_iFaces(iFace,idm) = jFace;
                        case 4
                            ndomain_iFaces(iFace,idm) = jFace;
                        end  
                    end
                    dist1 = (xmF-xpFNbr)^2 + (xpF-xmFNbr)^2 + (zmF-zpFNbr)^2 +(zpF-zmFNbr)^2; 

                    if dist1 < dist
                        dist = dist1;
                        switch iFace
                        case 1
                            ndomain_iFaces(iFace,idm) = -jFace;
                        case 2
                            ndomain_iFaces(iFace,idm) = -jFace;
                        case 3
                            ndomain_iFaces(iFace,idm) = -jFace;
                        case 4
                            ndomain_iFaces(iFace,idm) = -jFace;
                        end  
                    end
                end
            end
        end
        OM(idm).iFace_mo = ndomain_iFaces(1,idm);
        OM(idm).iFace_po = ndomain_iFaces(2,idm);
        OM(idm).iFace_om = ndomain_iFaces(3,idm);
        OM(idm).iFace_op = ndomain_iFaces(4,idm);
    end

    % build the rotation matrix
    for idm =1:ndomain

        rot_mo = zeros(2,2);
        rot_po = zeros(2,2);
        rot_om = zeros(2,2);
        rot_op = zeros(2,2);
        for jFace=1:4
            switch jFace
                case 1
                    iNbr_mo  = ndomain_iNbrs (1,idm);
                    iFace_mo = ndomain_iFaces(1,idm);
                    
                    if iNbr_mo~=0
                        switch abs(iFace_mo)
                            case 1
                                % to be checked
                                rot_mo(1,1) = -1;
                                rot_mo(2,2) = 1;
                            case 2
                                % structral mesh
                                rot_mo(1,1) =  1; 
                                rot_mo(2,2) =  sign(iFace_mo);
                            case 3
                                % elements 1 and 2
                                rot_mo(1,2) = -1;
                                rot_mo(2,1) = -1;
                            case 4
                                % elements 5 and 2
                                rot_mo(1,2) =  1;
                                rot_mo(2,1) =  1;
                        end
                    end
                case 2
                    iNbr_po  = ndomain_iNbrs (2,idm);
                    iFace_po = ndomain_iFaces(2,idm);
                    
                    if iNbr_po~=0
                        switch abs(iFace_po)
                            case 1
                                % strutral mesh
                                rot_po(1,1) = 1; 
                                rot_po(2,2) = sign(iFace_po);
                            case 2
                                % to be checked
                                rot_po(1,1) = -1;
                                rot_po(2,2) = 1;
                            case 3
                                % elements 1 and 4
                                rot_po(1,2) = 1;
                                rot_po(2,1) = 1;
                            case 4
                                % elements 5 and 4
                                rot_po(1,2) = -1;
                                rot_po(2,1) = -1;
                        end
                    end
                case 3
                    iNbr_om  = ndomain_iNbrs (3,idm);
                    iFace_om = ndomain_iFaces(3,idm);
                    
                    if iNbr_om~=0
                        switch abs(iFace_om)
                            case 1
                                % elements 2 and 1
                                rot_om(2,1) = -1;
                                rot_om(1,2) = -1;
                            case 2
                                % elements 4 and 1
                                rot_om(2,1) = 1;
                                rot_om(1,2) = 1;
                            case 3
                                % to be checked
                                rot_om(2,2) = -1;
                                rot_om(1,1) = 1;
                            case 4
                                % strutral mesh
                                rot_om(2,2) = 1; 
                                rot_om(1,1) = sign(iFace_om);
                        end
                    end
                case 4
                    iNbr_op  = ndomain_iNbrs (4,idm);
                    iFace_op = ndomain_iFaces(4,idm);
                    
                    if iNbr_op~=0
                        switch abs(iFace_op)
                            case 1
                                % elements 2 and 5
                                rot_op(2,1) = 1;
                                rot_op(1,2) = 1;
                            case 2
                                % elements 4 and 5
                                rot_op(2,1) = -1;
                                rot_op(1,2) = -1;
                            case 3
                                % strutral mesh
                                rot_op(2,2) = 1; 
                                rot_op(1,1) = sign(iFace_op);
                            case 4
                                % to be checked
                                rot_op(2,2) = -1;
                                rot_op(1,1) = sign(iFace_op);
                        end
                    end
            end
        end
        OM(idm).rot_mo = rot_mo;
        OM(idm).rot_po = rot_po;
        OM(idm).rot_om = rot_om;
        OM(idm).rot_op = rot_op;
    end

end
