classdef CDS_matrix
    %CDS_MATRIX Summary of this class goes here
    %   Detailed explanation goes here

    properties
        nrow  % number of rows in the matrix
        val   % diagonals elements values
    end

    methods

        function obj = CDS_matrix(nrow,val)
         % CDS_MATRIX Construct an instance of this class
         % Detailed explanation goes here
           obj.nrow = nrow;
           obj.val = val;
        end
        
    end
end

