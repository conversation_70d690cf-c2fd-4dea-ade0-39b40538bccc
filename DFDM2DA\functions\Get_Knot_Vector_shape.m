function [t] = Get_Knot_Vector_shape(np,kp)
%GET_KNOT_VECTOR1 Summary of this function goes here
%   Detailed explanation goes here

pt=kp-1;

t  = zeros(np+kp,1);
dx = 1/(np+1-kp);


xis = 0:1/(np-1):1;

for i = kp:np+1
    t(i) = (i-kp)*dx;
end

if mod(pt,2)==0
    for ii = pt+2:np
        id1 = ii - pt/2 - 1;
        id2 = ii - pt/2;
        t(ii) = ( xis(id1) + xis(id2) )/2;
    end 
else
    for ii = pt+2:np
        id1   = ii - (pt+1)/2;
        t(ii) = xis(id1);
    end
end

t(1:kp)       = t(kp)   - 10*eps;
t(np+1:np+kp) = t(np+1) + 10*eps;

end

