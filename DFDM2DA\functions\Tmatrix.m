function [D110, D120, D210, D220] = Tmatrix(N1,pN,M1,pM)

% % connected interface
ord_gi = ceil((pN+pM)/2); 

N2 = N1 - 1;
M2 = M1 - 1;

D110 = zeros(N1,M1);
D120 = zeros(N1,M2);
D210 = zeros(N2,M1);
D220 = zeros(N2,M2);

kN1 = pN + 1;
kN2 = kN1 - 1;

kM1 = pM + 1;
kM2 = kM1 - 1;


tN1 = Get_Knot_Vector(N1,kN1);
tN2 = Get_Knot_Vector(N2,kN2);

tM1 = Get_Knot_Vector(M1,kM1);
tM2 = Get_Knot_Vector(M2,kM2);

nodesN1  = tN1(kN1:(N1+1));
nodesN2  = tN2(kN2:(N2+1));
nodesM1  = tM1(kM1:(M1+1));
nodesM2  = tM2(kM2:(M2+1));

nodesN1M1 = unique([nodesN1;nodesM1]);
nodesN1M2 = unique([nodesN1;nodesM2]);
nodesN2M1 = unique([nodesN2;nodesM1]);
nodesN2M2 = unique([nodesN2;nodesM2]);

NBx_intervals11 = length(nodesN1M1)-1;
NBx_intervals22 = length(nodesN2M2)-1;
NBx_intervals12 = length(nodesN1M2)-1;
NBx_intervals21 = length(nodesN2M1)-1;


xint11  = zeros(NBx_intervals11,ord_gi);
wxint11 = zeros(NBx_intervals11,ord_gi);
for kd = 1:NBx_intervals11
    [xint11(kd,:),wxint11(kd,:)]=lgwt(ord_gi,nodesN1M1(kd),nodesN1M1(kd+1));
end

xint22  = zeros(NBx_intervals22,ord_gi);
wxint22 = zeros(NBx_intervals22,ord_gi);
for kd = 1:NBx_intervals22
    [xint22(kd,:),wxint22(kd,:)]=lgwt(ord_gi,nodesN2M2(kd),nodesN2M2(kd+1));
end

xint12  = zeros(NBx_intervals12,ord_gi);
wxint12 = zeros(NBx_intervals12,ord_gi);
for kd = 1:NBx_intervals12
    [xint12(kd,:),wxint12(kd,:)]=lgwt(ord_gi,nodesN1M2(kd),nodesN1M2(kd+1));
end

xint21  = zeros(NBx_intervals21,ord_gi);
wxint21 = zeros(NBx_intervals21,ord_gi);
for kd = 1:NBx_intervals21
    [xint21(kd,:),wxint21(kd,:)]=lgwt(ord_gi,nodesN2M1(kd),nodesN2M1(kd+1));
end

for ib1 = 1:N1 %b1
    for jb1 = 1:M1 %b1
        for kd = 1:NBx_intervals11
            for lpt = 1: ord_gi
                btmp1 = bspln(tN1,N1,ib1,kN1,xint11(kd,lpt));
                btmp2 = bspln(tM1,M1,jb1,kM1,xint11(kd,lpt));
                D110(ib1,jb1) = D110(ib1,jb1) + btmp1*btmp2*wxint11(kd,lpt);
            end
        end  
    end
end

for ib2 = 1:N2 %b1
    for jb2 = 1:M2 %b1
        for kd = 1:NBx_intervals22
            for lpt = 1: ord_gi
                btmp1 = bspln(tN2,N2,ib2,kN2,xint22(kd,lpt));
                btmp2 = bspln(tM2,M2,jb2,kM2,xint22(kd,lpt));
                D220(ib2,jb2) = D220(ib2,jb2) + btmp1*btmp2*wxint22(kd,lpt);
            end
        end  
    end
end

for ib1 = 1:N1 %b1
    for jb2 = 1:M2 %b1
        for kd = 1:NBx_intervals12
            for lpt = 1: ord_gi
                btmp1 = bspln(tN1,N1,ib1,kN1,xint12(kd,lpt));
                btmp2 = bspln(tM2,M2,jb2,kM2,xint12(kd,lpt));
                D120(ib1,jb2) = D120(ib1,jb2) + btmp1*btmp2*wxint12(kd,lpt);
            end
        end  
    end
end

for ib2 = 1:N2 %b1
    for jb1 = 1:M1 %b1
        for kd = 1:NBx_intervals21
            for lpt = 1: ord_gi
                btmp1 = bspln(tN2,N2,ib2,kN2,xint21(kd,lpt));
                btmp2 = bspln(tM1,M1,jb1,kM1,xint21(kd,lpt));
                D210(ib2,jb1) = D210(ib2,jb1) + btmp1*btmp2*wxint21(kd,lpt);
            end
        end  
    end
end



end