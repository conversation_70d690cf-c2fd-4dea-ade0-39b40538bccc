function [hatpoints1,hatpoints2] = hat_pts(N,p)
k=p+1;
t1 = Get_Knot_Vector(N,k)*1;
t2 = Get_Knot_Vector(N-1,k-1)*1;

NB_intervals = N - p;
xintervals=zeros(NB_intervals,2);
xintervals(:,1) = t1((p+1):(N+k-p-1)); 
xintervals(:,2) = t1((p+2):(N+k-p)); 

ord_gi = p; %order of gaussina interpolation;

xint = zeros(NB_intervals,ord_gi);
wint = zeros(NB_intervals,ord_gi);
for kd = 1:NB_intervals
    [xint(kd,:),wint(kd,:)]=lgwt(ord_gi,xintervals(kd,1),xintervals(kd,2));
end

mm11 = mass_matrix(p,N,N,t1,t1,xint,wint,0);
mm22 = mass_matrix(p,N-1,N-1,t2,t2,xint,wint,1);

% mm11 = zeros(N,N);
% mm22 = zeros(N-1,N-1);
% 
% for ib1 = 1:N %b1
%     for jb1 = 1:N %b1
%         for kd = 1:NB_intervals
%             for lpt = 1: ord_gi
%                 b1tmp1 = bspln(t1,N,ib1,k,xint(kd,lpt));
%                 b1tmp2 = bspln(t1,N,jb1,k,xint(kd,lpt));
%                 mm11(ib1,jb1) = mm11(ib1,jb1) + b1tmp1*b1tmp2*wint(kd,lpt);
%             end
%         end
%     end
% end
% 
% 
% for ib2 = 1:N-1 %b2
%     for jb2 = 1:N-1 %b2
%         for kd = 1:NB_intervals
%             for lpt = 1: ord_gi
%                 b2tmp1 = bspln(t2,N-1,ib2,k-1,xint(kd,lpt));
%                 b2tmp2 = bspln(t2,N-1,jb2,k-1,xint(kd,lpt));
%                 mm22(ib2,jb2) = mm22(ib2,jb2) + b2tmp1*b2tmp2*wint(kd,lpt);
%             end
%         end
%     end
% end

% R=chol(A); A=RT*R; invA=invR*invRT; R is the upper triangle matrix
% L11T=chol(mm11); 
% L22T=chol(mm22); 

L11T = sqrtm(mm11); 
L22T = sqrtm(mm22); 

L11  = L11T';
L22  = L22T';
 
invL11  = inv(L11);
invL11T = inv(L11T);

invL22  = inv(L22);
invL22T = inv(L22T);

intB1=zeros(N,1);
intB2=zeros(N-1,1);
for ib1 = 1:N %b1
    for kd = 1:NB_intervals
        for lpt = 1: ord_gi
            b1tmp1 = bspln(t1,N,ib1,k,xint(kd,lpt));
            intB1(ib1) = intB1(ib1) + b1tmp1*wint(kd,lpt);   
        end
    end
end

for ib2 = 1:N-1 %b1
    for kd = 1:NB_intervals
        for lpt = 1: ord_gi
            b2tmp1 = bspln(t2,N-1,ib2,k-1,xint(kd,lpt));
            intB2(ib2) = intB2(ib2) + b2tmp1*wint(kd,lpt);   
        end
    end
end

intxB1=zeros(N,1);
intxB2=zeros(N-1,1);
for ib1 = 1:N %b1
    for kd = 1:NB_intervals
        for lpt = 1: ord_gi
            b1tmp1 = bspln(t1,N,ib1,k,xint(kd,lpt));
            tmp2   = xint(kd,lpt);
            intxB1(ib1) = intxB1(ib1) + b1tmp1*tmp2*wint(kd,lpt);   
        end
    end
end

for ib2 = 1:N-1 %b1
    for kd = 1:NB_intervals
        for lpt = 1: ord_gi
            b2tmp1 = bspln(t2,N-1,ib2,k-1,xint(kd,lpt));
            tmp2 = xint(kd,lpt);
            intxB2(ib2) = intxB2(ib2) + b2tmp1*tmp2*wint(kd,lpt);   
        end
    end
end

hatpoints1=(invL11*intxB1)./(invL11*intB1); 
hatpoints2=(invL22*intxB2)./(invL22*intB2);
% for cholsky
% hatpoints1(end)=1;
% hatpoints2(end)=1;

end

