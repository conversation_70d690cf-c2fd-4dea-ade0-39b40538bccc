function T12 = inner_product(N1,pN1,dev1,M2,pM2,dev2,flag)
% flag is used to tell the program whether two bases have the same grids or not

if flag == 1
    % kk12, m11 etc...
    if dev1 == 0 && dev2 == 0
        for ib1 = 1:N1 %b1
            for jb1 = 1:M2 %b1
                for kd = 1:NB_intervals12
                    for lpt = 1: ord_gi
                        b1tmp1 = bspln(t1,N1,ib1,kN1,int12(kd,lpt));
                        b1tmp2 = bspln(t2,M2,jb1,kN2,int12(kd,lpt));
                        T12(ib1,jb1) = T12(ib1,jb1) + b1tmp1*b1tmp2*wint12(kd,lpt);
                    end
                end
            end
        end
    end

     if dev1 == 1 && dev2 == 0
        for ib1 = 1:N1 %b1
            for jb1 = 1:M2 %b1
                for kd = 1:NB_intervals12
                    for lpt = 1: ord_gi
                        
                        db1tmp1 = dbspln(t1,N1,ib1,kN1,int12(kd,lpt),1);
                        b1tmp2  = bspln(t2,M2,jb1,kN2,int12(kd,lpt));
                        T12(ib1,jb1) = T12(ib1,jb1) + db1tmp1*b1tmp2*wint12(kd,lpt);
                    end
                end
            end
        end
    end

    if dev1 == 0 && dev2 == 1
        for ib1 = 1:N1 %b1
            for jb1 = 1:M2 %b1
                for kd = 1:NB_intervals12
                    for lpt = 1: ord_gi
                        b1tmp1 = bspln(t1,N1,ib1,kN1,int12(kd,lpt));
                        db1tmp2 = dbspln(t2,M2,jb1,kN2,int12(kd,lpt),1);
                        T12(ib1,jb1) = T12(ib1,jb1) + b1tmp1*db1tmp2*wint12(kd,lpt);
                    end
                end
            end
        end
    end
else

    kN1 = pN1+1;
    kN2 = pM2+1;
    ord_gi = ceil((pN1+pM2)/2); %order of gaussain interpolation;

    t1 = Get_Knot_Vector(N1,kN1);
    t2 = Get_Knot_Vector(M2,kN2);

    % t1 = Get_Knot_Vector_shape(N1,kN1);
    % t2 = Get_Knot_Vector_shape(M2,kN2);

    nodes1  = t1((pN1+1):(N1+kN1-pN1));
    nodes2  = t2((pM2+1):(M2+kN2-pN1));
    nodes12 = unique([nodes1;nodes2]);

    fprintf(" Number of grids of 1st base is %d ...\n",length(nodes1))
    fprintf(" Number of grids of 2st base is %d ...\n",length(nodes2))
    fprintf(" Number of grids of combination is %d = (%d + %d -2)? ...\n",length(nodes12),length(nodes1),length(nodes2))
    NB_intervals12 = length(nodes12)-1;


    int12  = zeros(NB_intervals12,ord_gi);
    wint12 = zeros(NB_intervals12,ord_gi);
    for kd = 1:NB_intervals12
        [int12(kd,:),wint12(kd,:)]=lgwt(ord_gi,nodes12(kd),nodes12(kd+1));
    end

    T12 = zeros(N1,M2);
    % transformation matrix Tij

    for ib1 = 1:N1 %b1
        for jb1 = 1:M2 %b1
            for kd = 1:NB_intervals12
                for lpt = 1: ord_gi
                    b1tmp1 = bspln(t1,N1,ib1,kN1,int12(kd,lpt));
                    b1tmp2 = bspln(t2,M2,jb1,kN2,int12(kd,lpt));
                    T12(ib1,jb1) = T12(ib1,jb1) + b1tmp1*b1tmp2*wint12(kd,lpt);
                end
            end
        end
    end
    
   
    

end

end

