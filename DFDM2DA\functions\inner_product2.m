function T12 = inner_product2(basis1,basis2)

    N1   = basis1.nb;
    pN1  = basis1.pb;
    % xps1 = basis1.xps;
    t1  = basis1.tx;

    M2   = basis2.nb;
    pM2  = basis2.pb;
    % xps2 = basis2.xps;
    t2   = basis2.tx;



    kN1 = pN1+1;
    kM2 = pM2+1;
    ord_gi = ceil((pN1+pM2+1)/2); %order of gaussain interpolation;


    nodes1  = t1((pN1+1):(N1+kN1-pN1));
    % nodes2  = t2((pM2+1):(M2+kN2-pN1));
    nodes2  = t2((pM2+1):(M2+kM2-pM2));
    nodes12 = unique([nodes1;nodes2]);

    fprintf(" Number of grids of 1st base is %d ...\n",length(nodes1))
    fprintf(" Number of grids of 2st base is %d ...\n",length(nodes2))
    fprintf(" Number of grids of combination is %d = (%d + %d -2)? ...\n",length(nodes12),length(nodes1),length(nodes2))
    NB_intervals12 = length(nodes12)-1;


    int12  = zeros(NB_intervals12,ord_gi);
    wint12 = zeros(NB_intervals12,ord_gi);
    for kd = 1:NB_intervals12
        [int12(kd,:),wint12(kd,:)]=lgwt(ord_gi,nodes12(kd),nodes12(kd+1));
    end

    T12 = zeros(N1,M2);
    % transformation matrix Tij


    for ib1 = 1:N1 %b1
        for jb1 = 1:M2 %b1
            for kd = 1:NB_intervals12
                for lpt = 1: ord_gi
                    b1tmp1 = bspln(t1,N1,ib1,kN1,int12(kd,lpt));
                    b1tmp2 = bspln(t2,M2,jb1,kM2,int12(kd,lpt));
                    T12(ib1,jb1) = T12(ib1,jb1) + b1tmp1*b1tmp2*wint12(kd,lpt);
                end
            end
        end
    end

end


