function [matrix_out_CDS] = matrix_to_CDS(matrix_in)
% convert a regular matrix to the CDS formal
% Compressed Diagonal Storage

% input matrix mat
% with size (m,n)
% output matrix mat_CDS
% p: subdiagonals
% q: superdiagonals

% For example, m=n=5, p=2, q=1
% Input A
% a_{11} & a_{12} & 0      & 0      & 0      \\ 
% a_{21} & a_{22} & a_{23} & 0      & 0      \\ 
% a_{31} & a_{32} & a_{33} & a_{34} & 0      \\ 
% 0      & a_{42} & a_{43} & a_{44} & a_{45} \\
% 0      & 0      & a_{53} & a_{54} & a_{55} \\

% Output AB
% 0      & a_{12} & a_{23} & a_{34} & a_{45} \\
% a_{11} & a_{22} & a_{33} & a_{44} & a_{55} \\
% a_{21} & a_{32} & a_{43} & a_{54} &   0    \\
% a_{31} & a_{42} & a_{53} &   0    &   0    \\

[nr,nc]=size(matrix_in);

[p,q]=get_bandwidth(matrix_in,eps*5); 

matrix_out_CDS = CDS_matrix(); % initialization

matrix_out_CDS.nrow = nr;

matrix_out_CDS = zeros(p+q+1,nc);

for j=1:nc
    i1 = max(1,j-q);
    i2 = min(j+p,nr);
    matrix_out_CDS((i1-j+q+1):(i2-j+q+1),j) = matrix_in(i1:i2,j);
end

end


