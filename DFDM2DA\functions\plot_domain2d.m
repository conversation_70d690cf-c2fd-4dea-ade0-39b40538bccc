function plot_domain2d(OM)
%PLOT_DOMAIN Summary of this function goes here

figure

rmax = 0;
hold on
for iom = 1:length(OM)
    xa  = OM(iom).model2dA.xa;
    za  = OM(iom).model2dA.za;
    rho = OM(iom).model2dA.rho;
    mu  = OM(iom).model2dA.mu;

    Vp = sqrt(mu./rho);
    dist = sqrt(xa.^2+za.^2);
    rmax = max(rmax,max(dist(:)));
    [nx,nz] = size(xa);

    id1 = 1:nx:nx*nz;
    id2 = nx:nx:nx*nz;
    id3 = 1:nx;
    id4 = nx*(nz-1)+1:nx*nz;
    plot(xa(id1)/1e3,za(id1)/1e3,'k','LineWidth',2);
    plot(xa(id2)/1e3,za(id2)/1e3,'k','LineWidth',2);
    plot(xa(id3)/1e3,za(id3)/1e3,'k','LineWidth',2);
    plot(xa(id4)/1e3,za(id4)/1e3,'k','LineWidth',2);

    pcolor(xa/1e3,za/1e3,Vp); shading interp;
    

end

colormap("hot");
c=colorbar;
c.Title.String = 'Vp (m/s)';
c.Title.FontSize = 20;
xlabel('km')
ylabel('km')
xlim([-rmax,rmax])
ylim([-rmax,rmax])
axis equal tight

set(gca,'fontsize',20)
box on
hold off




end

