function [sxx, szz] = rotate_sij(sxx, szz, rot_mat)
    % ROTATE_SIJ Rotates the stress components using the given rotation matrix
    % Parameters:
    %   sxx, sxz, szx, szz: 2D stress component matrices
    %   rot: 2D rotation matrix

    [nx, nz] = size(sxx);
    sij = zeros(1, 2);

    for k = 1:nz
        for i = 1:nx
            % Assign the stress components to the sij matrix
            sij(1,1) = sxx(i,k);
            sij(1,2) = szz(i,k);
            % Perform the rotation using matrix multiplication

            sij = sij * rot_mat;
            
            % Assign the rotated stress components back to the original matrices
            sxx(i,k) = sij(1,1);
            szz(i,k) = sij(1,2);
        end
    end

    
    
end
