function save_domain_data_to_files(domain, output_dir, domain_id)
% SAVE_DOMAIN_DATA_TO_FILES 保存域数据到文件，格式与C++输出一致
%
% 输入:
%   domain - 域结构体
%   output_dir - 输出目录
%   domain_id - 域ID (0-based)

    % 创建域目录
    domain_dir = fullfile(output_dir, sprintf('domain_%d', domain_id));
    if ~exist(domain_dir, 'dir')
        mkdir(domain_dir);
    end
    
    fprintf('保存域 %d 数据到 %s\n', domain_id, domain_dir);
    
    % 保存参数文件
    save_parameters_file(domain, domain_dir, domain_id);
    
    % 保存坐标数据
    save_coordinate_data(domain, domain_dir);
    
    % 保存基函数矩阵
    save_basis_matrices(domain, domain_dir);
    
    % 保存坐标变换矩阵
    save_jacobian_matrices(domain, domain_dir);
    
    % 保存状态矩阵
    save_state_matrices(domain, domain_dir);
    
    % 保存边界向量
    save_boundary_vectors(domain, domain_dir);
    
    fprintf('域 %d 数据保存完成\n', domain_id);
end

function save_parameters_file(domain, domain_dir, domain_id)
    % 保存参数文件
    params_file = fullfile(domain_dir, 'parameters.txt');
    fid = fopen(params_file, 'w');
    if fid == -1
        error('无法创建参数文件: %s', params_file);
    end
    
    try
        fprintf(fid, 'iom = %d\n', domain_id);
        fprintf(fid, 'region = %d\n', domain.region);
        fprintf(fid, 'Nx1 = %d\n', domain.Nx1);
        fprintf(fid, 'Nz1 = %d\n', domain.Nz1);
        fprintf(fid, 'Nx2 = %d\n', domain.Nx1 - 1);  % Nx2 = Nx1 - 1
        fprintf(fid, 'Nz2 = %d\n', domain.Nz1 - 1);  % Nz2 = Nz1 - 1
        fprintf(fid, 'px1 = %d\n', domain.px1);
        fprintf(fid, 'pz1 = %d\n', domain.pz1);
        
        % 计算域边界
        if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
            x_min = min(domain.x2d11(:));
            x_max = max(domain.x2d11(:));
            z_min = min(domain.z2d11(:));
            z_max = max(domain.z2d11(:));
        else
            x_min = 0; x_max = 0; z_min = 0; z_max = 0;
        end
        
        fprintf(fid, 'x_min = %.16e\n', x_min);
        fprintf(fid, 'x_max = %.16e\n', x_max);
        fprintf(fid, 'z_min = %.16e\n', z_min);
        fprintf(fid, 'z_max = %.16e\n', z_max);
        
    catch ME
        fclose(fid);
        rethrow(ME);
    end
    fclose(fid);
end

function save_coordinate_data(domain, domain_dir)
    % 保存坐标数据
    save_matrix_to_file(domain, 'x2d11', domain_dir, 'x2d11.txt');
    save_matrix_to_file(domain, 'z2d11', domain_dir, 'z2d11.txt');
    save_matrix_to_file(domain, 'x2d22', domain_dir, 'x2d22.txt');
    save_matrix_to_file(domain, 'z2d22', domain_dir, 'z2d22.txt');
end

function save_basis_matrices(domain, domain_dir)
    % 保存基函数矩阵
    save_matrix_to_file(domain, 'bxT1', domain_dir, 'bxT1.txt');
    save_matrix_to_file(domain, 'bxT2', domain_dir, 'bxT2.txt');
    save_matrix_to_file(domain, 'bzT1', domain_dir, 'bzT1.txt');
    save_matrix_to_file(domain, 'bzT2', domain_dir, 'bzT2.txt');
end

function save_jacobian_matrices(domain, domain_dir)
    % 保存坐标变换矩阵
    save_matrix_to_file(domain, 'dxpdx11', domain_dir, 'dxpdx11.txt');
    save_matrix_to_file(domain, 'dxpdx22', domain_dir, 'dxpdx22.txt');
    save_matrix_to_file(domain, 'dxpdz11', domain_dir, 'dxpdz11.txt');
    save_matrix_to_file(domain, 'dxpdz22', domain_dir, 'dxpdz22.txt');
    save_matrix_to_file(domain, 'dzpdx11', domain_dir, 'dzpdx11.txt');
    save_matrix_to_file(domain, 'dzpdx22', domain_dir, 'dzpdx22.txt');
    save_matrix_to_file(domain, 'dzpdz11', domain_dir, 'dzpdz11.txt');
    save_matrix_to_file(domain, 'dzpdz22', domain_dir, 'dzpdz22.txt');
end

function save_state_matrices(domain, domain_dir)
    % 保存状态矩阵
    save_matrix_to_file(domain, 'state_U12', domain_dir, 'state_U12.txt');
    save_matrix_to_file(domain, 'state_U21', domain_dir, 'state_U21.txt');
    save_matrix_to_file(domain, 'state_Sxx11', domain_dir, 'state_Sxx11.txt');
    save_matrix_to_file(domain, 'state_Sxx22', domain_dir, 'state_Sxx22.txt');
    save_matrix_to_file(domain, 'state_Szz11', domain_dir, 'state_Szz11.txt');
    save_matrix_to_file(domain, 'state_Szz22', domain_dir, 'state_Szz22.txt');
    save_matrix_to_file(domain, 'state_Sxz11', domain_dir, 'state_Sxz11.txt');
    save_matrix_to_file(domain, 'state_Sxz22', domain_dir, 'state_Sxz22.txt');
end

function save_boundary_vectors(domain, domain_dir)
    % 保存边界向量
    save_matrix_to_file(domain, 'boundary_U12_left', domain_dir, 'boundary_U12_left.txt');
    save_matrix_to_file(domain, 'boundary_U12_right', domain_dir, 'boundary_U12_right.txt');
    save_matrix_to_file(domain, 'boundary_U12_bottom', domain_dir, 'boundary_U12_bottom.txt');
    save_matrix_to_file(domain, 'boundary_U12_top', domain_dir, 'boundary_U12_top.txt');
    save_matrix_to_file(domain, 'boundary_U21_left', domain_dir, 'boundary_U21_left.txt');
    save_matrix_to_file(domain, 'boundary_U21_right', domain_dir, 'boundary_U21_right.txt');
    save_matrix_to_file(domain, 'boundary_U21_bottom', domain_dir, 'boundary_U21_bottom.txt');
    save_matrix_to_file(domain, 'boundary_U21_top', domain_dir, 'boundary_U21_top.txt');
end

function save_matrix_to_file(domain, field_name, domain_dir, filename)
    % 通用矩阵保存函数
    if isfield(domain, field_name) && ~isempty(domain.(field_name))
        matrix = domain.(field_name);
        filepath = fullfile(domain_dir, filename);
        
        % 使用与C++相同的格式保存
        fid = fopen(filepath, 'w');
        if fid == -1
            warning('无法创建文件: %s', filepath);
            return;
        end
        
        try
            [rows, cols] = size(matrix);
            for i = 1:rows
                for j = 1:cols
                    if j == cols
                        fprintf(fid, '%.16e\n', matrix(i, j));
                    else
                        fprintf(fid, '%.16e ', matrix(i, j));
                    end
                end
            end
        catch ME
            fclose(fid);
            warning('保存矩阵 %s 时出错: %s', field_name, ME.message);
            return;
        end
        fclose(fid);
        
        fprintf('  保存 %s: %dx%d\n', filename, rows, cols);
    else
        fprintf('  跳过 %s (字段不存在或为空)\n', filename);
    end
end
