function save_matlab_parameters(domain, output_dir)
% 保存 MATLAB 域参数，用于与 C++ 对比
    
    params_file = fullfile(output_dir, 'matlab_parameters.txt');
    fid = fopen(params_file, 'w');
    if fid == -1
        error('无法创建参数文件: %s', params_file);
    end
    
    try
        fprintf(fid, '# MATLAB Domain Parameters\n');
        fprintf(fid, 'iom = 0\n');
        % region 字段在 MATLAB 中可能不存在
        if isfield(domain, 'region')
            fprintf(fid, 'region = %d\n', domain.region);
        else
            fprintf(fid, 'region = %d\n', 2);  % 默认值，与C++一致
        end
        fprintf(fid, 'Nx1 = %d\n', domain.Nx1);
        fprintf(fid, 'Nz1 = %d\n', domain.Nz1);
        fprintf(fid, 'Nx2 = %d\n', domain.Nx1 - 1);  % Nx2 = Nx1 - 1
        fprintf(fid, 'Nz2 = %d\n', domain.Nz1 - 1);  % Nz2 = Nz1 - 1
        fprintf(fid, 'px1 = %d\n', domain.px1);
        fprintf(fid, 'pz1 = %d\n', domain.pz1);
        
        % 计算域边界
        if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
            x_min = min(domain.x2d11(:));
            x_max = max(domain.x2d11(:));
            z_min = min(domain.z2d11(:));
            z_max = max(domain.z2d11(:));
        else
            x_min = 0; x_max = 0; z_min = 0; z_max = 0;
        end
        
        fprintf(fid, 'x_min = %.16e\n', x_min);
        fprintf(fid, 'x_max = %.16e\n', x_max);
        fprintf(fid, 'z_min = %.16e\n', z_min);
        fprintf(fid, 'z_max = %.16e\n', z_max);
        
        % 材料参数
        if isfield(domain, 'mu11') && ~isempty(domain.mu11)
            mu_val = domain.mu11(1,1);
        else
            mu_val = 0;
        end
        
        if isfield(domain, 'rho11') && ~isempty(domain.rho11)
            rho_val = domain.rho11(1,1);
        else
            rho_val = 0;
        end
        
        fprintf(fid, 'mu = %.16e\n', mu_val);
        fprintf(fid, 'rho = %.16e\n', rho_val);
        
        fprintf('  保存参数文件: %s\n', params_file);
        
    catch ME
        fclose(fid);
        rethrow(ME);
    end
    fclose(fid);
end

function save_matlab_coordinates(domain, output_dir)
% 保存 MATLAB 坐标数据
    
    fprintf('  保存坐标数据...\n');
    
    % 保存物理坐标
    save_matrix_to_csv(domain.x2d11, fullfile(output_dir, 'matlab_x2d11.csv'));
    save_matrix_to_csv(domain.z2d11, fullfile(output_dir, 'matlab_z2d11.csv'));
    
    if isfield(domain, 'x2d22') && ~isempty(domain.x2d22)
        save_matrix_to_csv(domain.x2d22, fullfile(output_dir, 'matlab_x2d22.csv'));
    end
    
    if isfield(domain, 'z2d22') && ~isempty(domain.z2d22)
        save_matrix_to_csv(domain.z2d22, fullfile(output_dir, 'matlab_z2d22.csv'));
    end
    
    % 保存参考坐标（如果存在）
    if isfield(domain, 'xi1') && ~isempty(domain.xi1)
        save_vector_to_csv(domain.xi1, fullfile(output_dir, 'matlab_xi1.csv'));
    end
    
    if isfield(domain, 'eta1') && ~isempty(domain.eta1)
        save_vector_to_csv(domain.eta1, fullfile(output_dir, 'matlab_eta1.csv'));
    end
end

function save_matlab_basis_functions(domain, output_dir)
% 保存 MATLAB 基函数矩阵
    
    fprintf('  保存基函数矩阵...\n');
    
    % 保存基函数矩阵
    if isfield(domain, 'bxT1') && ~isempty(domain.bxT1)
        save_matrix_to_csv(domain.bxT1, fullfile(output_dir, 'matlab_bxT1.csv'));
        fprintf('    bxT1: %dx%d\n', size(domain.bxT1));
    end
    
    if isfield(domain, 'bxT2') && ~isempty(domain.bxT2)
        save_matrix_to_csv(domain.bxT2, fullfile(output_dir, 'matlab_bxT2.csv'));
        fprintf('    bxT2: %dx%d\n', size(domain.bxT2));
    end
    
    if isfield(domain, 'bzT1') && ~isempty(domain.bzT1)
        save_matrix_to_csv(domain.bzT1, fullfile(output_dir, 'matlab_bzT1.csv'));
        fprintf('    bzT1: %dx%d\n', size(domain.bzT1));
    end
    
    if isfield(domain, 'bzT2') && ~isempty(domain.bzT2)
        save_matrix_to_csv(domain.bzT2, fullfile(output_dir, 'matlab_bzT2.csv'));
        fprintf('    bzT2: %dx%d\n', size(domain.bzT2));
    end
end

function save_matlab_coordinate_transforms(domain, output_dir)
% 保存 MATLAB 坐标变换矩阵
    
    fprintf('  保存坐标变换矩阵...\n');
    
    % 保存雅可比矩阵
    jacobian_fields = {'dxpdx11', 'dxpdx22', 'dxpdz11', 'dxpdz22', ...
                       'dzpdx11', 'dzpdx22', 'dzpdz11', 'dzpdz22'};
    
    for i = 1:length(jacobian_fields)
        field = jacobian_fields{i};
        if isfield(domain, field) && ~isempty(domain.(field))
            filename = sprintf('matlab_%s.csv', field);
            save_matrix_to_csv(domain.(field), fullfile(output_dir, filename));
            fprintf('    %s: %dx%d\n', field, size(domain.(field)));
        else
            fprintf('    %s: 不存在或为空\n', field);
        end
    end
end

function save_matlab_mesh_data(domain, output_dir)
% 保存 MATLAB 网格生成的中间数据
    
    fprintf('  保存网格中间数据...\n');
    
    % 保存材料参数矩阵
    if isfield(domain, 'mu11') && ~isempty(domain.mu11)
        save_matrix_to_csv(domain.mu11, fullfile(output_dir, 'matlab_mu11.csv'));
        fprintf('    mu11: %dx%d, 值范围: [%.2e, %.2e]\n', size(domain.mu11), ...
                min(domain.mu11(:)), max(domain.mu11(:)));
    end
    
    if isfield(domain, 'rho11') && ~isempty(domain.rho11)
        save_matrix_to_csv(domain.rho11, fullfile(output_dir, 'matlab_rho11.csv'));
        fprintf('    rho11: %dx%d, 值范围: [%.2e, %.2e]\n', size(domain.rho11), ...
                min(domain.rho11(:)), max(domain.rho11(:)));
    end
    
    % 保存权重和积分点（如果存在）
    if isfield(domain, 'wxint') && ~isempty(domain.wxint)
        save_vector_to_csv(domain.wxint, fullfile(output_dir, 'matlab_wxint.csv'));
    end
    
    if isfield(domain, 'wzint') && ~isempty(domain.wzint)
        save_vector_to_csv(domain.wzint, fullfile(output_dir, 'matlab_wzint.csv'));
    end
    
    if isfield(domain, 'xint') && ~isempty(domain.xint)
        save_vector_to_csv(domain.xint, fullfile(output_dir, 'matlab_xint.csv'));
    end
    
    if isfield(domain, 'zint') && ~isempty(domain.zint)
        save_vector_to_csv(domain.zint, fullfile(output_dir, 'matlab_zint.csv'));
    end
end

function save_matrix_to_csv(matrix, filename)
% 保存矩阵到 CSV 文件
    if isempty(matrix)
        return;
    end
    
    try
        csvwrite(filename, matrix);
    catch
        % 如果 csvwrite 失败，使用 dlmwrite
        dlmwrite(filename, matrix, 'precision', '%.16e');
    end
end

function save_vector_to_csv(vector, filename)
% 保存向量到 CSV 文件
    if isempty(vector)
        return;
    end

    % 确保是列向量
    if size(vector, 1) == 1
        vector = vector';
    end

    save_matrix_to_csv(vector, filename);
end

function save_matlab_debug_info(domain, output_dir)
% 保存 MATLAB 调试信息，用于问题诊断

    fprintf('  保存调试信息...\n');

    debug_file = fullfile(output_dir, 'matlab_debug_info.txt');
    fid = fopen(debug_file, 'w');
    if fid == -1
        error('无法创建调试文件: %s', debug_file);
    end

    try
        fprintf(fid, '# MATLAB 调试信息\n');
        fprintf(fid, '# 生成时间: %s\n\n', datestr(now));

        % 域结构体字段信息
        fprintf(fid, '## 域结构体字段列表:\n');
        fields = fieldnames(domain);
        for i = 1:length(fields)
            field = fields{i};
            if isnumeric(domain.(field)) || islogical(domain.(field))
                if isempty(domain.(field))
                    fprintf(fid, '%s: 空矩阵\n', field);
                else
                    fprintf(fid, '%s: %s, 大小: %s\n', field, class(domain.(field)), mat2str(size(domain.(field))));
                    if numel(domain.(field)) <= 10
                        fprintf(fid, '  值: %s\n', mat2str(domain.(field)));
                    else
                        fprintf(fid, '  值范围: [%.6e, %.6e]\n', min(domain.(field)(:)), max(domain.(field)(:)));
                    end
                end
            else
                fprintf(fid, '%s: %s\n', field, class(domain.(field)));
            end
        end

        % 关键计算检查
        fprintf(fid, '\n## 关键计算检查:\n');

        % 检查 Nx2, Nz2 计算
        fprintf(fid, 'Nx1 = %d, 计算的 Nx2 = %d\n', domain.Nx1, domain.Nx1-1);
        fprintf(fid, 'Nz1 = %d, 计算的 Nz2 = %d\n', domain.Nz1, domain.Nz1-1);

        % 检查坐标范围
        if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
            fprintf(fid, 'x2d11 范围: [%.6e, %.6e]\n', min(domain.x2d11(:)), max(domain.x2d11(:)));
            fprintf(fid, 'z2d11 范围: [%.6e, %.6e]\n', min(domain.z2d11(:)), max(domain.z2d11(:)));
        end

        % 检查基函数矩阵
        if isfield(domain, 'bxT1') && ~isempty(domain.bxT1)
            fprintf(fid, 'bxT1 非零元素数: %d / %d\n', nnz(domain.bxT1), numel(domain.bxT1));
            fprintf(fid, 'bxT1 值范围: [%.6e, %.6e]\n', min(domain.bxT1(:)), max(domain.bxT1(:)));
        end

        % 检查坐标变换矩阵
        jacobian_fields = {'dxpdx11', 'dxpdx22', 'dxpdz11', 'dxpdz22', ...
                           'dzpdx11', 'dzpdx22', 'dzpdz11', 'dzpdz22'};

        fprintf(fid, '\n## 坐标变换矩阵检查:\n');
        for i = 1:length(jacobian_fields)
            field = jacobian_fields{i};
            if isfield(domain, field) && ~isempty(domain.(field))
                matrix = domain.(field);
                fprintf(fid, '%s: 大小 %dx%d, 值范围 [%.6e, %.6e], 非零元素 %d\n', ...
                        field, size(matrix), min(matrix(:)), max(matrix(:)), nnz(matrix));

                % 检查是否为单位矩阵或零矩阵
                if all(matrix(:) == 1.0)
                    fprintf(fid, '  警告: %s 全部为 1.0 (可能是单位矩阵)\n', field);
                elseif all(matrix(:) == 0.0)
                    fprintf(fid, '  警告: %s 全部为 0.0 (零矩阵)\n', field);
                end
            else
                fprintf(fid, '%s: 不存在或为空\n', field);
            end
        end

        fprintf('  调试信息已保存: %s\n', debug_file);

    catch ME
        fclose(fid);
        rethrow(ME);
    end
    fclose(fid);
end
