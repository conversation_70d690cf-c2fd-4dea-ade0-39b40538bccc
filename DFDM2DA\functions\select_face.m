function Uout = select_face(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Uop, jFace)
    % SELECT_FACE Selects and flips the face of a 2D element based on the face index
    % Parameters:
    %   Umo, Upo, Uom, Uop: 2D element
    %   jFace: index of the face to be selected
    %   exbases, flip: additional parameters for the flip2d function


    switch abs(jFace)
        case 1
            i1 = 1;
            i2 = size(Umo, 1);
            j1 = 1;
            j2 = size(Umo, 2);
            Uout = flip1d(Umo, i1, i2, j1, j2, jFace);
            % Uout = reshape(Uout,length(Uout),1);
            % fprintf('... select face %d ...\n',jFace);
        case 2
            i1 = 1;
            i2 = size(Upo, 1);
            j1 = 1;
            j2 = size(Upo, 2);
            Uout = flip1d(Upo, i1, i2, j1, j2, jFace);
            % Uout = reshape(Uout,length(Uout),1);
            % fprintf('... select face %d ...\n',jFace);
        case 3
            i1 = 1;
            i2 = size(<PERSON><PERSON>, 1);
            j1 = 1;
            j2 = size(Uom, 2);
            Uout = flip1d(Uom, i1, i2, j1, j2, jFace);
            % Uout = reshape(Uout,length(Uout),1);
            % fprintf('... select face %d ...\n',jFace);
        case 4
            i1 = 1;
            i2 = size(Uop, 1);
            j1 = 1;
            j2 = size(Uop, 2);
            Uout  = flip1d(Uop, i1, i2, j1, j2, jFace);
            % Uout  = reshape(Uout,length(Uout),1);
            % fprintf('... select face %d ...\n',jFace);
        otherwise
            error('Invalid face index');
    end
    
end


