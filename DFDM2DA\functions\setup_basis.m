function basis_param = setup_basis(Nx1,px1)

struct_basis = struct('nb'   ,[], ...
                      'pb'   ,[], ...
                      'xps'  ,[], ...
                      'tx'   ,[]);

basis_param  = struct_basis(1);

kx1 = px1 + 1;
if(px1>Nx1-1)
    fprintf("Error in setup_basis, pb should be <=nb-1 ...\n");
end
xps = 0:1/(Nx1-1):1;
tx  = Get_Knot_Vector(Nx1,kx1);

basis_param.nb   = Nx1;
basis_param.pb   = px1;
basis_param.xps  = xps;
basis_param.tx   = tx;

end