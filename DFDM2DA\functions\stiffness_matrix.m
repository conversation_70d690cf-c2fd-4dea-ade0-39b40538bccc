function kk = stiffness_matrix(p,N1,tx1,tx2,xint,wxint,kind)


[NB_intervals,ord_gi] = size(xint);
k = p+1;
if kind==0
    kk = zeros(N1,N1-1);
    for ib1 = 1:2*p %b1
        for jb1 = 1:3*p %b1
            for kd = 1:NB_intervals
                for lpt = 1: ord_gi
                    b1tmp1 = dbspln(tx1,N1,ib1,k,xint(kd,lpt),1);
                    b1tmp2 =  bspln(tx2,N1-1,jb1,k-1,xint(kd,lpt));
                    kk(ib1,jb1) = kk(ib1,jb1) + b1tmp1*b1tmp2*wxint(kd,lpt);
                end
            end
        end
    end

    for i=2*p+1:(N1-p*2)
        kk(i,(i-p):(i+p))=kk(i-1,(i-1-p):(i-1+p));
    end

    for ib1 = (N1-p*2+1):N1 %b1
        for jb1 = (N1-3*p):N1-1 %b1
            for kd = 1:NB_intervals
                for lpt = 1: ord_gi
                    b1tmp1 = dbspln(tx1,N1,ib1,k,xint(kd,lpt),1);
                    b1tmp2 =  bspln(tx2,N1-1,jb1,k-1,xint(kd,lpt));
                    kk(ib1,jb1) = kk(ib1,jb1) + b1tmp1*b1tmp2*wxint(kd,lpt);
                end
            end
        end
    end
else
    kk = zeros(N1-1,N1);
    for ib1 = 1:2*p %b1
        for jb1 = 1:3*p %b1
            for kd = 1:NB_intervals
                for lpt = 1: ord_gi
                    b1tmp1 = dbspln(tx2,N1-1,ib1,k-1,xint(kd,lpt),1);
                    b1tmp2 =  bspln(tx1,N1,jb1,k,xint(kd,lpt));
                    kk(ib1,jb1) = kk(ib1,jb1) + b1tmp1*b1tmp2*wxint(kd,lpt);
                end
            end
        end
    end

    for i=2*p+1:(N1-p*2)
        kk(i,(i-p):(i+p))=kk(i-1,(i-1-p):(i-1+p));
    end

    for ib1 = (N1-p*2+1):N1-1 %b1
        for jb1 = (N1-3*p):N1 %b1
            for kd = 1:NB_intervals
                for lpt = 1: ord_gi
                    b1tmp1 = dbspln(tx2,N1-1,ib1,k-1,xint(kd,lpt),1);
                    b1tmp2 =  bspln(tx1,N1,jb1,k,xint(kd,lpt));
                    kk(ib1,jb1) = kk(ib1,jb1) + b1tmp1*b1tmp2*wxint(kd,lpt);
                end
            end
        end
    end

end

end

