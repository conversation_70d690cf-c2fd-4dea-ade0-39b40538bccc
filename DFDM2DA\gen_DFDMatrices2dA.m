% need to learn from <PERSON><PERSON>'s 2D paper
% try to change the inner_product function so that all the matrices related
% to DFDM are generated using one function
function OM = gen_DFDMatrices2dA(OM)
 
    ndomains = length(OM);
    for iom = 1:ndomains
         
        px1  = OM(iom).px1;  px2 = px1 - 1;
        pz1  = OM(iom).pz1;  pz2 = pz1 - 1;
        
        % ord_gi = 5; %order of gaussina interpolation, to be changed.
        ord_gi = 6;

        Nx1 = OM(iom).Nx1; Nx2 = Nx1 - 1;
        Nz1 = OM(iom).Nz1; Nz2 = Nz1 - 1;

        dx  = 1/(Nx1-1);
        dz  = 1/(Nz1-1);
        
        xps1 = 0:dx:1;
        zps1 = 0:dz:1;

        fprintf("...preparing all the DFD Matrices of domain %d with %d and %d points ... \n",iom,Nx1,Nz1);
        kx1 = px1 + 1;
        kz1 = pz1 + 1;
        kx2 = kx1 -1;
        kz2 = kz1 -1;

        tx1 = Get_Knot_Vector(Nx1,kx1);
        tx2 = Get_Knot_Vector(Nx2,kx2);

        tz1 = Get_Knot_Vector(Nz1,kz1);
        tz2 = Get_Knot_Vector(Nz2,kz2);

        NBx_intervals = Nx1 - px1;
        NBz_intervals = Nz1 - pz1;
        xintervals=zeros(NBx_intervals,2);
        zintervals=zeros(NBz_intervals,2);

        xintervals(:,1) = tx1((px1+1):Nx1);
        xintervals(:,2) = tx1((px1+2):Nx1+1);

        zintervals(:,1) = tz1((pz1+1):Nz1);
        zintervals(:,2) = tz1((pz1+2):Nz1+1);

        
        xint  = zeros(NBx_intervals,ord_gi);
        zint  = zeros(NBz_intervals,ord_gi);
        wxint = zeros(NBx_intervals,ord_gi);
        wzint = zeros(NBz_intervals,ord_gi);
        for kd = 1:NBx_intervals
            [xint(kd,:),wxint(kd,:)]=lgwt(ord_gi,xintervals(kd,1),xintervals(kd,2));
        end

        for kd = 1:NBz_intervals
            [zint(kd,:),wzint(kd,:)]=lgwt(ord_gi,zintervals(kd,1),zintervals(kd,2));
        end

       
        bx1   = zeros(Nx1,Nx1);
        for i = 1:Nx1
            for j = 1:Nx1
                xj       = xps1(j);
                bx1(i,j) = bspln(tx1,Nx1,i,kx1,xj);
            end
        end
        bx1T = bx1';
        
        bx2 = zeros(Nx2,Nx1); % each col is one B2
        for i = 1:Nx2
            for j = 1:Nx1
                x0 = xps1(j);
                bx2(i,j)  =  bspln(tx2,Nx2,i,kx2,x0);
            end
        end
        bx2T = bx2';

        bz1   = zeros(Nz1,Nz1);
        for i = 1:Nz1
            for j = 1:Nz1
                zj       = zps1(j);
                bz1(i,j) = bspln(tz1,Nz1,i,kz1,zj);
            end
        end
        bz1T = bz1';

        bz2 = zeros(Nz2,Nz1); % each col is one B2
        for i = 1:Nz2
            for j = 1:Nz1
                z0 = zps1(j);
                bz2(i,j)  =  bspln(tz2,Nz2,i,kx2,z0);
            end
        end
        bz2T = bz2';

        option=0;
        kkx12 = stiffness_matrix(px1,Nx1,tx1,tx2,xint,wxint,option);
        option=0;
        kkz12 = stiffness_matrix(pz1,Nz1,tz1,tz2,zint,wzint,option);

        option=1;
        kkx21 = stiffness_matrix(px1,Nx1,tx1,tx2,xint,wxint,option);
        option=1;
        kkz21 = stiffness_matrix(pz1,Nz1,tz1,tz2,zint,wzint,option);


        option = 0;
        mmx11 = mass_matrix(px1,Nx1,Nx1,tx1,tx1,xint,wxint,option);
        option = 0;
        mmz11 = mass_matrix(pz1,Nz1,Nz1,tz1,tz1,zint,wzint,option);

        option = 1;
        mmx22 = mass_matrix(px1,Nx2,Nx2,tx2,tx2,xint,wxint,option);
        option = 1;
        mmz22 = mass_matrix(pz1,Nz2,Nz2,tz2,tz2,zint,wzint,option);

        %
        LxT11    = sqrtm(mmx11);  % upper triangle
        % LxT11    = chol(mmx11);  % upper triangle
        Lx11     = LxT11';        % lower triangle
        invLx11  = inv(Lx11);
        invLxT11 = inv(LxT11);

        LxT22    = sqrtm(mmx22);
        % LxT22    = chol(mmx22);
        Lx22     = LxT22';        % lower triangle
        invLx22  = inv(Lx22);
        invLxT22 = inv(LxT22);

        LzT11    = sqrtm(mmz11);  % upper triangle
        % LzT11    = chol(mmz11);  % upper triangle
        Lz11     = LzT11';        % lower triangle
        invLz11  = inv(Lz11);
        invLzT11 = inv(LzT11);

        LzT22    = sqrtm(mmz22);
        % LzT22    = chol(mmz22);
        Lz22     = LzT22';        % lower triangle
        invLz22  = inv(Lz22);
        invLzT22 = inv(LzT22);

        
        OM(iom).bxT1  = bx1T;
        OM(iom).bxT2  = bx2T;
        OM(iom).bzT1  = bz1T;
        OM(iom).bzT2  = bz2T;
        OM(iom).kkx12 = kkx12;
        OM(iom).kkx21 = kkx21;
        OM(iom).kkz12 = kkz12;
        OM(iom).kkz21 = kkz21;

        OM(iom).invLx11  = invLx11;
        OM(iom).invLx22  = invLx22;
        OM(iom).invLxT11 = invLxT11;
        OM(iom).invLxT22 = invLxT22;
        OM(iom).invLz11  = invLz11;
        OM(iom).invLz22  = invLz22;
        OM(iom).invLzT11 = invLzT11;
        OM(iom).invLzT22 = invLzT22;

        % Dx210om, Dx120om, Dx110om, Dx220om
        % Dy210om, Dy120om, Dy110om, Dy220om

        basis_Nx1 = setup_basis(Nx1,px1);
        basis_Nx2 = setup_basis(Nx2,px2);
        basis_Nz1 = setup_basis(Nz1,pz1);
        basis_Nz2 = setup_basis(Nz2,pz2);
        % connected left domain
        iNbr_mo = OM(iom).iNbr_mo;
        if iNbr_mo~=0

            flip = OM(iom).iFace_mo;
            if abs(flip)==1 || abs(flip)==2    % get z from z; so Dzz
                Mz1  = OM(iNbr_mo).Nz1;   Mz2  = Mz1 - 1;
                pMz1 = OM(iNbr_mo).pz1;   pMz2 = pMz1 - 1;
                basis_Mz1 = setup_basis(Mz1,pMz1);
                basis_Mz2 = setup_basis(Mz2,pMz2);
                OM(iom).Dzz210mo = inner_product2(basis_Nz2,basis_Mz1);
                OM(iom).Dzz120mo = inner_product2(basis_Nz1,basis_Mz2);
                OM(iom).Dzz110mo = inner_product2(basis_Nz1,basis_Mz1);
                OM(iom).Dzz220mo = inner_product2(basis_Nz2,basis_Mz2);
            else % flip = 3 or 4, % get z from x; so Dzx       
                Mx1  = OM(iNbr_mo).Nx1;  Mx2  = Mx1 - 1;
                pMx1 = OM(iNbr_mo).px1;  pMx2 = pMx1 - 1;
                basis_Mx1 = setup_basis(Mx1,pMx1);
                basis_Mx2 = setup_basis(Mx2,pMx2);
                OM(iom).Dzx210mo = inner_product2(basis_Nz2,basis_Mx1);
                OM(iom).Dzx120mo = inner_product2(basis_Nz1,basis_Mx2);
                OM(iom).Dzx110mo = inner_product2(basis_Nz1,basis_Mx1);
                OM(iom).Dzx220mo = inner_product2(basis_Nz2,basis_Mx2);             
            end

        end


        % connected right domain
        iNbr_po = OM(iom).iNbr_po;
        if iNbr_po~=0

            flip = OM(iom).iFace_po;
            if abs(flip)==1 || abs(flip)==2  % get z from z; so Dzz         
                Mz1  = OM(iNbr_po).Nz1;   Mz2  = Mz1 - 1;
                pMz1 = OM(iNbr_po).pz1;   pMz2 = pMz1 - 1;
                basis_Mz1 = setup_basis(Mz1,pMz1);
                basis_Mz2 = setup_basis(Mz2,pMz2);
                OM(iom).Dzz210po = inner_product2(basis_Nz2,basis_Mz1);
                OM(iom).Dzz120po = inner_product2(basis_Nz1,basis_Mz2);
                OM(iom).Dzz110po = inner_product2(basis_Nz1,basis_Mz1);
                OM(iom).Dzz220po = inner_product2(basis_Nz2,basis_Mz2);
            else % flip = 3 or 4,          % get z from x; so Dzx         
                Mx1  = OM(iNbr_po).Nx1;  Mx2  = Mx1 - 1;
                pMx1 = OM(iNbr_po).px1;  pMx2 = pMx1 - 1;
                basis_Mx1 = setup_basis(Mx1,pMx1);
                basis_Mx2 = setup_basis(Mx2,pMx2);
                OM(iom).Dzx210po = inner_product2(basis_Nz2,basis_Mx1);
                OM(iom).Dzx120po = inner_product2(basis_Nz1,basis_Mx2);
                OM(iom).Dzx110po = inner_product2(basis_Nz1,basis_Mx1);
                OM(iom).Dzx220po = inner_product2(basis_Nz2,basis_Mx2);          
            end


        end


        % connected lower domain
        iNbr_om = OM(iom).iNbr_om;
        if iNbr_om~=0

            flip = OM(iom).iFace_om;
            if abs(flip)==3 || abs(flip)==4   % get x from x; so Dxx 
                Mx1  = OM(iNbr_om).Nx1;  Mx2  = Mx1 - 1;
                pMx1 = OM(iNbr_om).px1;  pMx2 = pMx1 - 1;
                basis_Mx1 = setup_basis(Mx1,pMx1);
                basis_Mx2 = setup_basis(Mx2,pMx2);
                OM(iom).Dxx210om = inner_product2(basis_Nx2,basis_Mx1);
                OM(iom).Dxx120om = inner_product2(basis_Nx1,basis_Mx2);
                OM(iom).Dxx110om = inner_product2(basis_Nx1,basis_Mx1);
                OM(iom).Dxx220om = inner_product2(basis_Nx2,basis_Mx2);
            else % flip = 1 or 2             % get x from z; so Dxz
                Mz1  = OM(iNbr_om).Nz1;  Mz2  = Mz1 - 1;
                pMz1 = OM(iNbr_om).pz1;  pMz2 = pMz1 - 1;
                basis_Mz1 = setup_basis(Mz1,pMz1);
                basis_Mz2 = setup_basis(Mz2,pMz2);
                OM(iom).Dxz210om = inner_product2(basis_Nx2,basis_Mz1);
                OM(iom).Dxz120om = inner_product2(basis_Nx1,basis_Mz2);
                OM(iom).Dxz110om = inner_product2(basis_Nx1,basis_Mz1);
                OM(iom).Dxz220om = inner_product2(basis_Nx2,basis_Mz2);
            end

        end


        % connected upper domain
        iNbr_op = OM(iom).iNbr_op;
        if iNbr_op~=0

            flip = OM(iom).iFace_op;
            if abs(flip)==3 || abs(flip)==4     % get x from x; so Dxx
                Mx1  = OM(iNbr_op).Nx1;  Mx2  = Mx1 - 1;
                pMx1 = OM(iNbr_op).px1;  pMx2 = pMx1 - 1;
                basis_Mx1 = setup_basis(Mx1,pMx1);
                basis_Mx2 = setup_basis(Mx2,pMx2);
                OM(iom).Dxx210op = inner_product2(basis_Nx2,basis_Mx1);
                OM(iom).Dxx120op = inner_product2(basis_Nx1,basis_Mx2);
                OM(iom).Dxx110op = inner_product2(basis_Nx1,basis_Mx1);
                OM(iom).Dxx220op = inner_product2(basis_Nx2,basis_Mx2);
            else % flip = 1 or 2               % get x from z; so Dxz
                Mz1  = OM(iNbr_op).Nz1;  Mz2  = Mz1 - 1;
                pMz1 = OM(iNbr_op).pz1;  pMz2 = pMz1 - 1;
                basis_Mz1 = setup_basis(Mz1,pMz1);
                basis_Mz2 = setup_basis(Mz2,pMz2);
                OM(iom).Dxz210op = inner_product2(basis_Nx2,basis_Mz1);
                OM(iom).Dxz120op = inner_product2(basis_Nx1,basis_Mz2);
                OM(iom).Dxz110op = inner_product2(basis_Nx1,basis_Mz1);
                OM(iom).Dxz220op = inner_product2(basis_Nx2,basis_Mz2);
            end

        end



    end

end

