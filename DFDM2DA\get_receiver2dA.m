function [rece] = get_receiver2dA(OM,dt,nt)
% GET_RECEIVER Summary of this function goes here

% receiver side
rece = create_Receiver2dA();
rece.ur = zeros(nt,3); % for benchmark
rece.ur(:,1) = (1:nt)'*dt;


rece.iom = 1;
rom = rece.iom;

% to be used based on the given global coordinates.
% nx = OM(rom).model2dA.nx;
% nz = OM(rom).model2dA.nz;
% xa = OM(rom).model2dA.xa;
% za = OM(rom).model2dA.za;
% xglobal and zglobal from input file.

rece.xlocal = 1.0;
rece.zlocal = 1.0;

xs = rece.xlocal;
zs = rece.zlocal;

%
Nx1 = OM(rom).Nx1;  Nx2 = Nx1 - 1;
Nz1 = OM(rom).Nz1;  Nz2 = Nz1 - 1;
%
px1 = OM(rom).px1;  px2 = px1 - 1;
pz1 = OM(rom).pz1;  pz2 = pz1 - 1;
%
kx1 = px1 + 1;  
kz1 = pz1 + 1; 
kx2 = px2 + 1;
kz2 = pz2 + 1;
%
tx1 = Get_Knot_Vector(Nx1,kx1);
tx2 = Get_Knot_Vector(Nx2,kx2);
tz1 = Get_Knot_Vector(Nz1,kz1);
tz2 = Get_Knot_Vector(Nz2,kz2);
%
rbx1  = zeros(1,Nx1);
rbx2  = zeros(1,Nx2);
rbz1  = zeros(1,Nz1);
rbz2  = zeros(1,Nz2);
for i = 1:Nx1
    rbx1(i) = bspln(tx1,Nx1,i,kx1,xs);
end
for i = 1:Nx2
    rbx2(i) = bspln(tx2,Nx2,i,kx2,xs);
end
for j = 1:Nz1
    rbz1(j) = bspln(tz1,Nz1,j,kz1,zs);
end
for j = 1:Nz2
    rbz2(j) = bspln(tz2,Nz2,j,kz2,zs);
end

rece.rbx1 = rbx1;
rece.rbx2 = rbx2;
rece.rbz1 = rbz1;
rece.rbz2 = rbz2;

end

