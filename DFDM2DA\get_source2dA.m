function [source] = get_source2dA(OM,freq,dt,nt)
%GENERATE_SOURCE Summary of this function goes here

% source side

% we need the global coordinates help 
% to calculate the reference element and local coordinates

source(1,1) = create_source2d();
source.iom = 3;
som = source.iom;

source.xlocal = 0.5;
source.zlocal = 0.5;

xs = source.xlocal;
zs = source.zlocal;

% to be used based on the given global coordinates.
% nx = OM(som).model2dA.nx;
% nz = OM(som).model2dA.nz;
% xa = OM(som).model2dA.xa;
% za = OM(som).model2dA.za;
%

%
Nx1 = OM(som).Nx1;  Nx2 = Nx1 - 1;
Nz1 = OM(som).Nz1;  Nz2 = Nz1 - 1;
%
px1 = OM(som).px1;   px2 = px1 - 1;
pz1 = OM(som).pz1;   pz2 = pz1 - 1;
%
kx1 = px1 + 1;  
kz1 = pz1 + 1; 
kx2 = px2 + 1;
kz2 = pz2 + 1;
%
tx1 = Get_Knot_Vector(Nx1,kx1);
tx2 = Get_Knot_Vector(Nx2,kx2);
tz1 = Get_Knot_Vector(Nz1,kz1);
tz2 = Get_Knot_Vector(Nz2,kz2);
%
sgbx1  = zeros(Nx1,1);
sgbx2  = zeros(Nx2,1);
sgbz1  = zeros(Nz1,1);
sgbz2  = zeros(Nz2,1);
for i = 1:Nx1
    sgbx1(i) = bspln(tx1,Nx1,i,kx1,xs);
end
for i = 1:Nx2
    sgbx2(i) = bspln(tx2,Nx2,i,kx2,xs);
end
for j = 1:Nz1
    sgbz1(j) = bspln(tz1,Nz1,j,kz1,zs);
end
for j = 1:Nz2
    sgbz2(j) = bspln(tz2,Nz2,j,kz2,zs);
end
%

%
invLx11 = OM(som).invLx11;
invLx22 = OM(som).invLx22;
invLz11 = OM(som).invLz11;
invLz22 = OM(som).invLz22;


% % source time function
% Source in Grid 1
invMsg12  = zeros(Nx1,Nz2);
sgbx_inv1 = invLx11*sgbx1;
sgbz_inv2 = invLz22*sgbz2;
for i = 1:Nx1
    for j = 1:Nz2
        invMsg12(i,j) = sgbx_inv1(i)*sgbz_inv2(j);
    end
end
% Source in Grid 2
invMsg21  = zeros(Nx2,Nz1);
sgbx_inv2 = invLx22*sgbx2;
sgbz_inv1 = invLz11*sgbz1;
for i = 1:Nx2
    for j = 1:Nz1
        invMsg21(i,j) = sgbx_inv2(i)*sgbz_inv1(j);
    end
end
source.invMsg12 = invMsg12;
source.invMsg21 = invMsg21;
%

% source time function
re = pi*freq*((1:nt)*dt-1.5/freq);
Ft = 1e5*(1-2*re.^2).*exp(-re.^2);

% Ft = zeros(nt,1); Ft(1) = 1e15;
source.Ft = Ft;

end

