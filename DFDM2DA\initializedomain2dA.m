function OM = initializedomain2dA(OM,nt)
%INITIALIZEDOMAINSTATE1D Summary of this function goes here
%   Detailed explanation goes here
ndomains = length(OM);

for iom = 1:ndomains
    fprintf("... Initialize the state of %d Domain ... \n",iom);
    Nx1 = OM(iom).Nx1;
    Nz1 = OM(iom).Nz1;
    Nx2 = Nx1 - 1;
    Nz2 = Nz1 - 1;

    % Acceleration
    OM(iom).state.dU2dtt12  = zeros(Nx1,Nz2);
    OM(iom).state.dU2dtt21  = zeros(Nx2,Nz1);
    
    % Displacement
    % U12
    OM(iom).state.U12_0    = zeros(Nx1,Nz2);   
    OM(iom).state.U12_1    = zeros(Nx1,Nz2);   
    OM(iom).state.U12      = zeros(Nx1,Nz2);
    % U21
    OM(iom).state.U21_0    = zeros(Nx2,Nz1);
    OM(iom).state.U21_1    = zeros(Nx2,Nz1);   
    OM(iom).state.U21      = zeros(Nx2,Nz1);   
            
    % Displacement boundary values left + right
    % U12
    OM(iom).state.U12mo     = zeros(1,Nz2);
    OM(iom).state.U12po     = zeros(1,Nz2);
    OM(iom).state.U12mo_inn = zeros(1,Nz2);
    OM(iom).state.U12po_inn = zeros(1,Nz2);
    OM(iom).state.U12mo_out = zeros(1,Nz2);
    OM(iom).state.U12po_out = zeros(1,Nz2);
    % U21
    OM(iom).state.U21mo     =  zeros(1,Nz1);
    OM(iom).state.U21po     =  zeros(1,Nz1);
    OM(iom).state.U21mo_inn =  zeros(1,Nz1);
    OM(iom).state.U21po_inn =  zeros(1,Nz1);
    OM(iom).state.U21mo_out =  zeros(1,Nz1);
    OM(iom).state.U21po_out =  zeros(1,Nz1);
    % Displacement boundary values bottom + upper
    % U12
    OM(iom).state.U12om     = zeros(Nx1,1);
    OM(iom).state.U12op     = zeros(Nx1,1);
    OM(iom).state.U12om_inn = zeros(Nx1,1);
    OM(iom).state.U12op_inn = zeros(Nx1,1);
    OM(iom).state.U12om_out = zeros(Nx1,1);
    OM(iom).state.U12op_out = zeros(Nx1,1);
    % U21
    OM(iom).state.U21om     =  zeros(Nx2,1);
    OM(iom).state.U21op     =  zeros(Nx2,1);
    OM(iom).state.U21om_inn =  zeros(Nx2,1);
    OM(iom).state.U21op_inn =  zeros(Nx2,1);
    OM(iom).state.U21om_out =  zeros(Nx2,1);
    OM(iom).state.U21op_out =  zeros(Nx2,1);

    
    % Stress 
    % S11
    OM(iom).state.Sxx11    = zeros(Nx1,Nz1); 
    OM(iom).state.Szz11    = zeros(Nx1,Nz1);
    % 22   
    OM(iom).state.Sxx22    = zeros(Nx2,Nz2);
    OM(iom).state.Szz22    = zeros(Nx2,Nz2);
     
    % S boundary values 
    % left + right
    OM(iom).state.Sxx11mo     = zeros(1,Nz1);
    OM(iom).state.Sxx11po     = zeros(1,Nz1);
    OM(iom).state.Sxx11mo_inn = zeros(1,Nz1);
    OM(iom).state.Sxx11po_inn = zeros(1,Nz1);
    OM(iom).state.Sxx11mo_out = zeros(1,Nz1);
    OM(iom).state.Sxx11po_out = zeros(1,Nz1);

    OM(iom).state.Sxx11om_inn = zeros(Nx1,1);
    OM(iom).state.Sxx11op_inn = zeros(Nx1,1);

    % left + right
    OM(iom).state.Sxx22mo     = zeros(1,Nz2);
    OM(iom).state.Sxx22po     = zeros(1,Nz2);
    OM(iom).state.Sxx22mo_inn = zeros(1,Nz2);
    OM(iom).state.Sxx22po_inn = zeros(1,Nz2);
    OM(iom).state.Sxx22mo_out = zeros(1,Nz2);
    OM(iom).state.Sxx22po_out = zeros(1,Nz2); 

    OM(iom).state.Sxx22om_inn = zeros(Nx2,1);
    OM(iom).state.Sxx22op_inn = zeros(Nx2,1);
    
     % S boundary values 
    % bottom + upper
    OM(iom).state.Szz11om     = zeros(Nx1,1);
    OM(iom).state.Szz11op     = zeros(Nx1,1);
    OM(iom).state.Szz11om_inn = zeros(Nx1,1);
    OM(iom).state.Szz11op_inn = zeros(Nx1,1);
    OM(iom).state.Szz11om_out = zeros(Nx1,1);
    OM(iom).state.Szz11op_out = zeros(Nx1,1);

    OM(iom).state.Szz11mo_inn = zeros(1,Nz1);
    OM(iom).state.Szz11po_inn = zeros(1,Nz1);

    % lower + upper
    OM(iom).state.Szz22om     = zeros(Nx2,1);
    OM(iom).state.Szz22op     = zeros(Nx2,1);
    OM(iom).state.Szz22om_inn = zeros(Nx2,1);
    OM(iom).state.Szz22op_inn = zeros(Nx2,1);
    OM(iom).state.Szz22om_out = zeros(Nx2,1);
    OM(iom).state.Szz22op_out = zeros(Nx2,1);

    OM(iom).state.Szz22mo_inn = zeros(1,Nz2);
    OM(iom).state.Szz22po_inn = zeros(1,Nz2);

    OM(iom).state.Umid = zeros(Nx1,Nz1,floor(nt));

end

for iom = 1:ndomains

    fprintf("... set the alpha values of %d Domain ... \n",iom);
 
    % alpha_in = 0.5;
    % alpha_bd = 0.5;

    iNbr_mo = OM(iom).iNbr_mo;
    iNbr_po = OM(iom).iNbr_po;
    iNbr_om = OM(iom).iNbr_om;
    iNbr_op = OM(iom).iNbr_op;
    
    if  iNbr_mo == 0
        OM(iom).alpha_mo = 0;
    else
        OM(iom).alpha_mo = 0.5;
    end

    if iNbr_po == 0
        OM(iom).alpha_po = 0;
    else
        OM(iom).alpha_po = 0.5;
    end

    if iNbr_om == 0
        OM(iom).alpha_om = 0;
    else
        OM(iom).alpha_om = 0.5;
    end

    if iNbr_op == 0
        OM(iom).alpha_op = 0;
    else
        OM(iom).alpha_op = 0.5;
    end

end


end

