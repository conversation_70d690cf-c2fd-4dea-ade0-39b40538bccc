function OM = mesh_regional2d(dx,freq,ppw)
% MESH_REGIONAL2D Summary of this function goes here

% center of the circular earth is the origin
R_Earth    = 6371e3;   % the radius of the earth
Len_degree = 6; % the length of the array 2D
Len_rad    = Len_degree/360*2*pi;
thickness  = 1110e3/5*3;
R1 = R_Earth - thickness;

nex = 3;
nez = 3;

ndomain = nex*nez;

OM(ndomain, 1) = create_domain2dA();


% model parameter
Vp  = 2750;   % [m/s]
rho = 2000;   % [kg/m^3]
mu  = rho*Vp*Vp;

fmax     = freq*2.75;
mini_wl= Vp/fmax;

% Physical parameters
Lx_top = Len_rad*R_Earth;
Lx_btm = Len_rad*R1;
Lx_middle = (Lx_top + Lx_btm)/2;

nax = ceil(Lx_middle/mini_wl*ppw);
naz = ceil(thickness/mini_wl*ppw);

Nx1 = ceil(nax/nex);
Nz1 = ceil(naz/nez);
Nx1 = 41;
Nz1 = 41;

nx_tot = ceil(Lx_middle/dx);
nz_tot = ceil(thickness/dx);

nx   = ceil(nx_tot/nex);
nz   = ceil(nz_tot/nez);
nx = 81;
nz = 81;
naxt = nex*(nx-1)+1;
nazt = nez*(nz-1)+1;

degreet = (Len_rad/2:-Len_rad/(naxt-1):-Len_rad/2)+pi/2;
rdistzt = (0:thickness/(nazt-1):thickness)+R1;
% rdistxt = (0:thicknessx/(naxt-1):thicknessx);
xa = zeros(nx,nz);
za = zeros(nx,nz);
for ielez = 1:nez
    for ielex = 1:nex
        iom = ielex + (ielez-1)*nex;  
        for jz=1:nz
            for ix=1:nx
                iax = (ielex-1)*(nx-1) + ix;
                jaz = (ielez-1)*(nz-1) + jz;
                xa(ix,jz) = rdistzt(jaz)*cos(degreet(iax));
                za(ix,jz) = rdistzt(jaz)*sin(degreet(iax));
            end
        end
        OM(iom).model2dA.xa   =  xa;
        OM(iom).model2dA.za   =  za;
    end
end

for iom = 1:ndomain
    OM(iom).Nx1   =  Nx1;
    OM(iom).Nz1   =  Nz1;
    OM(iom).model2dA.nx   =  nx;
    OM(iom).model2dA.nz   =  nz;
    OM(iom).model2dA.rho  =  rho*ones(nx,nz);
    OM(iom).model2dA.mu   =  mu*ones(nx,nz);
end


end

