function OM = mesh_sphere2dA(rad,dx,nparts,freq,ppw)

    inflat  = 0.44; % to inflate the central square.

    vp  = 2750;
    vs  = 0.0;
    rho = 2000;

    mu = rho*vp*vp;

    fmax = freq*3;
    nmin = 3;

    len_rad = length(rad);
    allrad  = zeros(1,2*len_rad+nparts-1);

    allrad(1:len_rad) = -rad(len_rad:-1:1);
    for ib = 1:nparts-1
        allrad(len_rad+ib)=ib/nparts*2*rad(1)-rad(1);
    end
    allrad(len_rad+nparts:end) = rad;


    len_allrad = length(allrad);

    ndomain       = (len_allrad-1)^2-4*(len_rad-1)^2;
    OM(ndomain, 1) = create_domain2dA();

    dtheta = 2*pi/(nparts*4);

    iom=0;
    % for ipass = 1 
        for jb=1:(len_allrad-1)     % number block in z direction
            for ib=1:(len_allrad-1) % number block in x direction

                if     (jb<len_rad-1  && ib>=len_rad && ib<len_rad+nparts)
                    region = 1; % bottom
                elseif (jb==len_rad-1 && ib>=len_rad && ib<len_rad+nparts)
                    region = 2; % bottom innermost sphere
                elseif (ib<len_rad-1  && jb>=len_rad && jb<len_rad+nparts)
                    region = 3; % left
                elseif (ib==len_rad-1 && jb>=len_rad && jb<len_rad+nparts)
                    region = 4; % left innermost sphere
                elseif (ib>=len_rad   && ib<len_rad+nparts && jb>=len_rad && jb<len_rad+nparts )
                    region = 5; % center cube innermost sphere
                elseif (ib==len_rad+nparts && jb>=len_rad && jb<len_rad+nparts)
                    region = 6; % right inner sphere
                elseif (ib>len_rad+nparts  && jb>=len_rad && jb<len_rad+nparts)
                    region = 7; % right
                elseif (jb==len_rad+nparts && ib>=len_rad && ib<len_rad+nparts)
                    region = 8; % top inner sphere
                elseif (jb>len_rad+nparts  && ib>=len_rad && ib<len_rad+nparts)
                    region = 9; % top
                else
                    region = 0;
                end

                switch region
                    case {1,2,8,9}

                        fprintf('...mesh the region %d...\n',region);
                        iom = iom +1;

                        r1 = allrad(jb);
                        r2 = allrad(jb+1);

                        aR = abs(r2+r1)/2;
                        dR = abs(r2-r1);
                        nx = max(3, ceil( (aR*pi/2)/nparts/dx) );
                        nz = max(3, ceil(  dR/dx) );
                        fprintf('...nx,nz=%d,%d...\n',nx,nz);


                        xa  = zeros(nx,nz);
                        za  = zeros(nx,nz);

                        % % for x_tmp and z_tmp
                        if region==1 || region==2
                            theta1 = 5*pi/4 + (ib  -len_rad)/nparts * pi/2; % angle from the positive x
                            theta2 = 5*pi/4 + (ib+1-len_rad)/nparts * pi/2;
                        end

                        if region==8 || region==9
                            theta1 = 3*pi/4 - (ib  -len_rad)/nparts * pi/2;
                            theta2 = 3*pi/4 - (ib+1-len_rad)/nparts * pi/2;
                        end
                        % %

                        for jj = 1:nz
                            for ii = 1:nx

                                theta = (ii-1)/(nx-1) * (theta2-theta1) + theta1;
                                r     = (jj-1)/(nz-1) * (r2-r1) + r1;
                                rabs  = abs(r);
                                xtmp = rabs*cos(theta);
                                ztmp = rabs*sin(theta);

                                % % for xp and zp
                                zs = r/sqrt(2);
                                if region==1 || region==2
                                    coeff = 1-2*( (ib-len_rad) + (ii-1)/(nx-1) )/nparts; % ratio xp/zp
                                end
                                if region==8  || region==9
                                    coeff =-1+2*( (ib-len_rad) + (ii-1)/(nx-1) )/nparts;
                                end
                                xs = coeff*zs;

                                if region==2 || region==8
                                    alpha     = (1-inflat) * (1- (rabs-rad(1))/(rad(2)-rad(1)) );
                                    xa(ii,jj) = alpha * xs + (1-alpha)*xtmp;
                                    za(ii,jj) = alpha * zs + (1-alpha)*ztmp;
                                else
                                    xa(ii,jj) = xtmp;
                                    za(ii,jj) = ztmp;
                                end

                            end
                        end

                        % info about the input model
                        rho_tmp = zeros(nx,nz);
                        mu_tmp  = zeros(nx,nz);


                        rs = sqrt(xa.^2+za.^2);
                        rmax = max(rs(:))*(1-10*eps);
                        rmin = min(rs(:))*(1+10*eps);
                        
                        vpmax = -9999999;
                        vpmin =  9999999;
                        vsmax = -9999999;
                        vsmin =  9999999;
                        
                        nnx = nmin;
                        nnz = nmin;
 
                        
                        % for jj = 1:nz
                        %     for ii = 1:nx
                        %         r0  = sqrt(xc(ii,jj,kk)^2+yc(ii,jj,kk)^2+zc(ii,jj,kk)^2);
                        %         r   = min( max(rmin, r0 ), rmax );
                        %         rho = density_prem(r/1000,'c')*1000;
                        %         vp  = vp_prem(r/1000,'c')*1000;
                        %         vs  = vs_prem(r/1000,'c')*1000;
                        %         mu     = vs*vs*rho;
                        %         lambda = vp*vp*rho -2*mu;
                        %         rho_tmp(ii,jj) = rho;
                        %         mu_tmp(ii,jj)  = mu;
                        % 
                        %         vpmax = max(vp,vpmax);
                        %         vsmax = max(vs,vsmax);
                        %         vpmin = min(vp,vpmin);
                        %         vsmin = min(vs,vsmin);
                        % 
                        %         % vmin = vsmin;
                        %         if vs > 10*eps
                        %             vmin = vpmin;
                        %         else
                        %             vmin = vsmin;
                        %         end
                        %         wavemin = vmin/(fmax);
                        %         nnx = max(nnx,ceil(dtheta*r/wavemin*ppw));
                        %         nnz = max(nnz,ceil((rmax-rmin)/wavemin*ppw));    
                        %     end
                        % end
                        
                        for jj = 1:nz
                            for ii = 1:nx
                                r0  = sqrt(xa(ii,jj)^2+za(ii,jj)^2);
                                r   = min( max(rmin, r0 ), rmax );
                                
                                vpmin = vp;
                                vsmin = vs;

                                rho_tmp(ii,jj) = rho;
                                mu_tmp(ii,jj) = mu;
                              
                                % vmin = vsmin;
                                % if vs > 10*eps
                                %     vmin = vpmin;
                                % else
                                %     vmin = vsmin;
                                % end
                                vmin = vp;
                                wavemin = vmin/(fmax);
                                nnx = max(nnx,ceil(dtheta*r/wavemin*ppw));
                                nnz = max(nnz,ceil((rmax-rmin)/wavemin*ppw));    
                            end
                        end
 

                        OM(iom).model2dA.nx   =  nx;
                        OM(iom).model2dA.nz   =  nz;
                        OM(iom).model2dA.xa   =  xa;
                        OM(iom).model2dA.za   =  za;
                        OM(iom).model2dA.rho  =  rho_tmp;
                        OM(iom).model2dA.mu   =  mu_tmp;

                        OM(iom).Nx1 = nnx;
                        OM(iom).Nz1 = nnz;
                        % OM(iom).Nx1 = 63;
                        % OM(iom).Nz1 = 63;

                    case {3,4,6,7}
                        fprintf('...mesh the region %d...\n',region);
                        iom = iom +1;
                        r1 = allrad(ib);
                        r2 = allrad(ib+1);
                        aR = abs(r2+r1)/2;
                        dR = abs(r2-r1);

                        nx  = max(3, ceil(  dR/dx)             );
                        nz  = max(3, ceil( (aR*pi/2)/nparts/dx) );


                        fprintf('...nx,nz=%d,%d...\n',nx,nz);

                        xa  = zeros(nx,nz);
                        za  = zeros(nx,nz);

                        if region==3 || region==4
                            theta1 = 5*pi/4 - (jb  -len_rad)/nparts * pi/2;
                            theta2 = 5*pi/4 - (jb+1-len_rad)/nparts * pi/2;
                        end
                        if region==6 || region==7
                            theta1 = 7*pi/4 + (jb  -len_rad)/nparts * pi/2;
                            theta2 = 7*pi/4 + (jb+1-len_rad)/nparts * pi/2;
                        end

                        for jj = 1:nz
                            for ii = 1:nx

                                theta = (jj-1)/(nz-1) * (theta2-theta1) + theta1;
                                r     = (ii-1)/(nx-1) * (r2-r1) + r1;
                                rabs    = abs(r);
                                xtmp = rabs*cos(theta);
                                ztmp = rabs*sin(theta);

                                % xp and zp
                                xs = r/sqrt(2);
                                if region==6 || region==7
                                    coeff =-1+2*( (jb-len_rad) + (jj-1)/(nz-1) )/nparts;
                                end
                                if region==3 || region==4
                                    coeff = 1-2*( (jb-len_rad) + (jj-1)/(nz-1) )/nparts;
                                end
                                zs = xs*coeff;

                                if region==4 || region==6
                                    alpha = (1-inflat) * (1- (rabs-rad(1))/(rad(2)-rad(1)) );
                                    xa(ii,jj) = alpha * xs + (1-alpha)*xtmp;
                                    za(ii,jj) = alpha * zs + (1-alpha)*ztmp;
                                else
                                    xa(ii,jj) = xtmp;
                                    za(ii,jj) = ztmp;
                                end

                            end
                        end

                        % info about the input model
                        rho_tmp = zeros(nx,nz);
                        mu_tmp = zeros(nx,nz);
                        

                        rs = sqrt(xa.^2+za.^2);
                        rmax = max(rs(:))*(1-10*eps);
                        rmin = min(rs(:))*(1+10*eps);
                        vpmax = -9999999;
                        vpmin =  9999999;
                        vsmax = -9999999;
                        vsmin =  9999999;
                        
                        nnx = nmin;
                        nnz = nmin;
 
                        
                        % for jj = 1:nz
                        %     for ii = 1:nx
                        %         r0  = sqrt(xc(ii,jj,kk)^2+yc(ii,jj,kk)^2+zc(ii,jj,kk)^2);
                        %         r   = min( max(rmin, r0 ), rmax );
                        %         rho = density_prem(r/1000,'c')*1000;
                        %         vp  = vp_prem(r/1000,'c')*1000;
                        %         vs  = vs_prem(r/1000,'c')*1000;
                        %         mu     = vs*vs*rho;
                        %         lambda = vp*vp*rho -2*mu;
                        %         rho_tmp(ii,jj) = rho;
                        %         C11_tmp(ii,jj) = lambda+2*mu; 
                        %         C12_tmp(ii,jj) = lambda;
                        %         C13_tmp(ii,jj) = lambda;
                        %         C22_tmp(ii,jj) = lambda+2*mu;
                        %         C23_tmp(ii,jj) = lambda;
                        %         C33_tmp(ii,jj) = 2*mu;
                        % 
                        %         vpmax = max(vp,vpmax);
                        %         vsmax = max(vs,vsmax);
                        %         vpmin = min(vp,vpmin);
                        %         vsmin = min(vs,vsmin);
                        % 
                        %         % vmin = vsmin;
                        %         if vs > 10*eps
                        %             vmin = vpmin;
                        %         else
                        %             vmin = vsmin;
                        %         end
                        %         wavemin = vmin/(fmax);
                        %         nnz = max(nnz,ceil(dtheta*r/wavemin*ppw));
                        %         nnx = max(nnx,ceil((rmax-rmin)/wavemin*ppw));    
                        %     end
                        % end
                        
 
                        for jj = 1:nz
                            for ii = 1:nx
                                r0  = sqrt(xa(ii,jj)^2+za(ii,jj)^2);
                                r   = min( max(rmin, r0 ), rmax );
                                
                                vpmin = vp;
                                vsmin = vs;
                                rho_tmp(ii,jj) = rho;
                                mu_tmp(ii,jj) = mu;
                              
                                % vmin = vsmin;
                                % if vs > 10*eps
                                %     vmin = vpmin;
                                % else
                                %     vmin = vsmin;
                                % end
                                vmin = vp;
                                wavemin = vmin/(fmax);
                                nnz = max(nnz,ceil(dtheta*r/wavemin*ppw));
                                nnx = max(nnx,ceil((rmax-rmin)/wavemin*ppw));    
                            end
                        end

                        OM(iom).model2dA.nx   =  nx;
                        OM(iom).model2dA.nz   =  nz;
                        OM(iom).model2dA.xa   =  xa;
                        OM(iom).model2dA.za   =  za;
                        OM(iom).model2dA.rho  =  rho_tmp;
                        OM(iom).model2dA.mu   =  mu_tmp;

                        OM(iom).Nx1 = nnx;
                        OM(iom).Nz1 = nnz;
                        % OM(iom).Nx1 = 61;
                        % OM(iom).Nz1 = 61;

                    case 5
                        fprintf('...mesh the region %d...\n',region);
                        iom = iom+1;
                        rx1 = allrad(ib);
                        rx2 = allrad(ib+1);
                        nx = max(3, ceil( (rx2-rx1)/dx ));

                        rz1 = allrad(jb);
                        rz2 = allrad(jb+1);
                        nz = max(3, ceil( (rz2-rz1)/dx ));

                        fprintf('...nx,nz=%d,%d...\n',nx,nz);

                        xa = zeros(nx,nz);
                        za = zeros(nx,nz);

                        for jj = 1:nz
                            for ii = 1:nx
                                % square: xp and zp is the reference points tangent square
                                xs = ( (ii-1)/(nx-1)* (rx2-rx1) + rx1 );
                                zs = ( (jj-1)/(nz-1)* (rz2-rz1) + rz1 );

                                if     (zs<=-xs) && (zs<=xs) % bottom
                                    theta = (xs-zs)/abs(zs) * pi/4 + 5*pi/4;
                                    r     = abs(zs);
                                elseif (xs<=-zs) && (xs<=zs) % left
                                    theta = (xs-zs)/abs(xs) * pi/4 + 5*pi/4;
                                    r     = abs(xs);
                                elseif (xs>=-zs) && (xs>=zs) % right
                                    theta = (zs-xs)/abs(xs) * pi/4 + pi/4;
                                    r     = abs(xs);
                                elseif (zs>=-xs) && (zs>=xs) % up
                                    theta = (zs-xs)/abs(zs) * pi/4 + pi/4; % from x axis
                                    r     = abs(zs);
                                end

                                if (abs(r)<eps)
                                    r=0;
                                    theta=0;
                                end
                                
                                alpha = r/rad(1)*inflat;
                                % The reason why divided by sqrt(2),
                                % because it out tangent square sqrt(2) to 1.
                                xa(ii,jj) = (1-alpha)*xs/sqrt(2) + alpha*r*cos(theta);
                                za(ii,jj) = (1-alpha)*zs/sqrt(2) + alpha*r*sin(theta);

                            end
                        end

                        % info about the input model
                        rho_tmp = zeros(nx,nz);
                        mu_tmp  = zeros(nx,nz);
                        

                        rs = sqrt(xa.^2+za.^2);
                        rmax = max(rs(:))*(1-10*eps);
                        rmin = min(rs(:))*(1+10*eps);
                        vpmax = -9999999;
                        vpmin =  9999999;
                        vsmax = -9999999;
                        vsmin =  9999999;

                        lx = max(xa(:)) - min(xa(:));
                        lz = max(za(:)) - min(za(:));
                        
                        nnx = nmin;
                        nnz = nmin;
 
                        
                        % for jj = 1:nz
                        %     for ii = 1:nx
                        %         r0  = sqrt(xc(ii,jj,kk)^2+yc(ii,jj,kk)^2+zc(ii,jj,kk)^2);
                        %         r   = min( max(rmin, r0 ), rmax );
                        %         rho = density_prem(r/1000,'c')*1000;
                        %         vp  = vp_prem(r/1000,'c')*1000;
                        %         vs  = vs_prem(r/1000,'c')*1000;
                        %         mu     = vs*vs*rho;
                        %         lambda = vp*vp*rho -2*mu;
                        %         rho_tmp(ii,jj) = rho;
                        %         C11_tmp(ii,jj) = lambda+2*mu; 
                        %         C12_tmp(ii,jj) = lambda;
                        %         C13_tmp(ii,jj) = lambda;
                        %         C22_tmp(ii,jj) = lambda+2*mu;
                        %         C23_tmp(ii,jj) = lambda;
                        %         C33_tmp(ii,jj) = 2*mu;
                        % 
                        %         vpmax = max(vp,vpmax);
                        %         vsmax = max(vs,vsmax);
                        %         vpmin = min(vp,vpmin);
                        %         vsmin = min(vs,vsmin);
                        % 
                        %         % vmin = vsmin;
                        %         if vs > 10*eps
                        %             vmin = vpmin;
                        %         else
                        %             vmin = vsmin;
                        %         end
                        %         wavemin = vmin/(fmax);
                        %         nnx = max(nnx,ceil(lx/wavemin*ppw));
                        %         nnz = max(nnz,ceil(lz/wavemin*ppw));    
                        %     end
                        % end
                        
                        for jj = 1:nz
                            for ii = 1:nx
                                r0  = sqrt(xa(ii,jj)^2+za(ii,jj)^2);
                                r   = min( max(rmin, r0 ), rmax );
                                
                                vpmin = vp;
                                vsmin = vs;

                                rho_tmp(ii,jj) = rho;
                                mu_tmp(ii,jj) = mu;
                              
                                % vmin = vsmin;
                                % if vs > 10*eps
                                %     vmin = vpmin;
                                % else
                                %     vmin = vsmin;
                                % end
                                vmin = vp;
                                wavemin = vmin/fmax;
                                nnx = max(nnx,ceil(lx/wavemin*ppw));
                                nnz = max(nnz,ceil(lz/wavemin*ppw));    
                            end
                        end


                        OM(iom).model2dA.nx   =  nx;
                        OM(iom).model2dA.nz   =  nz;
                        OM(iom).model2dA.xa   =  xa;
                        OM(iom).model2dA.za   =  za;
                        OM(iom).model2dA.rho  =  rho_tmp;
                        OM(iom).model2dA.mu   =  mu_tmp;

                        OM(iom).Nx1 = nnx;
                        OM(iom).Nz1 = nnz;
                        % OM(iom).Nx1 = 51;
                        % OM(iom).Nz1 = 51;
                end

            end
        end
    % end
    
end


function [OM]=DFDM2D_connection(OM)

    ndomain = length(OM);
    xtmp = zeros(4,ndomain);
    ztmp = zeros(4,ndomain);
    
    for iom = 1:ndomain
        
        xtmp(1,iom)= OM(iom).model2dA.xa(1,1);
        xtmp(2,iom)= OM(iom).model2dA.xa(nx,1);
        xtmp(3,iom)= OM(iom).model2dA.xa(1,nz);
        xtmp(4,iom)= OM(iom).model2dA.xa(nx,nz);
    
        ztmp(1,iom)= OM(iom).model2dA.za(1,1);
        ztmp(2,iom)= OM(iom).model2dA.za(nx,1);
        ztmp(3,iom)= OM(iom).model2dA.za(1,nz);
        ztmp(4,iom)= OM(iom).model2dA.za(nx,nz);
    
    end

    % find neighboring domains
    % ndomain_nbrs
    % 3  4
    %
    % 1  2
    ndomain_iNbrs = zeros(4,ndomain);
    for idm = 1:ndomain
        fprintf('...find the neighbor idomain of the %d domain...\n',idm);
        for iFace=1:4
            if ndomain_iNbrs(iFace,idm)==0
                switch iFace
                    case 1
                        pt1 = 1;
                        pt2 = 3;
                    case 2
                        pt1 = 2;
                        pt2 = 4;
                    case 3
                        pt1 = 1;
                        pt2 = 2;
                    case 4
                        pt1 = 3;
                        pt2 = 4;
                end

                xmF = xtmp(pt1,idm);  xpF = xtmp(pt2,idm);
                zmF = ztmp(pt1,idm);  zpF = ztmp(pt2,idm);

                xm_mid = (xmF+xpF)/2;
                zm_mid = (zmF+zpF)/2;

                for jdm = 1:ndomain
                    if jdm~=idm
                        for jFace=1:4

                            switch jFace
                                case 1
                                    pt1 = 1;
                                    pt2 = 3;
                                case 2
                                    pt1 = 2;
                                    pt2 = 4;
                                case 3
                                    pt1 = 1;
                                    pt2 = 2;
                                case 4
                                    pt1 = 3;
                                    pt2 = 4;
                            end

                            xmFNbr = xtmp(pt1,jdm); xpFNbr = xtmp(pt2,jdm);
                            zmFNbr = ztmp(pt1,jdm); zpFNbr = ztmp(pt2,jdm);

                            xmNbr_mid = (xmFNbr+xpFNbr)/2;
                            zmNbr_mid = (zmFNbr+zpFNbr)/2;

                            if abs(xm_mid-xmNbr_mid)+abs(zm_mid-zmNbr_mid) <50
                                ndomain_iNbrs(jFace,jdm)=idm;
                                ndomain_iNbrs(iFace,idm)=jdm;
                                % break
                            end

                        end
                    end
                end
            end

        end
        OM(idm).iNbr_mo  = ndomain_nbrs(1,idm);
        OM(idm).iNbr_po  = ndomain_nbrs(2,idm);
        OM(idm).iNbr_om  = ndomain_nbrs(3,idm);
        OM(idm).iNbr_op  = ndomain_nbrs(4,idm);
    end

    ndomain_iFaces = zeros(4,ndomain);
    for idm = 1:ndomain
            % find the matched Face of the neighboring element with current element
        fprintf('...find the matched Face of the %d domain...\n',idm);
        for iFace=1:4
            dist = realmax;
            switch iFace

                case 1
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 1;
                    pt2  = 3;
                case 2
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 2;
                    pt2  = 4;
                case 3
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 1;
                    pt2  = 2;
                case 4
                    iNbr = ndomain_iNbrs(iFace,idm);
                    pt1  = 3;
                    pt2  = 4;
            end
            if iNbr~=0
            xmF = xtmp(pt1,idm); xpF = xtmp(pt2,idm);

                zmF = ztmp(pt1,idm); zpF = ztmp(pt2,idm);

                for jFace=1:4
                
                    switch jFace

                        case 1
                            pt1 = 1;
                            pt2 = 3;
                        case 2
                            pt1 = 2;
                            pt2 = 4;
                        case 3
                            pt1 = 1;
                            pt2 = 2;
                        case 4
                            pt1 = 3;
                            pt2 = 4;
                    end
                    xmFNbr = xtmp(pt1,iNbr); xpFNbr = xtmp(pt2,iNbr);
                    zmFNbr = ztmp(pt1,iNbr); zpFNbr = ztmp(pt2,iNbr);

                    dist1 = (xmF-xmFNbr)^2 + (xpF-xpFNbr)^2 + (zmF-zmFNbr)^2 +(zpF-zpFNbr)^2; 

                    if dist1 < dist
                        dist = dist1;
                        switch iFace
                        case 1
                            ndomain_iFaces(iFace,idm) = jFace;
                        case 2
                            ndomain_iFaces(iFace,idm) = jFace;
                        case 3
                            ndomain_iFaces(iFace,idm) = jFace;
                        case 4
                            ndomain_iFaces(iFace,idm) = jFace;
                        end  
                    end
                    dist1 = (xmF-xpFNbr)^2 + (xpF-xmFNbr)^2 + (zmF-zpFNbr)^2 +(zpF-zmFNbr)^2; 

                    if dist1 < dist
                        dist = dist1;
                        switch iFace
                        case 1
                            ndomain_iFaces(iFace,idm) = -jFace;
                        case 2
                            ndomain_iFaces(iFace,idm) = -jFace;
                        case 3
                            ndomain_iFaces(iFace,idm) = -jFace;
                        case 4
                            ndomain_iFaces(iFace,idm) = -jFace;
                        end  
                    end
                end
            end
        end
        OM(idm).iFace_mo = ndomain_iFaces(1,idm);
        OM(idm).iFace_po = ndomain_iFaces(2,idm);
        OM(idm).iFace_om = ndomain_iFaces(3,idm);
        OM(idm).iFace_op = ndomain_iFaces(4,idm);
    end

    % build the rotation matrix
    for idm =1:ndomain

        for icase=1:4
            switch icase
                case 1
                    iNbr  = ndomain_iNbrs (1,idm);
                    iFace = ndomain_iFaces(1,idm);
                    if iNbr~=0
                        switch abs(iFace)
                            case 1
                                ndomain_Faces_rotmo(1,1,idm) = -1;
                                ndomain_Faces_rotmo(2,2,idm) = sign(iFace);
                            case 2
                                ndomain_Faces_rotmo(1,1,idm) =  1; % 11 structural mesh
                                ndomain_Faces_rotmo(2,2,idm) = sign(iFace);
                            case 3
                                ndomain_Faces_rotmo(1,2,idm) = -1;
                                ndomain_Faces_rotmo(2,1,idm) = sign(iFace);
                            case 4
                                ndomain_Faces_rotmo(1,2,idm) =  1;
                                ndomain_Faces_rotmo(2,1,idm) = sign(iFace);
                        end
                    end
                case 2
                    iNbr  = ndomain_iNbrs (2,idm);
                    iFace = ndomain_iFaces(2,idm);
                    if iNbr~=0
                        switch abs(iFace)
                            case 1
                                ndomain_Faces_rotpo(1,1,idm) = 1; % 11 structural mesh
                                ndomain_Faces_rotpo(2,2,idm) = sign(iFace);
                            case 2
                                ndomain_Faces_rotpo(1,1,idm) = -1;
                                ndomain_Faces_rotpo(2,2,idm) = sign(iFace);
                            case 3
                                ndomain_Faces_rotpo(1,2,idm) = 1;
                                ndomain_Faces_rotpo(2,1,idm) = sign(iFace);
                            case 4
                                ndomain_Faces_rotpo(1,2,idm) = -1;
                                ndomain_Faces_rotpo(2,1,idm) =sign(iFace);
                        end
                    end
                case 3
                    iNbr  = ndomain_iNbrs (3,idm);
                    iFace = ndomain_iFaces(3,idm);
                    if iNbr~=0
                        switch abs(iFace)
                            case 1
                                ndomain_Faces_rotom(2,1,idm) = -1;
                                ndomain_Faces_rotom(1,2,idm) = sign(iFace);
                            case 2
                                ndomain_Faces_rotom(2,1,idm) = 1;
                                ndomain_Faces_rotom(1,2,idm) = sign(iFace);
                            case 3
                                ndomain_Faces_rotom(2,2,idm) = -1;
                                ndomain_Faces_rotom(1,1,idm) = sign(iFace);
                            case 4
                                ndomain_Faces_rotom(2,2,idm) = 1; % 22 structural mesh
                                ndomain_Faces_rotom(1,1,idm) = sign(iFace);
                        end
                    end
                case 4
                    iNbr  = ndomain_iNbrs (4,idm);
                    iFace = ndomain_iFaces(4,idm);
                    if iNbr~=0
                        switch abs(iFace)
                            case 1
                                ndomain_Faces_rotop(2,1,idm) = 1;
                                ndomain_Faces_rotop(1,2,idm) = sign(iFace);
                            case 2
                                ndomain_Faces_rotop(2,1,idm) = -1;
                                ndomain_Faces_rotop(1,2,idm) = sign(iFace);
                            case 3
                                ndomain_Faces_rotop(2,2,idm) = 1; % 22 structural mesh
                                ndomain_Faces_rotop(1,1,idm) = sign(iFace);
                            case 4
                                ndomain_Faces_rotop(2,2,idm) = -1;
                                ndomain_Faces_rotop(1,1,idm) = sign(iFace);
                        end
                    end
            end
        end
    end
    OM(idm).rot_mo = squeeze( ndomain_Faces_rotmo(:,:,idm) );
    OM(idm).rot_po = squeeze( ndomain_Faces_rotpo(:,:,idm) );
    OM(idm).rot_om = squeeze( ndomain_Faces_rotom(:,:,idm) );
    OM(idm).rot_op = squeeze( ndomain_Faces_rotop(:,:,idm) );
end


% function OM = refine_model2dA(OM,freq,ppw,model_type)
% 
%     fmax    = 3*freq;
%     ndomain = length(OM);
% 
%     if model_type ==1
%         % homogeneous model
%         Vp  = 5000; % m/s
%         Vs  = 2750; % m/s
%         rho = 2000; % kg/m^3;
%         for iom = 1:ndomain
%             nx = OM(iom).model2dA.nx;
%             nz = OM(iom).model2dA.nz;
% 
% 
%             OM(iom).x2d     = x2d;
%             OM(iom).z2d     = z2d;
%             OM(iom).rho     = rho;
%             OM(iom).mu      = mu;
%             OM(iom).lambda  = lambda;
%         end
% 
%     end
% 
%     if model_type == 2
%         % heterogenous model
% 
%         for iom = 1:ndomain
% 
%             OM(iom).x2d     = x2d;
%             OM(iom).z2d     = z2d;
%             OM(iom).rho     = rho;
%             OM(iom).mu      = mu;
%             OM(iom).lambda  = lambda;
%         end
% 
%     end
% 
%     if model_type == 3
%         % inner core model
% 
%         for iom = 1:ndomain
% 
%             OM(iom).x2d     = x2d;
%             OM(iom).z2d     = z2d;
%             OM(iom).rho     = rho;
%             OM(iom).mu      = mu;
%             OM(iom).lambda  = lambda;
%         end
% 
%     end
% 
%     if model_type == 4
%         % PREM Model
% 
%         for iom = 1:ndomain
% 
%             OM(iom).x2d     = x2d;
%             OM(iom).z2d     = z2d;
%             OM(iom).rho     = rho;
%             OM(iom).mu      = mu;
%             OM(iom).lambda  = lambda;
%         end
% 
%     end
% 
% 
% end




function density = density_prem(r, opt)
    % Return PREM density profile
    % r : radius (r=0 at the Earth's center)
    
    % Get density coefficients based on radius and option
    coefs = density_coefs_prem(r, opt);
    
    % Normalize radius
    x = r / 6371;
    
    % Calculate density using polynomial
    density = poly3(x, coefs);
end


function f=poly3(x, coefs)
    % 3-order polynomial degree
    f = coefs(1) + coefs(2)*x + coefs(3).*x.*x + coefs(4).*x.*x.*x;
end

function coefs = density_coefs_prem(r, opt)
    % Return PREM density polynomial coefficients
    % r : radius (r=0 at the Earth's center)
    
    % Determine layer index based on radius and option
    n = layer_index_prem(r, opt);
    
    % Initialize coefs array
    coefs = zeros(1, 4);
    
    % Assign coefficients based on layer index
    switch n
        case 1
            coefs = [13.0885, 0.0, -8.8381, 0.0];
        case 2
            coefs = [12.5815, -1.2638, -3.6426, -5.5281];
        case 3
            coefs = [7.9565, -6.4761, 5.5283, -3.0807];
        case 4
            coefs = [7.9565, -6.4761, 5.5283, -3.0807];
        case 5
            coefs = [7.9565, -6.4761, 5.5283, -3.0807];
        case 6
            coefs = [5.3197, -1.4836, 0.0, 0.0];
        case 7
            coefs = [11.2494, -8.0298, 0.0, 0.0];
        case 8
            coefs = [7.1089, -3.8045, 0.0, 0.0];
        case 9
            coefs = [2.6910, 0.6924, 0.0, 0.0];
        case 10
            coefs = [2.6910, 0.6924, 0.0, 0.0];
        case 11
            coefs = [2.9, 0.0, 0.0, 0.0];
        case 12
            coefs = [2.6, 0.0, 0.0, 0.0];
        case 13
            coefs = [1.020, 0.0, 0.0, 0.0];
        otherwise
            warning('Invalid layer index in density_coefs_prem');
    end
end

function coefs = vp_coefs_prem(r, opt)
    % Return PREM isotropic p-wave velocity polynomial coefficients
    % r : radius (r=0 at the Earth's center)
    
    % Determine layer index based on radius and option
    n = layer_index_prem(r, opt);
    
    % Initialize coefficients array
    coefs = zeros(1, 4);
    
    % Assign coefficients based on layer index
    switch n
        case 1
            coefs = [11.2622, 0.0, -6.3640, 0.0];
        case 2
            coefs = [11.0487, -4.0362, 4.8023, -13.5732];
        case 3
            coefs = [15.3891, -5.3181, 5.5242, -2.5514];
        case 4
            coefs = [24.9520, -40.4673, 51.4832, -26.6419];
        case 5
            coefs = [29.2766, -23.6027, 5.5242, -2.5514];
        case 6
            coefs = [19.0957, -9.8672, 0.0, 0.0];
        case 7
            coefs = [39.7027, -32.6166, 0.0, 0.0];
        case 8
            coefs = [20.3926, -12.2569, 0.0, 0.0];
        case 9
            coefs = [4.1875, 3.9382, 0.0, 0.0];
        case 10
            coefs = [4.1875, 3.9382, 0.0, 0.0];
        case 11
            coefs = [6.8, 0.0, 0.0, 0.0];
        case 12
            coefs = [5.8, 0.0, 0.0, 0.0];
        case 13
            coefs = [1.45, 0.0, 0.0, 0.0];
        otherwise
            warning('Invalid layer index in vp_coefs_prem');
    end
end

function coefs = vs_coefs_prem(r, opt)
    % Return PREM isotropic p-wave velocity polynomial coefficients
    % r : radius (r=0 at the Earth's center)
    
    % Determine layer index based on radius and option
    n = layer_index_prem(r, opt);
    
    % Initialize coefficients array
    coefs = zeros(1, 4);
    
    % Assign coefficients based on layer index
    switch n
    case 1
        coefs = [3.6678, 0.0, -4.4475, 0.0];
    case 2
        coefs = [0.0, 0.0, 0.0, 0.0];
    case 3
        coefs = [6.9254, 1.4672, -2.0834, 0.9783];
    case 4
        coefs = [11.1671, -13.7818, 17.4575, -9.2777];
    case 5
        coefs = [22.3459, -17.2473, -2.0834, 0.9783];
    case 6
        coefs = [9.9839, -4.9324, 0.0, 0.0];
    case 7
        coefs = [22.3512, -18.5856, 0.0, 0.0];
    case 8
        coefs = [8.9496, -4.4597, 0.0, 0.0];
    case 9
        coefs = [2.1519, 2.3481, 0.0, 0.0];
    case 10
        coefs = [2.1519, 2.3481, 0.0, 0.0];
    case 11
        coefs = [3.9, 0.0, 0.0, 0.0];
    case 12
        coefs = [3.2, 0.0, 0.0, 0.0];
    case 13
        coefs = [0.0, 0.0, 0.0, 0.0];
    otherwise
        warning('INVALID LAYER INDEX IN VS_COEFS_PREM, PRESS ENTER TO CONTINUE');
    end


end


function layer_index = layer_index_prem(r, opt)
    % Return PREM layer index corresponding to radius r
    % r : radius (r=0 at the Earth's center)
    
    % Define depth interfaces
    depths_interfaces = [0.0, 1221.5, 3480.0, 3630.0, 5600.0, 5701.0, 5771.0, 5971.0, 6151.0, 6291.0, 6346.0, 6356.0, 6368.0, 6371.0];
    
    % Check if radius is within valid range
    if r < depths_interfaces(1) || r > depths_interfaces(end)
        warning('Invalid radius in layer_index_prem');
    end
    
    % Initialize layer index
    layer_index = 13;
    
    % Determine the appropriate layer index based on radius
    for i = 12:-1:1
        if r >= depths_interfaces(i) && r < depths_interfaces(i + 1)
            layer_index = i;
            break;
        end
    end
    
    % Adjust layer index based on option
    switch opt
        case {'C', 'c'}
            if layer_index == 13
                layer_index = 12;
            end
        case {'O', 'o'}
            % No change required for 'O' or 'o'
        otherwise
            warning('Invalid option OPT in layer_index_prem');
    end
end

function vp = vp_prem(r, opt)
    % Return PREM isotropic p-wave velocity profile
    % r : radius (r=0 at the Earth's center)
    
    % % Define coefficients array
    % coefs = zeros(1, 4);
    
    % Normalize radius
    x = r / 6371;
    
    % Get VP coefficients based on radius and option
    coefs = vp_coefs_prem(r, opt);
    
    % Calculate VP using polynomial
    vp = poly3(x, coefs);
end

function vs = vs_prem(r, opt)
    % Return PREM isotropic s-wave velocity profile
    % r : radius (r=0 at the Earth's center)
    
    % % Define coefficients array
    % coefs = zeros(1, 4);
    
    % Normalize radius
    x = r / 6371;
    
    % Get VP coefficients based on radius and option
    coefs = vs_coefs_prem(r, opt);
    
    % Calculate VP using polynomial
    vs = poly3(x, coefs);
end

