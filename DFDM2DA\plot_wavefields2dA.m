function plot_wavefields2dA(OM,it)
%PLOT_DOMAIN Summary of this function goes here

Umax = -99999999;
rmax = 0;
hold on
for iom = 1:length(OM)
    x2d   = OM(iom).x2d11;
    z2d   = OM(iom).z2d11;
    Umid  = OM(iom).state.Umid(:,:,it);
    Umax = max(Umax,max(Umid(:)));
    % Umax = max(Umid(:));

    % dist = sqrt(x2d.^2+z2d.^2);
    % rmax = max(rmax,max(dist(:)));
    [nx,nz] = size(x2d);

    id1 = 1:nx:nx*nz;
    id2 = nx:nx:nx*nz;
    id3 = 1:nx;
    id4 = nx*(nz-1)+1:nx*nz;
    plot(x2d(id1)/1e3,z2d(id1)/1e3,'--k','LineWidth',1);
    plot(x2d(id2)/1e3,z2d(id2)/1e3,'--k','LineWidth',1);
    plot(x2d(id3)/1e3,z2d(id3)/1e3,'--k','LineWidth',1);
    plot(x2d(id4)/1e3,z2d(id4)/1e3,'--k','LineWidth',1);

    pcolor(x2d/1e3,z2d/1e3,Umid); 

end
colormap(jet);
clim([-8e-8,8e-8])
shading interp;
fprintf('... time step %d with max(U) = %0.2e ...\n', it, Umax);

% c.Title.String = 'Vp (m/s)';
c.Title.FontSize = 20;
xlabel('km')
ylabel('km')
% xlim([-rmax,rmax])
% ylim([-rmax,rmax])
axis equal tight

set(gca,'fontsize',20)
box on
hold off
drawnow


end

