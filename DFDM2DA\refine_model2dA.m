function [OM] = refine_model2dA(OM)
% REFINE_MODEL2DA Summary of this function goes here

    p = 5;
    % ord_gi = p + 1;
    % kp1 = p + 1;
    % kp2 = kp1 -1;

    pt  = 2;
    kpt = pt+1;

    Vp  = 2750;
    rho = 2000;
    
    ndomain = length(OM);
    for iom = 1:ndomain
        Nx1 = OM(iom).Nx1;   Nx2 = Nx1 - 1;
        Nz1 = OM(iom).Nz1;   Nz2 = Nz1 - 1;
        dx  = 1/(Nx1-1);
        dz  = 1/(Nz1-1);
        xps1 = 0:dx:1;
        zps1 = 0:dz:1;
        fprintf('... Number of hat points are %d and %d, ...\n',Nx1,Nz1);
        
        x2d11t = OM(iom).model2dA.xa;
        z2d11t = OM(iom).model2dA.za;
        [Nxt,Nzt] = size(x2d11t);
        
        dxt   = 1/(Nxt-1);
        dzt   = 1/(Nzt-1);
        xpst1 = 0:dxt:1;
        zpst1 = 0:dzt:1;
 

        % tx1 = Get_Knot_Vector(Nx1,kp1)*1;  tx2 = tx1(2:end-1);
        % tz1 = Get_Knot_Vector(Nz1,kp1)*1;  tz2 = tz1(2:end-1);

        txt1 = Get_Knot_Vector_shape(Nxt,kpt)*1;
        tzt1 = Get_Knot_Vector_shape(Nzt,kpt)*1;

        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        % build the Bmat to obtain the coefficiency of topograpy in bspline basis
        % so that we could describe it in a basis way.
        % Same number of the input and output due to the inversion.
        % x and z direction
        B1xp_mat = zeros(Nxt,Nxt); 
        B1zp_mat = zeros(Nzt,Nzt);
        for ibx=1:Nxt % for each basis function (nb: Nxt)
            for jpx=1:Nxt 
                x0 = xpst1(jpx);  % for each evenly distributed points (topography)
                B1xp_mat(jpx,ibx) = bspln(txt1,Nxt,ibx,kpt,x0);
            end
        end
        for ibz = 1:Nzt
            for jpz = 1:Nzt
                z0 = zpst1(jpz);
                B1zp_mat(jpz,ibz) = bspln(tzt1,Nzt,ibz,kpt,z0);
            end
        end
        invB1xp_mat = inv(B1xp_mat);
        invB1zp_mat = inv(B1zp_mat);

        % The corresponding coeffs of the grids in x and z direction
        % Note that only need to invert one direction
        % x direction
        coef11x_xp = pagemtimes(invB1xp_mat,x2d11t);
        coef11z_xp = pagemtimes(invB1xp_mat,z2d11t);

        x2d11tT    = permute(x2d11t,[2,1,3]);
        coef11x_zp = pagemtimes(invB1zp_mat,x2d11tT); 
        coef11x_zp = permute(coef11x_zp,[2,1,3]);

        z2d11tT    = permute(z2d11t,[2,1,3]);
        coef11z_zp = pagemtimes(invB1zp_mat,z2d11tT);  
        coef11z_zp = permute(coef11z_zp,[2,1,3]);

        % for plotting %%%%%%%%%%%%%%%%%%%%%%%%
        B1xp_mat_plot = zeros(Nx1,Nxt); 
        B1zp_mat_plot = zeros(Nz1,Nzt);
        for jpx = 1:Nx1
            x0  = xps1(jpx);
            for ib1x = 1:Nxt
                B1xp_mat_plot(jpx,ib1x) = bspln(txt1,Nxt,ib1x,kpt,x0);
            end
        end
        for jpz = 1:Nz1
            z0  = zps1(jpz);
            for ib1z = 1:Nzt
                B1zp_mat_plot(jpz,ib1z) = bspln(tzt1,Nzt,ib1z,kpt,z0);
            end
        end
        % 11
        x2d11  = pagemtimes(B1xp_mat_plot,coef11x_xp);
        x2d11T = permute(x2d11,[2,1,3]);
        x2d11T = pagemtimes(B1zp_mat_plot, x2d11T);
        x2d11  = permute(x2d11T,[2,1,3]);

        z2d11  = pagemtimes(B1xp_mat_plot,coef11z_zp);
        z2d11T = permute(z2d11,[2,1,3]);
        z2d11T = pagemtimes(B1zp_mat_plot, z2d11T);
        z2d11  = permute(z2d11T,[2,1,3]);
        % for plotting  %%%%%%%%%%%%%%%%%%%%%%%%

        % to calculate the bspline basis values at hat points in x and z directions
        fprintf('...calculate the hat points begin...\n');
        [hatpointsx1,hatpointsx2] = hat_pts(Nx1,p);
        [hatpointsz1,hatpointsz2] = hat_pts(Nz1,p);
        fprintf('...calculate the hat points end...\n');
        B1xp_mat_hat1 = zeros(Nx1,Nxt); 
        B1xp_mat_hat2 = zeros(Nx2,Nxt); 
        B1zp_mat_hat1 = zeros(Nz1,Nzt); 
        B1zp_mat_hat2 = zeros(Nz2,Nzt); 

        for ib1x = 1:Nxt
            for jpx = 1:Nx1
                x0  = hatpointsx1(jpx);
                B1xp_mat_hat1(jpx,ib1x) = bspln(txt1,Nxt,ib1x,kpt,x0);
            end
        end
        for ib1x = 1:Nxt
            for jpx = 1:Nx2
                x0  = hatpointsx2(jpx);
                B1xp_mat_hat2(jpx,ib1x) = bspln(txt1,Nxt,ib1x,kpt,x0);
            end
        end
        for ib1z = 1:Nzt
            for jpz = 1:Nz1
                z0  = hatpointsz1(jpz);
                B1zp_mat_hat1(jpz,ib1z) = bspln(tzt1,Nzt,ib1z,kpt,z0);
            end
        end
        for ib1z = 1:Nzt
            for jpz = 1:Nz2
                z0  = hatpointsz2(jpz);
                B1zp_mat_hat2(jpz,ib1z) = bspln(tzt1,Nzt,ib1z,kpt,z0);
            end
        end

        % partial derivative matrix at the hat points in the reference element
        dB1xp_mat_hat1 = zeros(Nx1,Nxt);     
        dB1zp_mat_hat1 = zeros(Nz1,Nzt); 

        dB1xp_mat_hat2 = zeros(Nx2,Nxt);     
        dB1zp_mat_hat2 = zeros(Nz2,Nzt); 

        for ib1x = 1:Nxt
            for jpx = 1:Nx1
                x0  = hatpointsx1(jpx); 
                dB1xp_mat_hat1(jpx,ib1x) = dbspln(txt1,Nxt,ib1x,kpt,x0,1);
            end
        end
        for ib1x = 1:Nxt
            for jpx = 1:Nx2
                x0  = hatpointsx2(jpx);
                dB1xp_mat_hat2(jpx,ib1x) = dbspln(txt1,Nxt,ib1x,kpt,x0,1);
            end
        end
        for ib1z = 1:Nzt
            for jpz = 1:Nz1
                z0  = hatpointsz1(jpz);
                dB1zp_mat_hat1(jpz,ib1z) = dbspln(tzt1,Nzt,ib1z,kpt,z0,1);
            end
        end
        for ib1z = 1:Nzt
            for jpz = 1:Nz2
                z0  = hatpointsz2(jpz);
                dB1zp_mat_hat2(jpz,ib1z) = dbspln(tzt1,Nzt,ib1z,kpt,z0,1);
            end
        end

        % % using the coeffs obtained above to get the coordinates of hat points in the physical element
        % 11
        x2d11_hat  = pagemtimes(B1xp_mat_hat1,coef11x_xp);
        x2d11_hatT = permute(x2d11_hat,[2,1,3]);
        x2d11_hatT = pagemtimes(B1zp_mat_hat1, x2d11_hatT);
        x2d11_hat  = permute(x2d11_hatT,[2,1,3]);

        z2d11_hat  = pagemtimes(B1xp_mat_hat1,coef11z_zp);
        z2d11_hatT = permute(z2d11_hat,[2,1,3]);
        z2d11_hatT = pagemtimes(B1zp_mat_hat1, z2d11_hatT);
        z2d11_hat  = permute(z2d11_hatT,[2,1,3]);

        
        hold on
        scatter(x2d11_hat(1:Nx1*Nz1)/1e3,z2d11_hat(1:Nx1*Nz1)/1e3,'b.');
        % scatter(x2d12_hat(1:Nx1*Nz2)/1e3,z2d12_hat(1:Nx1*Nz2)/1e3,'g.');
        % scatter(x2d21_hat(1:Nx2*Nz1)/1e3,z2d21_hat(1:Nx2*Nz1)/1e3,'b.');
        % scatter(x2d22_hat(1:Nx2*Nz2)/1e3,z2d22_hat(1:Nx2*Nz2)/1e3,'m.');
        axis equal tight
        hold off



        % calculate the spatial derivatives
        % x direction 
        % dxdxp11 
        dxdxp11_hat  = pagemtimes(dB1xp_mat_hat1,coef11x_xp);
        dxdxp11_hatT = permute(dxdxp11_hat,[2,1,3]);
        dxdxp11_hatT = pagemtimes(B1zp_mat_hat1, dxdxp11_hatT);
        dxdxp11_hat  = permute(dxdxp11_hatT,[2,1,3]);
        % dxdxp12
        dxdxp12_hat  = pagemtimes(dB1xp_mat_hat1,coef11x_xp);
        dxdxp12_hatT = permute(dxdxp12_hat,[2,1,3]);
        dxdxp12_hatT = pagemtimes(B1zp_mat_hat2, dxdxp12_hatT);
        dxdxp12_hat  = permute(dxdxp12_hatT,[2,1,3]);
        % dxdxp21
        dxdxp21_hat  = pagemtimes(dB1xp_mat_hat2,coef11x_xp);
        dxdxp21_hatT = permute(dxdxp21_hat,[2,1,3]);
        dxdxp21_hatT = pagemtimes(B1zp_mat_hat1, dxdxp21_hatT);
        dxdxp21_hat  = permute(dxdxp21_hatT,[2,1,3]);
        % dxdxp22
        dxdxp22_hat  = pagemtimes(dB1xp_mat_hat2,coef11x_xp);
        dxdxp22_hatT = permute(dxdxp22_hat,[2,1,3]);
        dxdxp22_hatT = pagemtimes(B1zp_mat_hat2, dxdxp22_hatT);
        dxdxp22_hat  = permute(dxdxp22_hatT,[2,1,3]);
        % dzdxp11
        dzdxp11_hat  = pagemtimes(dB1xp_mat_hat1,coef11z_xp);
        dzdxp11_hatT = permute(dzdxp11_hat,[2,1,3]);
        dzdxp11_hatT = pagemtimes(B1zp_mat_hat1, dzdxp11_hatT);
        dzdxp11_hat  = permute(dzdxp11_hatT,[2,1,3]);
        % dzdxp12
        dzdxp12_hat  = pagemtimes(dB1xp_mat_hat1,coef11z_xp);
        dzdxp12_hatT = permute(dzdxp12_hat,[2,1,3]);
        dzdxp12_hatT = pagemtimes(B1zp_mat_hat2, dzdxp12_hatT);
        dzdxp12_hat  = permute(dzdxp12_hatT,[2,1,3]);
        % dzdxp21
        dzdxp21_hat  = pagemtimes(dB1xp_mat_hat2,coef11z_xp);
        dzdxp21_hatT = permute(dzdxp21_hat,[2,1,3]);
        dzdxp21_hatT = pagemtimes(B1zp_mat_hat1, dzdxp21_hatT);
        dzdxp21_hat  = permute(dzdxp21_hatT,[2,1,3]);
        % dzdxp22
        dzdxp22_hat  = pagemtimes(dB1xp_mat_hat2,coef11z_xp);
        dzdxp22_hatT = permute(dzdxp22_hat,[2,1,3]);
        dzdxp22_hatT = pagemtimes(B1zp_mat_hat2, dzdxp22_hatT);
        dzdxp22_hat  = permute(dzdxp22_hatT,[2,1,3]);

        % z direction
        % dxdzp11;
        dxdzp11_hat  = pagemtimes(B1xp_mat_hat1, coef11x_zp);
        dxdzp11_hatT = permute(dxdzp11_hat,[2,1,3]);
        dxdzp11_hatT = pagemtimes(dB1zp_mat_hat1,dxdzp11_hatT);
        dxdzp11_hat  = permute(dxdzp11_hatT,[2,1,3]);
        % dzdzp11;
        dzdzp11_hat  = pagemtimes(B1xp_mat_hat1, coef11z_zp);
        dzdzp11_hatT = permute(dzdzp11_hat,[2,1,3]);
        dzdzp11_hatT = pagemtimes(dB1zp_mat_hat1,dzdzp11_hatT);
        dzdzp11_hat  = permute(dzdzp11_hatT,[2,1,3]);
        % dxdzp21;
        dxdzp21_hat  = pagemtimes(B1xp_mat_hat2, coef11x_zp);
        dxdzp21_hatT = permute(dxdzp21_hat,[2,1,3]);
        dxdzp21_hatT = pagemtimes(dB1zp_mat_hat1,dxdzp21_hatT);
        dxdzp21_hat  = permute(dxdzp21_hatT,[2,1,3]);
        % dzdzp21;
        dzdzp21_hat  = pagemtimes(B1xp_mat_hat2, coef11z_zp);
        dzdzp21_hatT = permute(dzdzp21_hat,[2,1,3]);
        dzdzp21_hatT = pagemtimes(dB1zp_mat_hat1,dzdzp21_hatT);
        dzdzp21_hat  = permute(dzdzp21_hatT,[2,1,3]);
        % dxdzp12;
        dxdzp12_hat  = pagemtimes(B1xp_mat_hat1, coef11x_zp);
        dxdzp12_hatT = permute(dxdzp12_hat,[2,1,3]);
        dxdzp12_hatT = pagemtimes(dB1zp_mat_hat2,dxdzp12_hatT);
        dxdzp12_hat  = permute(dxdzp12_hatT,[2,1,3]);
        % dzdzp12;
        dzdzp12_hat  = pagemtimes(B1xp_mat_hat1, coef11z_zp);
        dzdzp12_hatT = permute(dzdzp12_hat,[2,1,3]);
        dzdzp12_hatT = pagemtimes(dB1zp_mat_hat2,dzdzp12_hatT);
        dzdzp12_hat  = permute(dzdzp12_hatT,[2,1,3]);
        % dxdzp22;
        dxdzp22_hat  = pagemtimes(B1xp_mat_hat2, coef11x_zp);
        dxdzp22_hatT = permute(dxdzp22_hat,[2,1,3]);
        dxdzp22_hatT = pagemtimes(dB1zp_mat_hat2,dxdzp22_hatT);
        dxdzp22_hat  = permute(dxdzp22_hatT,[2,1,3]);
        % dzdzp22;
        dzdzp22_hat  = pagemtimes(B1xp_mat_hat2, coef11z_zp);
        dzdzp22_hatT = permute(dzdzp22_hat,[2,1,3]);
        dzdzp22_hatT = pagemtimes(dB1zp_mat_hat2,dzdzp22_hatT);
        dzdzp22_hat  = permute(dzdzp22_hatT,[2,1,3]);

        % Jac
        Jac11_hat = dxdxp11_hat.*dzdzp11_hat - dzdxp11_hat.*dxdzp11_hat;
        Jac12_hat = dxdxp12_hat.*dzdzp12_hat - dzdxp12_hat.*dxdzp12_hat;
        Jac21_hat = dxdxp21_hat.*dzdzp21_hat - dzdxp21_hat.*dxdzp21_hat;
        Jac22_hat = dxdxp22_hat.*dzdzp22_hat - dzdxp22_hat.*dxdzp22_hat;

        % 1/Jac
        tmp11_hat = 1./Jac11_hat;
        tmp12_hat = 1./Jac12_hat;
        tmp21_hat = 1./Jac21_hat;
        tmp22_hat = 1./Jac22_hat;

        % dxi_dx
        dxpdx11_hat =  dzdzp11_hat.*tmp11_hat; %(1,1)
        dzpdx11_hat = -dzdxp11_hat.*tmp11_hat; %(1,2)
        dxpdz11_hat = -dxdzp11_hat.*tmp11_hat; %(2,1)
        dzpdz11_hat =  dxdxp11_hat.*tmp11_hat; %(2,2)

        dxpdx12_hat =  dzdzp12_hat.*tmp12_hat; %(1,1)
        dzpdx12_hat = -dzdxp12_hat.*tmp12_hat; %(1,2)
        dxpdz12_hat = -dxdzp12_hat.*tmp12_hat; %(2,1)
        dzpdz12_hat =  dxdxp12_hat.*tmp12_hat; %(2,2)

        dxpdx21_hat =  dzdzp21_hat.*tmp21_hat; %(1,1)
        dzpdx21_hat = -dzdxp21_hat.*tmp21_hat; %(1,2)
        dxpdz21_hat = -dxdzp21_hat.*tmp21_hat; %(2,1)
        dzpdz21_hat =  dxdxp21_hat.*tmp21_hat; %(2,2)

        dxpdx22_hat =  dzdzp22_hat.*tmp22_hat; %(1,1)
        dzpdx22_hat = -dzdxp22_hat.*tmp22_hat; %(1,2)
        dxpdz22_hat = -dxdzp22_hat.*tmp22_hat; %(2,1)
        dzpdz22_hat =  dxdxp22_hat.*tmp22_hat; %(2,2)

        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%




        % used to calculate the refined hat coordinate
       % rho12   = zeros(Nx1,Nz2); 
       % rho21   = zeros(Nx1,Nz2);
       % mu22    = zeros(Nx2,Nz2); 
       % mu11    = zeros(Nx1,Nz1);
       
       OM(iom).px1 = p;
       OM(iom).pz1 = p;

       OM(iom).x2d11 = x2d11;
       OM(iom).z2d11 = z2d11;
       
       OM(iom).dxpdx11 = dxpdx11_hat;
       OM(iom).dzpdx11 = dzpdx11_hat;
       OM(iom).dxpdz11 = dxpdz11_hat;
       OM(iom).dzpdz11 = dzpdz11_hat;
       OM(iom).dxpdx12 = dxpdx12_hat;
       OM(iom).dzpdx12 = dzpdx12_hat;
       OM(iom).dxpdz12 = dxpdz12_hat;
       OM(iom).dzpdz12 = dzpdz12_hat;
       OM(iom).dxpdx21 = dxpdx21_hat;
       OM(iom).dzpdx21 = dzpdx21_hat;
       OM(iom).dxpdz21 = dxpdz21_hat;
       OM(iom).dzpdz21 = dzpdz21_hat;
       OM(iom).dxpdx22 = dxpdx22_hat;
       OM(iom).dzpdx22 = dzpdx22_hat;
       OM(iom).dxpdz22 = dxpdz22_hat;
       OM(iom).dzpdz22 = dzpdz22_hat;
       OM(iom).Jac12   = Jac12_hat;
       OM(iom).Jac21   = Jac21_hat;
       OM(iom).Jac11   = Jac11_hat;
       OM(iom).Jac22   = Jac22_hat;

       % OM(iom).rho12   = rho12_hat;
       % OM(iom).rho21   = rho21_hat;
       % OM(iom).mu22    = mu22;
       % OM(iom).mu11    = mu11;

       % to be changed
       OM(iom).rho12 = rho;
       OM(iom).rho21 = rho;
       OM(iom).mu22  = Vp*Vp*rho;
       OM(iom).mu11  = Vp*Vp*rho;

    end

end





function [Nx1,Nz1] = dist2d(xa,za,rho,mu,fmax,ppw)
    
    vmin = min( sqrt(mu./rho) ); 
    wavemin = vmin/fmax;

    xtmp = xa(2:end,:) - xa(1:end,:); xtmp = abs(xtmp);
    ztmp = za(:,2:end) - za(:,1:end); ztmp = abs(ztmp);

    distx = sum(xtmp,1); 
    distz = sum(ztmp,2); 
    max_ddx = max(distx(:));
    max_ddz = max(distz(:));  
    Nx1 = max(3,ceil(max_ddx/wavemin*ppw));
    Nz1 = max(3,ceil(max_ddz/wavemin*ppw));   

end




