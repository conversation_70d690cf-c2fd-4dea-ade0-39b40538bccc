function save_domain_data_cpp_format(domain, output_dir)
% SAVE_DOMAIN_DATA_CPP_FORMAT 保存域数据，格式与C++输出一致
% 
% 输入:
%   domain - 域结构体 (OM的一个元素)
%   output_dir - 输出目录路径
%
% 输出格式与C++程序完全一致，包括文件路径显示和数据格式

    % 创建domain_0子目录
    domain_dir = fullfile(output_dir, 'domain_0');
    if ~exist(domain_dir, 'dir')
        mkdir(domain_dir);
    end
    
    % 辅助函数：保存矩阵数据并显示文件路径
    function save_matrix_with_path(matrix, filename)
        if ~isempty(matrix) && size(matrix, 1) > 0 && size(matrix, 2) > 0
            full_path = fullfile(domain_dir, filename);
            % 显示文件路径（模拟C++输出格式）
            fprintf('D:\\project\\ware\\output_cpp\\domain_0\\%s\n', filename);
            
            % 保存矩阵数据，使用科学计数法，16位精度
            fid = fopen(full_path, 'w');
            if fid ~= -1
                for i = 1:size(matrix, 1)
                    for j = 1:size(matrix, 2)
                        if j < size(matrix, 2)
                            fprintf(fid, '%.16e ', matrix(i, j));
                        else
                            fprintf(fid, '%.16e', matrix(i, j));
                        end
                    end
                    fprintf(fid, '\n');
                end
                fclose(fid);
                fprintf('\n'); % 空行分隔
            end
        end
    end
    
    % 辅助函数：保存向量数据并显示文件路径
    function save_vector_with_path(vector, filename)
        if ~isempty(vector) && length(vector) > 0
            full_path = fullfile(domain_dir, filename);
            % 显示文件路径（模拟C++输出格式）
            fprintf('D:\\project\\ware\\output_cpp\\domain_0\\%s\n', filename);
            
            % 保存向量数据，使用科学计数法，16位精度
            fid = fopen(full_path, 'w');
            if fid ~= -1
                for i = 1:length(vector)
                    fprintf(fid, '%.16e\n', vector(i));
                end
                fclose(fid);
                fprintf('\n'); % 空行分隔
            end
        end
    end
    
    % 保存域参数
    param_file = fullfile(domain_dir, 'parameters.txt');
    fprintf('D:\\project\\ware\\output_cpp\\domain_0\\parameters.txt\n');
    fid = fopen(param_file, 'w');
    if fid ~= -1
        fprintf(fid, 'iom = %d\n', 0);  % 固定为域0
        fprintf(fid, 'region = %d\n', domain.region);
        % 计算域边界
        if isfield(domain, 'x2d11') && ~isempty(domain.x2d11)
            x_min = min(domain.x2d11(:));
            x_max = max(domain.x2d11(:));
            z_min = min(domain.z2d11(:));
            z_max = max(domain.z2d11(:));
        else
            x_min = 0; x_max = 0; z_min = 0; z_max = 0;
        end

        fprintf(fid, 'x_min = %.16e\n', x_min);
        fprintf(fid, 'x_max = %.16e\n', x_max);
        fprintf(fid, 'z_min = %.16e\n', z_min);
        fprintf(fid, 'z_max = %.16e\n', z_max);
        fprintf(fid, 'Nx1 = %d\n', domain.Nx1);
        fprintf(fid, 'Nz1 = %d\n', domain.Nz1);
        fprintf(fid, 'Nx2 = %d\n', domain.Nx1 - 1);  % Nx2 = Nx1 - 1
        fprintf(fid, 'Nz2 = %d\n', domain.Nz1 - 1);  % Nz2 = Nz1 - 1
        fprintf(fid, 'px1 = %d\n', domain.px1);
        fprintf(fid, 'pz1 = %d\n', domain.pz1);

        % 安全访问可能不存在的字段
        if isfield(domain, 'mu')
            fprintf(fid, 'mu = %.16e\n', domain.mu);
        else
            fprintf(fid, 'mu = %.16e\n', 0.0);
        end

        if isfield(domain, 'rho')
            fprintf(fid, 'rho = %.16e\n', domain.rho);
        else
            fprintf(fid, 'rho = %.16e\n', 0.0);
        end

        % 邻居信息（可能不存在）
        fprintf(fid, 'iNbr_mo = %d\n', get_field_or_default(domain, 'iNbr_mo', -1));
        fprintf(fid, 'iNbr_po = %d\n', get_field_or_default(domain, 'iNbr_po', -1));
        fprintf(fid, 'iNbr_om = %d\n', get_field_or_default(domain, 'iNbr_om', -1));
        fprintf(fid, 'iNbr_op = %d\n', get_field_or_default(domain, 'iNbr_op', -1));
        fclose(fid);
    end
    fprintf('\n');
    
    % 保存基函数矩阵
    if isfield(domain, 'bxT1')
        save_matrix_with_path(domain.bxT1, 'bxT1.txt');
    end
    if isfield(domain, 'bxT2')
        save_matrix_with_path(domain.bxT2, 'bxT2.txt');
    end
    if isfield(domain, 'bzT1')
        save_matrix_with_path(domain.bzT1, 'bzT1.txt');
    end
    if isfield(domain, 'bzT2')
        save_matrix_with_path(domain.bzT2, 'bzT2.txt');
    end
    
    % 保存坐标变换矩阵
    if isfield(domain, 'dxpdx11')
        save_matrix_with_path(domain.dxpdx11, 'dxpdx11.txt');
    end
    if isfield(domain, 'dxpdx22')
        save_matrix_with_path(domain.dxpdx22, 'dxpdx22.txt');
    end
    if isfield(domain, 'dxpdz11')
        save_matrix_with_path(domain.dxpdz11, 'dxpdz11.txt');
    end
    if isfield(domain, 'dxpdz22')
        save_matrix_with_path(domain.dxpdz22, 'dxpdz22.txt');
    end
    if isfield(domain, 'dzpdx11')
        save_matrix_with_path(domain.dzpdx11, 'dzpdx11.txt');
    end
    if isfield(domain, 'dzpdx22')
        save_matrix_with_path(domain.dzpdx22, 'dzpdx22.txt');
    end
    if isfield(domain, 'dzpdz11')
        save_matrix_with_path(domain.dzpdz11, 'dzpdz11.txt');
    end
    if isfield(domain, 'dzpdz22')
        save_matrix_with_path(domain.dzpdz22, 'dzpdz22.txt');
    end
    
    % 保存逆L矩阵
    if isfield(domain, 'invLx11')
        save_matrix_with_path(domain.invLx11, 'invLx11.txt');
    end
    if isfield(domain, 'invLx22')
        save_matrix_with_path(domain.invLx22, 'invLx22.txt');
    end
    if isfield(domain, 'invLxT11')
        save_matrix_with_path(domain.invLxT11, 'invLxT11.txt');
    end
    if isfield(domain, 'invLxT22')
        save_matrix_with_path(domain.invLxT22, 'invLxT22.txt');
    end
    if isfield(domain, 'invLz11')
        save_matrix_with_path(domain.invLz11, 'invLz11.txt');
    end
    if isfield(domain, 'invLz22')
        save_matrix_with_path(domain.invLz22, 'invLz22.txt');
    end
    if isfield(domain, 'invLzT11')
        save_matrix_with_path(domain.invLzT11, 'invLzT11.txt');
    end
    if isfield(domain, 'invLzT22')
        save_matrix_with_path(domain.invLzT22, 'invLzT22.txt');
    end
    
    % 保存雅可比矩阵
    if isfield(domain, 'Jac11')
        save_matrix_with_path(domain.Jac11, 'Jac11.txt');
    end
    if isfield(domain, 'Jac22')
        save_matrix_with_path(domain.Jac22, 'Jac22.txt');
    end
    
    % 保存DFD矩阵
    if isfield(domain, 'kkx12')
        save_matrix_with_path(domain.kkx12, 'kkx12.txt');
    end
    if isfield(domain, 'kkx21')
        save_matrix_with_path(domain.kkx21, 'kkx21.txt');
    end
    if isfield(domain, 'kkz12')
        save_matrix_with_path(domain.kkz12, 'kkz12.txt');
    end
    if isfield(domain, 'kkz21')
        save_matrix_with_path(domain.kkz21, 'kkz21.txt');
    end
    
    % 保存材料属性矩阵
    if isfield(domain, 'mu11')
        save_matrix_with_path(domain.mu11, 'mu11.txt');
    end
    if isfield(domain, 'mu22')
        save_matrix_with_path(domain.mu22, 'mu22.txt');
    end
    
    % 保存状态矩阵
    if isfield(domain, 'state')
        state = domain.state;
        
        if isfield(state, 'Sxx11')
            save_matrix_with_path(state.Sxx11, 'state_Sxx11.txt');
        end
        if isfield(state, 'Sxx22')
            save_matrix_with_path(state.Sxx22, 'state_Sxx22.txt');
        end
        if isfield(state, 'Szz11')
            save_matrix_with_path(state.Szz11, 'state_Szz11.txt');
        end
        if isfield(state, 'Szz22')
            save_matrix_with_path(state.Szz22, 'state_Szz22.txt');
        end
        if isfield(state, 'U12')
            save_matrix_with_path(state.U12, 'state_U12.txt');
        end
        if isfield(state, 'U21')
            save_matrix_with_path(state.U21, 'state_U21.txt');
        end
        
        % 保存边界向量
        if isfield(state, 'U12mo')
            save_vector_with_path(state.U12mo, 'state_U12mo.txt');
        end
        if isfield(state, 'U12om')
            save_vector_with_path(state.U12om, 'state_U12om.txt');
        end
        if isfield(state, 'U12op')
            save_vector_with_path(state.U12op, 'state_U12op.txt');
        end
        if isfield(state, 'U12po')
            save_vector_with_path(state.U12po, 'state_U12po.txt');
        end
        if isfield(state, 'U21mo')
            save_vector_with_path(state.U21mo, 'state_U21mo.txt');
        end
        if isfield(state, 'U21om')
            save_vector_with_path(state.U21om, 'state_U21om.txt');
        end
        if isfield(state, 'U21op')
            save_vector_with_path(state.U21op, 'state_U21op.txt');
        end
        if isfield(state, 'U21po')
            save_vector_with_path(state.U21po, 'state_U21po.txt');
        end
    end
    
end

function value = get_field_or_default(struct, field_name, default_value)
    % 安全获取结构体字段，如果不存在则返回默认值
    if isfield(struct, field_name)
        value = struct.(field_name);
    else
        value = default_value;
    end
end
