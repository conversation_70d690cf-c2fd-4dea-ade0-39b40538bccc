function OM=save_wavefields2dA(OM,it)
% SAVE_WAVEFIELD

for iom = 1:length(OM)
    % invLT
    invLxT11 = OM(iom).invLxT11;
    invLzT11 = OM(iom).invLzT11;
    invLxT22 = OM(iom).invLxT22;
    invLzT22 = OM(iom).invLzT22;

    % bT
    bxT1 = OM(iom).bxT1;
    bzT1 = OM(iom).bzT1;
    bxT2 = OM(iom).bxT2;
    bzT2 = OM(iom).bzT2;

    % U
    U21 = OM(iom).state.U21;
    U12 = OM(iom).state.U12;


    % From Ux211, Ux121, Ux112 and Ux222 to Ux111
    % 1st: from hat to bspline
    Ub21  = tensorProduct2D(invLxT22,invLzT11,U21);
    Ub12  = tensorProduct2D(invLxT11,invLzT22,U12);
    % 2nd: from bspline to real Uxmid
    Umid1 = tensorProduct2D(bxT2,bzT1,Ub21);
    Umid2 = tensorProduct2D(bxT1,bzT2,Ub12);

    % % grid 1
    % Ub12  = pagemtimes(invLxT11,U12); 
    % Ub12  = permute(Ub12,[2,1]);
    % Ub12  = pagemtimes(invLzT22,Ub12);
    % Umid1 = pagemtimes(bzT2,Ub12);
    % Umid1 = permute(Umid1,[2,1]);
    % Umid1 = pagemtimes(bxT1,Umid1);
    % 
    % % grid 2
    % Ub21  = pagemtimes(invLxT22,U21); 
    % Ub21  = permute(Ub21,[2,1]);
    % Ub21  = pagemtimes(invLzT11,Ub21);
    % Umid2 = pagemtimes(bzT1,Ub21);
    % Umid2 = permute(Umid2,[2,1]);
    % Umid2 = pagemtimes(bxT2,Umid2);
    % 
    % % Save wavefields
    OM(iom).state.Umid(:,:,it) = (Umid1 + Umid2)/2;
 
end

end

