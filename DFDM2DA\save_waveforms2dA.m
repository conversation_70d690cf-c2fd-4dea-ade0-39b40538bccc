function rece = save_waveforms2dA(OM,rece,it)
% SAVE_WAVEFORMS

for ir = 1:length(rece)

    iom = rece(ir).iom;

    % invL
    invLxT11 = OM(iom).invLxT11;
    invLzT11 = OM(iom).invLzT11;
    invLxT22 = OM(iom).invLxT22;
    invLzT22 = OM(iom).invLzT22;

    % bs
    rbx1 = rece(ir).rbx1;
    rbx2 = rece(ir).rbx2;
    rbz1 = rece(ir).rbz1;
    rbz2 = rece(ir).rbz2;

    % U
    U21 = OM(iom).state.U21;   
    U12 = OM(iom).state.U12;
    
    % From Ux211, Ux121, Ux112 and Ux222 to Ux111
    % 1st: from hat to bspline
    Ub21 = tensorProduct2D(invLxT22,invLzT11,U21);
    Ub12 = tensorProduct2D(invLxT11,invLzT22,U12);
    % 2nd: from bspline to real Uxmid
    Umid1 = tensorProduct2D(rbx2,rbz1,Ub21);
    Umid2 = tensorProduct2D(rbx1,rbz2,Ub12);
 
    rece(ir).ur(it,2) = Umid1;
    rece(ir).ur(it,3) = Umid2;
  

end



end

