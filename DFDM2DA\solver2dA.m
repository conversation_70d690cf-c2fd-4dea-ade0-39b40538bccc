function [OM,rece] = solver2dA(OM,source,rece,dt,nt)
% SOLVER3D Summary of this function goes here
    
     
    for it=1:nt
        
        % update wavefield
        OM = update_wavefields2dA(OM,dt);
        
        % compute 4 inner boundary variables of all the domain
        OM = compute_boundary_Uvalue_inn2dA(OM);
        
        % compute 4 outer boundary variables of all the domain
        % with MPI communication
        OM = compute_boundary_Uvalue_out2dA(OM);
        
        % from displacement to strain
        OM = compute_KU2dA(OM);

        % compute 6 inner boundary variables of all the domain
        OM = compute_boundary_Svalue_inn2dA(OM);

        % compute 6 outer boundary variables of all the domain
        % with MPI communication
        OM = compute_boundary_Svalue_out2dA(OM);

        % from displacement to strain
        OM = compute_KS2dA(OM);

        % inject the source
        OM = add_source2dA(OM,source,it);

        % save waveforms
        rece = save_waveforms2dA(OM,rece,it);

        % save wavefields
        if mod(it,50)==0
            OM = save_wavefields2dA(OM,it);
            plot_wavefields2dA(OM,it);
        end

    end


end

