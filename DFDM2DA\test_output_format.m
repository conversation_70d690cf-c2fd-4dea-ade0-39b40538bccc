% TEST_OUTPUT_FORMAT 测试输出格式是否与C++一致
% 这个脚本用于验证 save_domain_data_cpp_format 函数的输出格式

% 创建一个简单的测试域结构
test_domain = struct();
test_domain.iom = 1;
test_domain.region = 1;
test_domain.x_min = 0.0;
test_domain.x_max = 1.0;
test_domain.z_min = 0.0;
test_domain.z_max = 1.0;
test_domain.Nx1 = 10;
test_domain.Nz1 = 10;
test_domain.Nx2 = 9;
test_domain.Nz2 = 9;
test_domain.px1 = 5;
test_domain.pz1 = 5;
test_domain.mu = 1.5125e+10;
test_domain.rho = 2000.0;
test_domain.iNbr_mo = -1;
test_domain.iNbr_po = -1;
test_domain.iNbr_om = -1;
test_domain.iNbr_op = -1;

% 创建一些测试矩阵
test_domain.bxT1 = rand(5, 10) * 1e-6;
test_domain.bxT2 = rand(4, 9) * 1e-6;
test_domain.bzT1 = rand(5, 10) * 1e-6;
test_domain.bzT2 = rand(4, 9) * 1e-6;

test_domain.kkx12 = rand(10, 9) * 1e3;
test_domain.kkx21 = rand(9, 10) * 1e3;
test_domain.kkz12 = rand(10, 9) * 1e3;
test_domain.kkz21 = rand(9, 10) * 1e3;

test_domain.Jac11 = rand(10, 10) * 1e8;
test_domain.Jac22 = rand(9, 9) * 1e8;

% 创建状态结构
test_domain.state = struct();
test_domain.state.U12 = rand(10, 9) * 1e-12;
test_domain.state.U21 = rand(9, 10) * 1e-12;
test_domain.state.Sxx11 = rand(10, 10) * 1e6;
test_domain.state.Szz11 = rand(10, 10) * 1e6;

% 创建边界向量
test_domain.state.U12mo = rand(10, 1) * 1e-12;
test_domain.state.U12po = rand(10, 1) * 1e-12;
test_domain.state.U21mo = rand(9, 1) * 1e-12;
test_domain.state.U21po = rand(9, 1) * 1e-12;

% 确保测试输出目录存在
test_output_dir = 'test_output';
if ~exist(test_output_dir, 'dir')
    mkdir(test_output_dir);
end

% 测试保存函数
fprintf('=== 测试输出格式 ===\n');
save_domain_data_cpp_format(test_domain, test_output_dir);
fprintf('=== 测试完成 ===\n');

% 验证文件是否创建
domain_dir = fullfile(test_output_dir, 'domain_0');
if exist(domain_dir, 'dir')
    files = dir(fullfile(domain_dir, '*.txt'));
    fprintf('\n创建的文件:\n');
    for i = 1:length(files)
        fprintf('  %s\n', files(i).name);
    end
    fprintf('\n总共创建了 %d 个文件\n', length(files));
else
    fprintf('❌ 错误: domain_0 目录未创建\n');
end
