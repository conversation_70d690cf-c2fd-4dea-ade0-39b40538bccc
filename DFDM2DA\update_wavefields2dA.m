function [OM] = update_wavefields2dA(OM,dt)
%UPDATE_WAVEFIELDS
dt2 = dt*dt;
for iom = 1:length(OM)

    OM(iom).state.U12   = 2*OM(iom).state.U12_1 - OM(iom).state.U12_0 + dt2*OM(iom).state.dU2dtt12;
    OM(iom).state.U12_0 = OM(iom).state.U12_1; 
    OM(iom).state.U12_1 = OM(iom).state.U12;
    

    OM(iom).state.U21   = 2*OM(iom).state.U21_1 - OM(iom).state.U21_0 + dt2*OM(iom).state.dU2dtt21;
    OM(iom).state.U21_0 = OM(iom).state.U21_1; 
    OM(iom).state.U21_1 = OM(iom).state.U21;
      
    
end


end

