=========== Mesh Validation ============
Total domains: 12
  Domain 0: region=2, nx=72, nz=63, Nx1=37, Nz1=28
  Domain 1: region=2, nx=72, nz=63, Nx1=37, Nz1=28
  Domain 2: region=4, nx=63, nz=72, Nx1=28, Nz1=37
  Domain 3: region=5, nx=60, nz=60, Nx1=20, Nz1=20
  Domain 4: region=5, nx=60, nz=60, Nx1=20, Nz1=20
  Domain 5: region=6, nx=63, nz=72, Nx1=28, Nz1=37
  Domain 6: region=4, nx=63, nz=72, Nx1=28, Nz1=37
  Domain 7: region=5, nx=60, nz=60, Nx1=20, Nz1=20
  Domain 8: region=5, nx=60, nz=60, Nx1=20, Nz1=20
  Domain 9: region=6, nx=63, nz=72, Nx1=28, Nz1=37
  Domain 10: region=8, nx=72, nz=63, Nx1=37, Nz1=28
  Domain 11: region=8, nx=72, nz=63, Nx1=37, Nz1=28
========================================
Extracting corner points for 12 domains...
Domain 0 corners: x=[-863731,-2.24386e-10,-424264,-4.8496e-11] z=[-863731,-1.2215e+06,-424264,-501588]
Domain 1 corners: x=[-2.24386e-10,863731,-4.8496e-11,424264] z=[-1.2215e+06,-863731,-501588,-424264]
...鎵惧鍩?0 鐨勯偦鍩?..
...鎵惧鍩?1 鐨勯偦鍩?..
...鎵惧鍩?2 鐨勯偦鍩?..
...鎵惧鍩?3 鐨勯偦鍩?..
...鎵惧鍩?4 鐨勯偦鍩?..
...鎵惧鍩?5 鐨勯偦鍩?..
...鎵惧鍩?6 鐨勯偦鍩?..
...鎵惧鍩?7 鐨勯偦鍩?..
...鎵惧鍩?8 鐨勯偦鍩?..
...鎵惧鍩?9 鐨勯偦鍩?..
...鎵惧鍩?10 鐨勯偦鍩?..
...鎵惧鍩?11 鐨勯偦鍩?..
...鏌ユ壘鍩?0 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?1 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?2 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?3 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?4 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?5 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?6 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?7 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?8 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?9 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?10 鐨勫尮閰嶉潰...
...鏌ユ壘鍩?11 鐨勫尮閰嶉潰...
[find_connections2dA] 宸蹭负 12 涓煙寤虹珛杩炴帴鍏崇郴
璁＄畻鐨勬椂闂存闀縟t = 0.1, 鎬绘椂闂存鏁皀t = 4800
Initializing domains with nt = 4800...
... Initialize the state of 1 Domain ...
  璁剧疆鍩?0 缃戞牸鍙傛暟: Nx1=37, Nz1=28, Nx2=36, Nz2=27
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 2 Domain ...
  璁剧疆鍩?1 缃戞牸鍙傛暟: Nx1=37, Nz1=28, Nx2=36, Nz2=27
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 3 Domain ...
  璁剧疆鍩?2 缃戞牸鍙傛暟: Nx1=28, Nz1=37, Nx2=27, Nz2=36
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 4 Domain ...
  璁剧疆鍩?3 缃戞牸鍙傛暟: Nx1=20, Nz1=20, Nx2=19, Nz2=19
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 5 Domain ...
  璁剧疆鍩?4 缃戞牸鍙傛暟: Nx1=20, Nz1=20, Nx2=19, Nz2=19
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 6 Domain ...
  璁剧疆鍩?5 缃戞牸鍙傛暟: Nx1=28, Nz1=37, Nx2=27, Nz2=36
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 7 Domain ...
  璁剧疆鍩?6 缃戞牸鍙傛暟: Nx1=28, Nz1=37, Nx2=27, Nz2=36
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 8 Domain ...
  璁剧疆鍩?7 缃戞牸鍙傛暟: Nx1=20, Nz1=20, Nx2=19, Nz2=19
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 9 Domain ...
  璁剧疆鍩?8 缃戞牸鍙傛暟: Nx1=20, Nz1=20, Nx2=19, Nz2=19
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 10 Domain ...
  璁剧疆鍩?9 缃戞牸鍙傛暟: Nx1=28, Nz1=37, Nx2=27, Nz2=36
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 11 Domain ...
  璁剧疆鍩?10 缃戞牸鍙傛暟: Nx1=37, Nz1=28, Nx2=36, Nz2=27
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... Initialize the state of 12 Domain ...
  璁剧疆鍩?11 缃戞牸鍙傛暟: Nx1=37, Nz1=28, Nx2=36, Nz2=27
  Allocating Umid storage for 4800 timesteps
  璺宠繃闆呭彲姣旂煩闃靛垵濮嬪寲锛岀瓑寰?refine_model2dA 姝ｇ‘璁＄畻
... set the alpha values of 1 Domain ...
... set the alpha values of 2 Domain ...
... set the alpha values of 3 Domain ...
... set the alpha values of 4 Domain ...
... set the alpha values of 5 Domain ...
... set the alpha values of 6 Domain ...
... set the alpha values of 7 Domain ...
... set the alpha values of 8 Domain ...
... set the alpha values of 9 Domain ...
... set the alpha values of 10 Domain ...
... set the alpha values of 11 Domain ...
... set the alpha values of 12 Domain ...
[refine_model2dA] Processing 12 domains
Processing domain 0...
  Domain 0 grid size: 37x28
  璁＄畻寰楀埌 Nx2=36, Nz2=27
  Topography grid size: 72x63
  Hat points: 37 and 28
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 0 boundaries: x=[-863.731, -2.73113e-13] km, z=[-1221.5, -424.264] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 36, dom.Nz2 = 27
  dxpdx11 鍊艰寖鍥? [7.40509e-07, 2.23983e-06]
  Domain 0 processing completed
Processing domain 1...
  Domain 1 grid size: 37x28
  璁＄畻寰楀埌 Nx2=36, Nz2=27
  Topography grid size: 72x63
  Hat points: 37 and 28
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 1 boundaries: x=[1.32356e-13, 863.731] km, z=[-1221.5, -424.264] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 36, dom.Nz2 = 27
  dxpdx11 鍊艰寖鍥? [7.40509e-07, 2.23983e-06]
  Domain 1 processing completed
Processing domain 2...
  Domain 2 grid size: 28x37
  璁＄畻寰楀埌 Nx2=27, Nz2=36
  Topography grid size: 63x72
  Hat points: 28 and 37
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 2 boundaries: x=[-1221.5, -424.264] km, z=[-863.731, -1.92286e-13] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 27, dom.Nz2 = 36
  dxpdx11 鍊艰寖鍥? [1.13914e-06, 1.64456e-06]
  Domain 2 processing completed
Processing domain 3...
  Domain 3 grid size: 20x20
  璁＄畻寰楀埌 Nx2=19, Nz2=19
  Topography grid size: 60x60
  Hat points: 20 and 20
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 3 boundaries: x=[-501.588, -3.14035e-13] km, z=[-501.588, -2.53398e-13] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 19, dom.Nz2 = 19
  dxpdx11 鍊艰寖鍥? [1.6315e-06, 2.55324e-06]
  Domain 3 processing completed
Processing domain 4...
  Domain 4 grid size: 20x20
  璁＄畻寰楀埌 Nx2=19, Nz2=19
  Topography grid size: 60x60
  Hat points: 20 and 20
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 4 boundaries: x=[4.39788e-14, 501.588] km, z=[-501.588, -2.84972e-13] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 19, dom.Nz2 = 19
  dxpdx11 鍊艰寖鍥? [1.6315e-06, 2.55324e-06]
  Domain 4 processing completed
Processing domain 5...
  Domain 5 grid size: 28x37
  璁＄畻寰楀埌 Nx2=27, Nz2=36
  Topography grid size: 63x72
  Hat points: 28 and 37
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 5 boundaries: x=[424.264, 1221.5] km, z=[-863.731, -2.89278e-13] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 27, dom.Nz2 = 36
  dxpdx11 鍊艰寖鍥? [1.13914e-06, 1.64456e-06]
  Domain 5 processing completed
Processing domain 6...
  Domain 6 grid size: 28x37
  璁＄畻寰楀埌 Nx2=27, Nz2=36
  Topography grid size: 63x72
  Hat points: 28 and 37
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 6 boundaries: x=[-1221.5, -424.264] km, z=[2.15255e-13, 863.731] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 27, dom.Nz2 = 36
  dxpdx11 鍊艰寖鍥? [1.13914e-06, 1.64456e-06]
  Domain 6 processing completed
Processing domain 7...
  Domain 7 grid size: 20x20
  璁＄畻寰楀埌 Nx2=19, Nz2=19
  Topography grid size: 60x60
  Hat points: 20 and 20
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 7 boundaries: x=[-501.588, -2.69564e-13] km, z=[1.06542e-13, 501.588] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 19, dom.Nz2 = 19
  dxpdx11 鍊艰寖鍥? [1.6315e-06, 2.55324e-06]
  Domain 7 processing completed
Processing domain 8...
  Domain 8 grid size: 20x20
  璁＄畻寰楀埌 Nx2=19, Nz2=19
  Topography grid size: 60x60
  Hat points: 20 and 20
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 8 boundaries: x=[9.64659e-14, 501.588] km, z=[8.51612e-14, 501.588] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 19, dom.Nz2 = 19
  dxpdx11 鍊艰寖鍥? [1.6315e-06, 2.55324e-06]
  Domain 8 processing completed
Processing domain 9...
  Domain 9 grid size: 28x37
  璁＄畻寰楀埌 Nx2=27, Nz2=36
  Topography grid size: 63x72
  Hat points: 28 and 37
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 9 boundaries: x=[424.264, 1221.5] km, z=[5.7561e-14, 863.731] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 27, dom.Nz2 = 36
  dxpdx11 鍊艰寖鍥? [1.13914e-06, 1.64456e-06]
  Domain 9 processing completed
Processing domain 10...
  Domain 10 grid size: 37x28
  璁＄畻寰楀埌 Nx2=36, Nz2=27
  Topography grid size: 72x63
  Hat points: 37 and 28
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 10 boundaries: x=[-863.731, -2.08452e-13] km, z=[424.264, 1221.5] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 36, dom.Nz2 = 27
  dxpdx11 鍊艰寖鍥? [7.40509e-07, 2.23983e-06]
  Domain 10 processing completed
Processing domain 11...
  Domain 11 grid size: 37x28
  璁＄畻寰楀埌 Nx2=36, Nz2=27
  Topography grid size: 72x63
  Hat points: 37 and 28
  Building B-spline basis matrices...
  Computing matrix inverses...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Computing B-spline coefficients...
  Building plotting basis matrices...
  Computing coordinate grids...
  Computing hat points...
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
  Building hat point basis matrices...
  Building derivative basis matrices...
  Computing coordinates at hat points...
  Computing spatial derivatives...
  Computing Jacobian matrices...
  Computing inverse coordinate derivatives...
  Storing results...
  Domain 11 boundaries: x=[1.9909e-13, 863.731] km, z=[424.264, 1221.5] km
  鉁?璁剧疆鍩熺粨鏋勪綋: dom.Nx2 = 36, dom.Nz2 = 27
  dxpdx11 鍊艰寖鍥? [7.40509e-07, 2.23983e-06]
  Domain 11 processing completed
[refine_model2dA] All domains processed successfully
...preparing all the DFD Matrices of domain 1 with 37 and 28 points ...
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms

=== inner_product2 璋冪敤 #1 鍏抽敭鏁版嵁 ===
  N1=27, M2=28, ord_gi=6, NB=23
  int12 size: 23x6
  wint12 size: 23x6
  鉁?鐭╅樀灏哄姝ｇ‘
  int12[0,0:2] = [0.00146805, 0.00736501, 0.0165518]
  wint12[0,0:2] = [0.00372445, 0.00784264, 0.010172]
  鎵€鏈夌Н鍒嗙偣鍜屾潈閲嶉兘鏈夋晥
=== inner_product2 鏁版嵁杈撳嚭瀹屾垚 ===

[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 484 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms

=== inner_product2 璋冪敤 #2 鍏抽敭鏁版嵁 ===
  N1=28, M2=27, ord_gi=6, NB=23
  int12 size: 23x6
  wint12 size: 23x6
  鉁?鐭╅樀灏哄姝ｇ‘
  int12[0,0:2] = [0.00146805, 0.00736501, 0.0165518]
  wint12[0,0:2] = [0.00372445, 0.00784264, 0.010172]
  鎵€鏈夌Н鍒嗙偣鍜屾潈閲嶉兘鏈夋晥
=== inner_product2 鏁版嵁杈撳嚭瀹屾垚 ===

[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 515 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms

=== inner_product2 璋冪敤 #3 鍏抽敭鏁版嵁 ===
  N1=28, M2=28, ord_gi=6, NB=23
  int12 size: 23x6
  wint12 size: 23x6
  鉁?鐭╅樀灏哄姝ｇ‘
  int12[0,0:2] = [0.00146805, 0.00736501, 0.0165518]
  wint12[0,0:2] = [0.00372445, 0.00784264, 0.010172]
  鎵€鏈夌Н鍒嗙偣鍜屾潈閲嶉兘鏈夋晥
=== inner_product2 鏁版嵁杈撳嚭瀹屾垚 ===

[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 651 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 303 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 465 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 447 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 636 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 291 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 812 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 806 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1105 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 526 ms
  All DFD matrices computed successfully for domain 1
...preparing all the DFD Matrices of domain 2 with 37 and 28 points ...
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 434 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 3 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 427 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 599 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 291 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 435 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 440 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 594 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 284 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 796 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 792 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1099 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 516 ms
  All DFD matrices computed successfully for domain 2
...preparing all the DFD Matrices of domain 3 with 28 and 37 points ...
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 800 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 791 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1098 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 525 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 426 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 434 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 577 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 280 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 430 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 432 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 581 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 280 ms
  All DFD matrices computed successfully for domain 3
...preparing all the DFD Matrices of domain 4 with 20 and 20 points ...
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 790 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 805 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1084 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 498 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 135 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 138 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 191 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 89 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 783 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 809 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1104 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 515 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 143 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 148 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 189 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 91 ms
  All DFD matrices computed successfully for domain 4
...preparing all the DFD Matrices of domain 5 with 20 and 20 points ...
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 143 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 148 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 197 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 98 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 796 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 793 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1084 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 503 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 787 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 789 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1081 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 518 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 138 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 141 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 199 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 95 ms
  All DFD matrices computed successfully for domain 5
...preparing all the DFD Matrices of domain 6 with 28 and 37 points ...
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 3 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 788 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 786 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1079 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 523 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 435 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 424 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 585 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 279 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 429 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 428 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 582 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 282 ms
  All DFD matrices computed successfully for domain 6
...preparing all the DFD Matrices of domain 7 with 28 and 37 points ...
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 3 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 796 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 798 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1094 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 518 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 437 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 428 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 591 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 279 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 436 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 438 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 594 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 277 ms
  All DFD matrices computed successfully for domain 7
...preparing all the DFD Matrices of domain 8 with 20 and 20 points ...
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 789 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 791 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1103 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 522 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 140 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 137 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 195 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 95 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 148 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 142 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 214 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 93 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 790 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 791 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1105 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 518 ms
  All DFD matrices computed successfully for domain 8
...preparing all the DFD Matrices of domain 9 with 20 and 20 points ...
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=0, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
DEBUG: stiffness_matrix option=1, N1=20, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 143 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 143 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 198 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 92 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 806 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 803 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1083 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 519 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 138 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/380)
[PROGRESS] inner_product2 10% (38/380)
[PROGRESS] inner_product2 20% (76/380)
[PROGRESS] inner_product2 30% (114/380)
[PROGRESS] inner_product2 40% (152/380)
[PROGRESS] inner_product2 50% (190/380)
[PROGRESS] inner_product2 60% (228/380)
[PROGRESS] inner_product2 70% (266/380)
[PROGRESS] inner_product2 80% (304/380)
[PROGRESS] inner_product2 90% (342/380)
[PROGRESS] inner_product2 100% (380/380)
[PROGRESS] Completed inner_product2 (380/380)
[TIMER] Inner product computation took 144 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/400)
[PROGRESS] inner_product2 10% (40/400)
[PROGRESS] inner_product2 20% (80/400)
[PROGRESS] inner_product2 30% (120/400)
[PROGRESS] inner_product2 40% (160/400)
[PROGRESS] inner_product2 50% (200/400)
[PROGRESS] inner_product2 60% (240/400)
[PROGRESS] inner_product2 70% (280/400)
[PROGRESS] inner_product2 80% (320/400)
[PROGRESS] inner_product2 90% (360/400)
[PROGRESS] inner_product2 100% (400/400)
[PROGRESS] Completed inner_product2 (400/400)
[TIMER] Inner product computation took 202 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/361)
[PROGRESS] inner_product2 10% (37/361)
[PROGRESS] inner_product2 20% (73/361)
[PROGRESS] inner_product2 30% (109/361)
[PROGRESS] inner_product2 40% (145/361)
[PROGRESS] inner_product2 50% (181/361)
[PROGRESS] inner_product2 60% (217/361)
[PROGRESS] inner_product2 70% (253/361)
[PROGRESS] inner_product2 80% (289/361)
[PROGRESS] inner_product2 90% (325/361)
[PROGRESS] inner_product2 100% (361/361)
[PROGRESS] Completed inner_product2 (361/361)
[TIMER] Inner product computation took 98 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 802 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 784 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1092 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 519 ms
  All DFD matrices computed successfully for domain 9
...preparing all the DFD Matrices of domain 10 with 28 and 37 points ...
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 784 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 781 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1089 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 513 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 434 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 422 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 591 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 275 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 420 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 428 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 580 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 269 ms
  All DFD matrices computed successfully for domain 10
...preparing all the DFD Matrices of domain 11 with 37 and 28 points ...
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 421 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 420 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 577 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 268 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 418 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 418 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 571 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 267 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 776 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 784 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1080 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 498 ms
  All DFD matrices computed successfully for domain 11
...preparing all the DFD Matrices of domain 12 with 37 and 28 points ...
DEBUG: stiffness_matrix option=0, N1=37, p=5
DEBUG: stiffness_matrix option=0, N1=28, p=5
DEBUG: stiffness_matrix option=1, N1=37, p=5
DEBUG: stiffness_matrix option=1, N1=28, p=5
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
Warning: inv - Using SVD pseudo-inverse for robust inversion.
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 0 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 418 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 419 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 580 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 285 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 426 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/756)
[PROGRESS] inner_product2 10% (76/756)
[PROGRESS] inner_product2 20% (152/756)
[PROGRESS] inner_product2 30% (227/756)
[PROGRESS] inner_product2 40% (303/756)
[PROGRESS] inner_product2 50% (378/756)
[PROGRESS] inner_product2 60% (454/756)
[PROGRESS] inner_product2 70% (530/756)
[PROGRESS] inner_product2 80% (605/756)
[PROGRESS] inner_product2 90% (681/756)
[PROGRESS] inner_product2 100% (756/756)
[PROGRESS] Completed inner_product2 (756/756)
[TIMER] Inner product computation took 430 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/784)
[PROGRESS] inner_product2 10% (79/784)
[PROGRESS] inner_product2 20% (157/784)
[PROGRESS] inner_product2 30% (236/784)
[PROGRESS] inner_product2 40% (314/784)
[PROGRESS] inner_product2 50% (392/784)
[PROGRESS] inner_product2 60% (471/784)
[PROGRESS] inner_product2 70% (549/784)
[PROGRESS] inner_product2 80% (628/784)
[PROGRESS] inner_product2 90% (706/784)
[PROGRESS] inner_product2 100% (784/784)
[PROGRESS] Completed inner_product2 (784/784)
[TIMER] Inner product computation took 583 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/729)
[PROGRESS] inner_product2 10% (73/729)
[PROGRESS] inner_product2 20% (146/729)
[PROGRESS] inner_product2 30% (219/729)
[PROGRESS] inner_product2 40% (292/729)
[PROGRESS] inner_product2 50% (365/729)
[PROGRESS] inner_product2 60% (438/729)
[PROGRESS] inner_product2 70% (511/729)
[PROGRESS] inner_product2 80% (584/729)
[PROGRESS] inner_product2 90% (657/729)
[PROGRESS] inner_product2 100% (729/729)
[PROGRESS] Completed inner_product2 (729/729)
[TIMER] Inner product computation took 272 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/720)
[PROGRESS] inner_product2 10% (72/720)
[PROGRESS] inner_product2 20% (144/720)
[PROGRESS] inner_product2 30% (216/720)
[PROGRESS] inner_product2 40% (288/720)
[PROGRESS] inner_product2 50% (360/720)
[PROGRESS] inner_product2 60% (432/720)
[PROGRESS] inner_product2 70% (504/720)
[PROGRESS] inner_product2 80% (576/720)
[PROGRESS] inner_product2 90% (648/720)
[PROGRESS] inner_product2 100% (720/720)
[PROGRESS] Completed inner_product2 (720/720)
[TIMER] Inner product computation took 779 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 2 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/703)
[PROGRESS] inner_product2 10% (71/703)
[PROGRESS] inner_product2 20% (141/703)
[PROGRESS] inner_product2 30% (211/703)
[PROGRESS] inner_product2 40% (282/703)
[PROGRESS] inner_product2 50% (352/703)
[PROGRESS] inner_product2 60% (422/703)
[PROGRESS] inner_product2 70% (493/703)
[PROGRESS] inner_product2 80% (563/703)
[PROGRESS] inner_product2 90% (633/703)
[PROGRESS] inner_product2 100% (703/703)
[PROGRESS] Completed inner_product2 (703/703)
[TIMER] Inner product computation took 779 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/740)
[PROGRESS] inner_product2 10% (74/740)
[PROGRESS] inner_product2 20% (148/740)
[PROGRESS] inner_product2 30% (222/740)
[PROGRESS] inner_product2 40% (296/740)
[PROGRESS] inner_product2 50% (370/740)
[PROGRESS] inner_product2 60% (444/740)
[PROGRESS] inner_product2 70% (518/740)
[PROGRESS] inner_product2 80% (592/740)
[PROGRESS] inner_product2 90% (666/740)
[PROGRESS] inner_product2 100% (740/740)
[PROGRESS] Completed inner_product2 (740/740)
[TIMER] Inner product computation took 1063 ms
[INFO] inner_product2 - Entering critical section: Quadrature setup
[TIMER] Quadrature setup took 1 ms
[INFO] inner_product2 - Entering critical section: Inner product computation
[PROGRESS] Starting inner_product2 (0/684)
[PROGRESS] inner_product2 10% (69/684)
[PROGRESS] inner_product2 20% (137/684)
[PROGRESS] inner_product2 30% (206/684)
[PROGRESS] inner_product2 40% (274/684)
[PROGRESS] inner_product2 50% (342/684)
[PROGRESS] inner_product2 60% (411/684)
[PROGRESS] inner_product2 70% (479/684)
[PROGRESS] inner_product2 80% (548/684)
[PROGRESS] inner_product2 90% (616/684)
[PROGRESS] inner_product2 100% (684/684)
[PROGRESS] Completed inner_product2 (684/684)
[TIMER] Inner product computation took 498 ms
  All DFD matrices computed successfully for domain 12
gen_DFDMatrices2dA completed successfully for all 12 domains
Time step 1/4800 (0.0208333%) Elapsed: 0s
  [DEBUG] update_wavefields2dA: 寮€濮嬫洿鏂帮紝dt = 0.1
  [DEBUG] Domain 0: U12 size = 37x27, dU2dtt12 size = 37x27
  [DEBUG] Domain 1: U12 size = 37x27, dU2dtt12 size = 37x27
  [DEBUG] Domain 2: U12 size = 28x36, dU2dtt12 size = 28x36
  [DEBUG] Domain 3: U12 size = 20x19, dU2dtt12 size = 20x19
  [DEBUG] Domain 4: U12 size = 20x19, dU2dtt12 size = 20x19
  [DEBUG] Domain 5: U12 size = 28x36, dU2dtt12 size = 28x36
  [DEBUG] Domain 6: U12 size = 28x36, dU2dtt12 size = 28x36
  [DEBUG] Domain 7: U12 size = 20x19, dU2dtt12 size = 20x19
  [DEBUG] Domain 8: U12 size = 20x19, dU2dtt12 size = 20x19
  [DEBUG] Domain 9: U12 size = 28x36, dU2dtt12 size = 28x36
  [DEBUG] Domain 10: U12 size = 37x27, dU2dtt12 size = 37x27
  [DEBUG] Domain 11: U12 size = 37x27, dU2dtt12 size = 37x27
  [DEBUG] update_wavefields2dA: 鎵€鏈夊煙娉㈠満鏇存柊瀹屾垚
Error at time step 1: pagemtimes: dimension mismatch

=== 鍩?0 鏈€缁堢姸鎬佽皟璇曚俊鎭?===
Nx1=37, Nz1=28
Nx2=36, Nz2=27
鍩熻竟鐣? x=[-863731, -2.73113e-10], z=[-1.2215e+06, -424264]
dxpdx11 鐭╅樀: 37x28, 鍊艰寖鍥? [7.40509e-07, 2.23983e-06]
x2d11 鐭╅樀: 37x28, 鍊艰寖鍥? [-863731, -2.73113e-10]
================================

=== 淇濆瓨鍙傛暟鍒? D:\project\ware\output_cpp\domain_0\parameters.txt ===
iom = 0
region = 2
x_min = -8.6373093321936531e+05
x_max = -2.7311296299116172e-10
z_min = -1.2215000000000005e+06
z_max = -4.2426406871192728e+05
Nx1 = 37
Nz1 = 28
Nx2 = 36
Nz2 = 27
px1 = 5
pz1 = 5
mu = 1.5125000000000000e+10
rho = 2.0000000000000000e+03
iNbr_mo = 3
iNbr_po = 2
iNbr_om = 0
iNbr_op = 4
鉁?鍙傛暟鏂囦欢宸蹭繚瀛

淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\bxT1.txt (37x37)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\bxT2.txt (37x36)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\bzT1.txt (28x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\bzT2.txt (28x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛

=== 闆呭彲姣旂煩闃垫鏌?===
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dxpdx11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dxpdx22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dxpdz11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dxpdz22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dzpdx11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dzpdx22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dzpdz11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\dzpdz22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛

=== 闆呭彲姣旂煩闃佃皟璇曞畬鎴?===
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLx11.txt (37x37)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLx22.txt (36x36)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLxT11.txt (37x37)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLxT22.txt (36x36)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLz11.txt (28x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLz22.txt (27x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLzT11.txt (28x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\invLzT22.txt (27x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\Jac11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\Jac22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\kkx12.txt (37x36)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\kkx21.txt (36x37)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\kkz12.txt (28x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\kkz21.txt (27x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\mu11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\mu22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\state_Sxx11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\state_Sxx22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\state_Szz11.txt (37x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\state_Szz22.txt (36x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\state_U12.txt (37x27)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鐭╅樀鍒? D:\project\ware\output_cpp\domain_0\state_U21.txt (36x28)
鉁?鐭╅樀鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U12mo.txt (27 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U12om.txt (37 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U12op.txt (37 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U12po.txt (27 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U21mo.txt (28 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U21om.txt (36 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U21op.txt (36 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛
淇濆瓨鍚戦噺鍒? D:\project\ware\output_cpp\domain_0\state_U21po.txt (28 鍏冪礌)
鉁?鍚戦噺鏂囦欢宸蹭繚瀛

D:\project\ware\out\build\x64-Debug\main2dA_d.exe (进程 10952)已退出，代码为 0 (0x0)。
要在调试停止时自动关闭控制台，请启用“工具”->“选项”->“调试”->“调试停止时自动关闭控制台”。
按任意键关闭此窗口. . .