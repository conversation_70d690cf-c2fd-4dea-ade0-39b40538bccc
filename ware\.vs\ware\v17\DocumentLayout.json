{"Version": 1, "WorkspaceRootPath": "D:\\project\\ware\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Eigen\\eigen-3.4\\eigen-3.4.0\\eigen-3.4.0\\Eigen\\src\\Core\\GenericPacketMath.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\src\\main2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\main2dA.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:2:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:2:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:1:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\project\\ware\\CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:1:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CMakeLists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "GenericPacketMath.h", "DocumentMoniker": "D:\\Eigen\\eigen-3.4\\eigen-3.4.0\\eigen-3.4.0\\Eigen\\src\\Core\\GenericPacketMath.h", "RelativeDocumentMoniker": "..\\..\\Eigen\\eigen-3.4\\eigen-3.4.0\\eigen-3.4.0\\Eigen\\src\\Core\\GenericPacketMath.h", "ToolTip": "D:\\Eigen\\eigen-3.4\\eigen-3.4.0\\eigen-3.4.0\\Eigen\\src\\Core\\GenericPacketMath.h", "RelativeToolTip": "..\\..\\Eigen\\eigen-3.4\\eigen-3.4.0\\eigen-3.4.0\\Eigen\\src\\Core\\GenericPacketMath.h", "ViewState": "AgIAAJMAAAAAAAAAAAAkwKIAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-31T23:54:43.333Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "main2dA.cpp", "DocumentMoniker": "D:\\project\\ware\\src\\main2dA.cpp", "RelativeDocumentMoniker": "src\\main2dA.cpp", "ToolTip": "D:\\project\\ware\\src\\main2dA.cpp", "RelativeToolTip": "src\\main2dA.cpp", "ViewState": "AgIAAAoAAAAAAAAAAAAkwB0AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-31T13:19:11.365Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\project\\ware\\CMakeLists.txt", "RelativeDocumentMoniker": "CMakeLists.txt", "ToolTip": "D:\\project\\ware\\CMakeLists.txt", "RelativeToolTip": "CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T11:18:12.462Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\project\\ware\\CMakeLists.txt", "RelativeDocumentMoniker": "CMakeLists.txt", "ToolTip": "D:\\project\\ware\\CMakeLists.txt", "RelativeToolTip": "CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-07-31T13:06:41.83Z"}]}]}]}