{"cmake.configureOnOpen": true, "cmake.buildDirectory": "${workspaceFolder}/out/build/${buildType}", "cmake.generator": "Ninja", "cmake.buildType": "Debug", "cmake.configureArgs": ["-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"], "cmake.configureSettings": {"CMAKE_TOOLCHAIN_FILE": "C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake"}, "files.associations": {"*.hpp": "cpp", "*.cpp": "cpp", "iostream": "cpp", "vector": "cpp", "string": "cpp", "fstream": "cpp", "cmath": "cpp", "algorithm": "cpp", "memory": "cpp", "functional": "cpp"}, "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64", "C_Cpp.default.cppStandard": "c++17"}