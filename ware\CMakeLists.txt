# ========================================
# DFDM2DA CMakeLists.txt - 优化版本
# ========================================

cmake_minimum_required(VERSION 3.16)

# 在Windows上优先使用MSBuild而不是Ninja
if(WIN32 AND NOT DEFINED CMAKE_GENERATOR)
    set(CMAKE_GENERATOR "Visual Studio 17 2022")
endif()

project(DFDM2DA LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Windows特定设置
if(WIN32)
    # 避免路径长度问题
    set(CMAKE_OBJECT_PATH_MAX 260)
    
    # 设置编码
    add_compile_options("$<$<C_COMPILER_ID:MSVC>:/utf-8>")
    add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
    
    # 避免并行链接问题
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /bigobj")
endif()

# Visual Studio specific settings
if(CMAKE_GENERATOR MATCHES "Visual Studio")
    message(STATUS "Configuring for Visual Studio")
    set(CMAKE_VS_INCLUDE_INSTALL_TO_DEFAULT_BUILD 1)
    # Set main2dA as the startup project in Visual Studio
    set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT main2dA)
    message(STATUS "✓ Set main2dA as startup project")
endif()

# ========================================
# Dependency Detection
# ========================================

# Find Eigen (Required)
message(STATUS "Searching for Eigen...")
set(EIGEN_PATHS
    "D:/Eigen/eigen-3.4/eigen-3.4.0/eigen-3.4.0"
    "C:/eigen"
    "C:/vcpkg/installed/x64-windows/include"
    "$ENV{EIGEN_ROOT}"
    "${CMAKE_CURRENT_SOURCE_DIR}/../eigen"
)

set(EIGEN_FOUND FALSE)
foreach(PATH ${EIGEN_PATHS})
    if(PATH AND EXISTS "${PATH}/Eigen/Dense")
        set(EIGEN_INCLUDE_DIR "${PATH}")
        set(EIGEN_FOUND TRUE)
        message(STATUS "✓ Found Eigen at: ${PATH}")
        break()
    endif()
endforeach()

if(NOT EIGEN_FOUND)
    message(FATAL_ERROR "❌ Eigen not found! Please install Eigen or set EIGEN_ROOT environment variable.")
endif()

# Find OpenBLAS (Optional but recommended)
message(STATUS "Searching for OpenBLAS...")
set(OPENBLAS_PATHS
    "D:/OpenBLAS-0.3.30-x64"
    "C:/OpenBLAS"
    "C:/vcpkg/installed/x64-windows"
    "$ENV{OPENBLAS_ROOT}"
)

set(OPENBLAS_FOUND FALSE)
foreach(PATH ${OPENBLAS_PATHS})
    if(PATH AND EXISTS "${PATH}/include/cblas.h")
        find_library(OPENBLAS_LIB
            NAMES openblas libopenblas
            PATHS "${PATH}/lib" "${PATH}/bin"
            NO_DEFAULT_PATH
        )
        if(OPENBLAS_LIB)
            set(OPENBLAS_INCLUDE_DIR "${PATH}/include")
            set(OPENBLAS_FOUND TRUE)
            message(STATUS "✓ Found OpenBLAS at: ${PATH}")
            message(STATUS "  Library: ${OPENBLAS_LIB}")
            break()
        endif()
    endif()
endforeach()

if(NOT OPENBLAS_FOUND)
    message(STATUS "⚠ OpenBLAS not found - using Eigen-only implementation")
endif()

# ========================================
# Source Files Collection
# ========================================

# Collect all source files
file(GLOB_RECURSE ALL_SOURCES
    "src/*.cpp"
)

# Remove any duplicate header files that might be in src/
list(FILTER ALL_SOURCES EXCLUDE REGEX ".*\\.hpp$")

# Remove test files from main build
list(FILTER ALL_SOURCES EXCLUDE REGEX ".*test.*\\.cpp$")

# Ensure main2dA.cpp is included
if(NOT "${CMAKE_CURRENT_SOURCE_DIR}/src/main2dA.cpp" IN_LIST ALL_SOURCES)
    message(FATAL_ERROR "❌ main2dA.cpp not found in src/ directory")
endif()

list(LENGTH ALL_SOURCES SOURCE_COUNT)
message(STATUS "Found ${SOURCE_COUNT} source files for compilation")

# ========================================
# Main Executable Target
# ========================================

add_executable(main2dA ${ALL_SOURCES})

# ========================================
# Target Configuration
# ========================================

# Set target properties
set_target_properties(main2dA PROPERTIES
    OUTPUT_NAME "main2dA"
    DEBUG_POSTFIX "_d"
    # Visual Studio specific properties for direct execution
    VS_DEBUGGER_WORKING_DIRECTORY "${CMAKE_CURRENT_SOURCE_DIR}"
    VS_DEBUGGER_COMMAND_ARGUMENTS ""
    VS_DEBUGGER_ENVIRONMENT ""
)

# Include directories
target_include_directories(main2dA PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Add Eigen support
if(EIGEN_FOUND)
    target_compile_definitions(main2dA PRIVATE USE_EIGEN)
    target_include_directories(main2dA PRIVATE ${EIGEN_INCLUDE_DIR})
    message(STATUS "✓ Eigen support enabled")
endif()

# Add OpenBLAS support
if(OPENBLAS_FOUND)
    target_compile_definitions(main2dA PRIVATE USE_OPENBLAS)
    target_include_directories(main2dA PRIVATE ${OPENBLAS_INCLUDE_DIR})
    target_link_libraries(main2dA ${OPENBLAS_LIB})
    message(STATUS "✓ OpenBLAS support enabled")
endif()

# ========================================
# Compiler-Specific Settings
# ========================================

# Visual Studio / MSVC Optimizations
if(MSVC)
    message(STATUS "Applying MSVC/Visual Studio optimizations...")

    # Core compiler flags for maximum compatibility
    target_compile_options(main2dA PRIVATE
        /utf-8              # Force UTF-8 encoding (fixes C4819)
        /wd4819             # Disable encoding warnings
        /wd4996             # Disable security warnings
        /wd4267             # Disable size_t conversion warnings
        /wd4244             # Disable conversion warnings
        /wd4305             # Disable truncation warnings
        /wd4018             # Disable signed/unsigned mismatch warnings
        /W1                 # Warning level 1 (minimal warnings)
        /bigobj             # Support large object files
        /EHsc               # Exception handling model
        /permissive-        # Strict standard conformance
        /Zc:__cplusplus     # Enable correct __cplusplus macro
    )

    # Essential preprocessor definitions
    target_compile_definitions(main2dA PRIVATE
        _CRT_SECURE_NO_WARNINGS     # Disable CRT security warnings
        NOMINMAX                     # Prevent min/max macro conflicts
        _USE_MATH_DEFINES           # Enable M_PI and other math constants
        WIN32_LEAN_AND_MEAN         # Reduce Windows header bloat
        _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS  # Silence C++17 warnings
    )

    # Add MATLAB compatibility definition
    if(MATLAB_COMPATIBLE_BOUNDS)
        target_compile_definitions(main2dA PRIVATE MATLAB_COMPATIBLE_BOUNDS=1)
        message(STATUS "✓ MATLAB-compatible bounds checking enabled")
    endif()

    # Configuration-specific optimizations
    target_compile_options(main2dA PRIVATE
        $<$<CONFIG:Debug>:/Od>          # Debug: No optimization
        $<$<CONFIG:Debug>:/Zi>          # Debug: Full debug info
        $<$<CONFIG:Debug>:/RTC1>        # Debug: Runtime checks
        $<$<CONFIG:Release>:/O2>        # Release: Full optimization
        $<$<CONFIG:Release>:/Ob2>       # Release: Inline expansion
        $<$<CONFIG:Release>:/DNDEBUG>   # Release: Disable assertions
    )

    # Linker optimizations for Release
    target_link_options(main2dA PRIVATE
        $<$<CONFIG:Release>:/OPT:REF>   # Remove unreferenced functions
        $<$<CONFIG:Release>:/OPT:ICF>   # Identical COMDAT folding
    )

    message(STATUS "✓ MSVC optimizations applied")

# GCC/Clang settings (fallback)
else()
    target_compile_options(main2dA PRIVATE
        -Wall
        -Wextra
        -O3
        $<$<CONFIG:Debug>:-g>
        $<$<CONFIG:Debug>:-O0>
    )
    message(STATUS "✓ GCC/Clang optimizations applied")
endif()



# ========================================
# Configuration Summary
# ========================================

message(STATUS "")
message(STATUS "========================================")
message(STATUS "DFDM2DA Configuration Summary")
message(STATUS "========================================")
message(STATUS "Generator:        ${CMAKE_GENERATOR}")
message(STATUS "Build type:       ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard:     C++${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler:         ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  Eigen:          ${EIGEN_FOUND}")
if(EIGEN_FOUND)
    message(STATUS "    Path:         ${EIGEN_INCLUDE_DIR}")
endif()
message(STATUS "  OpenBLAS:       ${OPENBLAS_FOUND}")
if(OPENBLAS_FOUND)
    message(STATUS "    Path:         ${OPENBLAS_INCLUDE_DIR}")
    message(STATUS "    Library:      ${OPENBLAS_LIB}")
endif()
message(STATUS "")
message(STATUS "Targets:")
message(STATUS "  main2dA:        ✓ Primary executable")
message(STATUS "")
message(STATUS "Build commands:")
message(STATUS "  Configure:      cmake --build . --config Debug")
message(STATUS "  Build:          cmake --build . --config Debug --target main2dA")
message(STATUS "  Run:            .\\Debug\\main2dA.exe")
message(STATUS "")
message(STATUS "Visual Studio Usage:")
message(STATUS "  1. Open the generated .sln file in Visual Studio")
message(STATUS "  2. main2dA is set as the startup project")
message(STATUS "  3. Press F5 to build and run directly")
message(STATUS "  4. Working directory is set to: ${CMAKE_CURRENT_SOURCE_DIR}")
message(STATUS "========================================")
message(STATUS "")



