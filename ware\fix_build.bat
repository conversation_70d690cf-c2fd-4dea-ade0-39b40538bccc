@echo off
echo 修复CMake构建问题...
cd /d "%~dp0"

echo 1. 关闭所有可能占用文件的进程...
taskkill /f /im ninja.exe 2>nul
taskkill /f /im cl.exe 2>nul
taskkill /f /im link.exe 2>nul
taskkill /f /im msbuild.exe 2>nul

echo 2. 清理构建目录...
if exist "out\build" (
    rmdir /s /q "out\build"
    echo 已删除构建目录
)

echo 3. 等待文件系统释放...
timeout /t 3 /nobreak >nul

echo 4. 重新创建构建目录...
mkdir "out\build\x64-Debug" 2>nul

echo 修复完成！请重新在Visual Studio中配置项目。
pause