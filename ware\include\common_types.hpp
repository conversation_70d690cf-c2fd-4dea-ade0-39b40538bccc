#pragma once

#include <vector>
#include <stdexcept>
#include <iostream>
#include <algorithm>
#include <cmath>
#include <limits>
#include <cassert>
#include <string>
#include <complex>  // 添加复数支持

// Use Eigen for high-performance linear algebra
#ifdef USE_EIGEN
#include <Eigen/Dense>
#include <Eigen/Sparse>
#include <Eigen/Eigenvalues>
#endif

// Debugging and compatibility options
#ifndef MATLAB_COMPATIBLE_BOUNDS
#define MATLAB_COMPATIBLE_BOUNDS 1  // 1 = MATLAB-like behavior, 0 = strict bounds checking
#endif

// OpenBLAS function declarations (avoiding header file includes)
#ifdef USE_OPENBLAS
extern "C" {
    void cblas_dgemm(const int Order, const int TransA, const int TransB,
                     const int M, const int N, const int K,
                     const double alpha, const double *A, const int lda,
                     const double *B, const int ldb,
                     const double beta, double *C, const int ldc);
    void cblas_dgemv(const int Order, const int TransA,
                     const int M, const int N,
                     const double alpha, const double *A, const int lda,
                     const double *X, const int incX,
                     const double beta, double *Y, const int incY);
    double cblas_ddot(const int N, const double *X, const int incX,
                      const double *Y, const int incY);
    double cblas_dnrm2(const int N, const double *X, const int incX);
}
#define CblasRowMajor 101
#define CblasColMajor 102
#define CblasNoTrans 111
#define CblasTrans 112
#define CblasConjTrans 113
#endif

constexpr double PI = 3.141592653589793;
constexpr double EPS = 2.220446049250313e-16;

// 基本类型定义
using Integer = int;
using Real = double;
using Complex = std::complex<Real>;  // 复数类型

// 前向声明
class Matrix;
class Vector;
class ComplexMatrix;   // 复数矩阵
class ComplexVector;   // 复数向量

#ifdef USE_EIGEN
using EigenMatrix = Eigen::MatrixXd;
using EigenVector = Eigen::VectorXd;
using EigenSparseMatrix = Eigen::SparseMatrix<double>;
#endif

// 前向声明一些常用函数
Matrix zeros(Integer rows, Integer cols);
Vector zeros(Integer size);
Matrix ones(Integer rows, Integer cols);
Vector ones(Integer size);
Matrix create_identity(Integer n); 
Matrix transpose(const Matrix& A);
Matrix inverse(const Matrix& A);
Matrix matmul(const Matrix& A, const Matrix& B);
Vector linspace(Real start, Real end, Integer num);
Vector matvec(const Matrix& A, const Vector& x);

// 复数矩阵的基本操作函数声明
// 注意：完整定义将在ComplexMatrix类声明之后实现
ComplexMatrix czeros(Integer rows, Integer cols);
ComplexVector czeros(Integer size);
ComplexMatrix cones(Integer rows, Integer cols);
ComplexVector cones(Integer size);
ComplexMatrix create_cidentity(Integer n);
ComplexMatrix ctranspose(const ComplexMatrix& A);      // 共轭转置
ComplexMatrix transpose(const ComplexMatrix& A);       // 非共轭转置
ComplexMatrix inverse(const ComplexMatrix& A);
ComplexMatrix matmul(const ComplexMatrix& A, const ComplexMatrix& B);
ComplexVector matvec(const ComplexMatrix& A, const ComplexVector& x);

// 实数矩阵和复数矩阵的转换函数声明
// 注意：完整定义将在ComplexMatrix类声明之后实现
ComplexMatrix toComplex(const Matrix& re, const Matrix& im);
Matrix real(const ComplexMatrix& A);
Matrix imag(const ComplexMatrix& A);
Real abs(const Complex& z);
Real arg(const Complex& z);

class Matrix {
public:
    Integer rows{0}, cols{0};
    std::vector<Real> data;

    // 存储顺序标志，true表示列优先（MATLAB兼容），false表示行优先
    static constexpr bool is_col_major = true;

    // 默认构造函数
    Matrix() = default;

    // 基本构造函数
    Matrix(Integer r, Integer c) : rows(r), cols(c), data(r * c, 0.0) {
        if (r < 0 || c < 0) {
            throw std::invalid_argument("Matrix dimensions must be non-negative");
        }
    }

    Matrix(Integer r, Integer c, Real val) : rows(r), cols(c), data(r * c, val) {
        if (r < 0 || c < 0) {
            throw std::invalid_argument("Matrix dimensions must be non-negative");
        }
    }

    Matrix(const std::vector<Real>& vec) : rows(static_cast<Integer>(vec.size())), cols(1), data(vec) {}

    // 拷贝构造函数（显式定义以确保正确性）
    Matrix(const Matrix& other) : rows(other.rows), cols(other.cols), data(other.data) {}

    // 移动构造函数（性能优化）
    Matrix(Matrix&& other) noexcept
        : rows(other.rows), cols(other.cols), data(std::move(other.data)) {
        other.rows = 0;
        other.cols = 0;
    }

    // 拷贝赋值运算符（异常安全）
    Matrix& operator=(const Matrix& other) {
        if (this != &other) {
            Matrix temp(other);  // 拷贝构造
            swap(temp);          // 无异常交换
        }
        return *this;
    }

    // 移动赋值运算符（性能优化）
    Matrix& operator=(Matrix&& other) noexcept {
        if (this != &other) {
            rows = other.rows;
            cols = other.cols;
            data = std::move(other.data);
            other.rows = 0;
            other.cols = 0;
        }
        return *this;
    }

    // 析构函数（使用默认，std::vector自动清理）
    ~Matrix() = default;

    // 异常安全的交换函数
    void swap(Matrix& other) noexcept {
        std::swap(rows, other.rows);
        std::swap(cols, other.cols);
        std::swap(data, other.data);
    }

    // 安全的元素访问（带边界检查）
    Real& at(Integer i, Integer j) {
        if (i < 0 || i >= rows || j < 0 || j >= cols) {
            throw std::out_of_range("Matrix index (" + std::to_string(i) +
                                  ", " + std::to_string(j) + ") out of bounds [" +
                                  std::to_string(rows) + "x" + std::to_string(cols) + "]");
        }
        return data.at(idx(i, j));
    }

    const Real& at(Integer i, Integer j) const {
        if (i < 0 || i >= rows || j < 0 || j >= cols) {
            throw std::out_of_range("Matrix index (" + std::to_string(i) +
                                  ", " + std::to_string(j) + ") out of bounds [" +
                                  std::to_string(rows) + "x" + std::to_string(cols) + "]");
        }
        return data.at(idx(i, j));
    }

private:
    // 索引计算方法 - 支持列优先存储
    inline Integer idx(Integer i, Integer j) const {
        if (is_col_major) {
            // 列优先索引: col*rows + row (MATLAB兼容)
            return j * rows + i;
        } else {
            // 行优先索引: row*cols + col
            return i * cols + j;
        }
    }

public:

    // MATLAB兼容的元素访问（性能优化版本）
    Real& operator()(Integer i, Integer j) {
#if MATLAB_COMPATIBLE_BOUNDS
        if (i < 0 || j < 0 || i >= rows || j >= cols) {
            // 返回NaN而不是零，更符合MATLAB行为
            static Real nan_value = std::numeric_limits<Real>::quiet_NaN();
            return nan_value;
        }
#else
        // 调试模式下使用安全检查
        return at(i, j);
#endif
        return data.at(idx(i, j));
    }

    const Real& operator()(Integer i, Integer j) const {
#if MATLAB_COMPATIBLE_BOUNDS
        if (i < 0 || j < 0 || i >= rows || j >= cols) {
            static Real nan_value = std::numeric_limits<Real>::quiet_NaN();
            return nan_value;
        }
#else
        return at(i, j);
#endif
        return data.at(idx(i, j));
    }

    // 安全获取（返回NaN而不是抛出异常）
    Real safe_get(Integer i, Integer j) const {
        if (i < 0 || i >= rows || j < 0 || j >= cols) {
            return std::numeric_limits<Real>::quiet_NaN();
        }
        return data.at(idx(i, j));
    }

    Real& operator[](Integer i) {
#if MATLAB_COMPATIBLE_BOUNDS
        if (i < 0 || i >= size()) {
            static Real zero_value = 0.0;
            return zero_value;
        }
#else
        if (i < 0 || i >= size()) {
            throw std::out_of_range("Vector index out of bounds");
        }
#endif
        return data.at(i);  // 使用安全的at()方法
    }

    const Real& operator[](Integer i) const {
#if MATLAB_COMPATIBLE_BOUNDS
        if (i < 0 || i >= size()) {
            static Real zero_value = 0.0;
            return zero_value;
        }
#else
        if (i < 0 || i >= size()) {
            throw std::out_of_range("Vector index out of bounds");
        }
#endif
        return data.at(i);  // 使用安全的at()方法
    }

    Integer size() const { return static_cast<Integer>(data.size()); }

    // 获取原始数据指针（用于BLAS等库）
    Real* data_ptr() { return data.data(); }
    const Real* data_ptr() const { return data.data(); }

    Matrix operator*(Real scalar) const {
        Matrix result(rows, cols);
        for (Integer r = 0; r < rows; ++r) {
            for (Integer c = 0; c < cols; ++c) {
                result(r, c) = (*this)(r, c) * scalar;
            }
        }
        return result;
    }

    Matrix hadamard(const Matrix& other) const {
        Matrix result(rows, cols);
        for (Integer r = 0; r < rows; ++r) {
            for (Integer c = 0; c < cols; ++c) {
                result(r, c) = (*this)(r, c) * other(r, c);
            }
        }
        return result;
    }

    Matrix transpose() const {
        Matrix result(cols, rows);
        for (Integer r = 0; r < rows; ++r) {
            for (Integer c = 0; c < cols; ++c) {
                result(c, r) = (*this)(r, c);
            }
        }
        return result;
    }

    Matrix inverse() const;
};

class Vector : public Matrix {
public:
    // 构造函数
    Vector() : Matrix() {}
    Vector(Integer size) : Matrix(size, 1) {
        if (size < 0) {
            throw std::invalid_argument("Vector size must be non-negative");
        }
    }
    Vector(Integer size, Real val) : Matrix(size, 1, val) {
        if (size < 0) {
            throw std::invalid_argument("Vector size must be non-negative");
        }
    }
    Vector(const std::vector<Real>& data) : Matrix(data) {}

    // 拷贝构造函数
    Vector(const Vector& other) : Matrix(other) {}

    // 移动构造函数
    Vector(Vector&& other) noexcept : Matrix(std::move(other)) {}

    // 从Matrix构造（确保是列向量）
    explicit Vector(const Matrix& mat) : Matrix(mat) {
        if (mat.cols != 1) {
            throw std::invalid_argument("Cannot convert non-column matrix to Vector");
        }
    }

    // 赋值运算符
    Vector& operator=(const Vector& other) {
        Matrix::operator=(other);
        return *this;
    }

    Vector& operator=(Vector&& other) noexcept {
        Matrix::operator=(std::move(other));
        return *this;
    }

    // 检查维度匹配并报告详细错误信息
    void check_dimensions(const Vector& other, const std::string& op) const {
        if (size() != other.size()) {
            std::string msg = "Vector dimension mismatch in " + op + ": " 
                + std::to_string(size()) + " vs " + std::to_string(other.size());
            throw std::invalid_argument(msg);
        }
    }

    // 向量元素访问 - 重载()操作符，MATLAB风格1D访问
    Real& operator()(Integer i) {
        // MATLAB兼容边界检查
#if MATLAB_COMPATIBLE_BOUNDS
        if (i < 0 || i >= size()) {
            static Real zero_value = 0.0;
            return zero_value;
        }
#else
        if (i < 0 || i >= size()) {
            throw std::out_of_range("Vector index out of bounds");
        }
#endif
        // 对于向量，不管是行优先还是列优先存储，索引都是一样的
        return data.at(i);  // 使用安全的at()方法
    }

    const Real& operator()(Integer i) const {
        // MATLAB兼容边界检查
#if MATLAB_COMPATIBLE_BOUNDS
        if (i < 0 || i >= size()) {
            static Real zero_value = 0.0;
            return zero_value;
        }
#else
        if (i < 0 || i >= size()) {
            throw std::out_of_range("Vector index out of bounds");
        }
#endif
        return data.at(i);  // 使用安全的at()方法
    }

    // 向量运算 - 加强错误处理
    Vector operator+(const Vector& other) const {
        check_dimensions(other, "addition");
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = (*this)[i] + other[i];
        }
        return result;
    }

    Vector operator-(const Vector& other) const {
        check_dimensions(other, "subtraction");
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = (*this)[i] - other[i];
        }
        return result;
    }

    // MATLAB风格的元素乘法 (.*)
    Vector element_mult(const Vector& other) const {
        check_dimensions(other, "element-wise multiplication");
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = (*this)[i] * other[i];
        }
        return result;
    }

    // 向量除以标量
    Vector operator/(Real scalar) const {
        if (std::abs(scalar) < EPS) {
            throw std::invalid_argument("Division by zero or near-zero scalar");
        }
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = (*this)[i] / scalar;
        }
        return result;
    }
    
    // 向量乘以标量
    Vector operator*(Real scalar) const {
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = (*this)[i] * scalar;
        }
        return result;
    }

    Real dot(const Vector& other) const {
        if(size() != other.size()) {
            throw std::invalid_argument("Vector dimensions mismatch in dot product");
        }
        
#ifdef USE_OPENBLAS
        // 使用BLAS库的ddot函数计算点积
        return cblas_ddot(
            size(),            // 向量长度
            data.data(),       // 第一个向量
            1,                 // 第一个向量的步长
            other.data.data(), // 第二个向量
            1                  // 第二个向量的步长
        );
#elif defined(USE_EIGEN)
        // 使用Eigen库计算点积
        Eigen::Map<const Eigen::VectorXd> eigenA(data.data(), size());
        Eigen::Map<const Eigen::VectorXd> eigenB(other.data.data(), other.size());
        return eigenA.dot(eigenB);
#else
        // 默认实现
        Real result = 0.0;
        for (Integer i = 0; i < size(); ++i) {
            result += (*this)[i] * other[i];
        }
        return result;
#endif
    }

    Real norm() const {
#ifdef USE_OPENBLAS
        // 使用BLAS库的dnrm2函数计算L2范数
        return cblas_dnrm2(
            size(),           // 向量长度
            data.data(),      // 向量数据
            1                 // 步长
        );
#elif defined(USE_EIGEN)
        // 使用Eigen库计算L2范数
        Eigen::Map<const Eigen::VectorXd> eigenA(data.data(), size());
        return eigenA.norm();
#else
        // 默认实现
        Real sum = 0.0;
        for (Integer i = 0; i < size(); ++i) {
            sum += (*this)[i] * (*this)[i];
        }
        return std::sqrt(sum);
#endif
    }

    Vector hadamard(const Vector& other) const {
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = (*this)[i] * other[i];
        }
        return result;
    }

    Vector hadamard_inverse() const {
        Vector result(size());
        for (Integer i = 0; i < size(); ++i) {
            result[i] = 1.0 / ((*this)[i] + EPS);
        }
        return result;
    }
};

inline Matrix zeros(Integer rows, Integer cols) { return Matrix(rows, cols, 0.0); }
inline Vector zeros(Integer size) { return Vector(size, 0.0); }
inline Matrix ones(Integer rows, Integer cols) { return Matrix(rows, cols, 1.0); }
inline Vector ones(Integer size) { return Vector(size, 1.0); }
inline Matrix transpose(const Matrix& A) { return A.transpose(); }
inline Matrix inverse(const Matrix& A) { return A.inverse(); }
inline Matrix matmul(const Matrix& A, const Matrix& B) {
    if(A.cols != B.rows) { return Matrix(); }
    Matrix C(A.rows, B.cols);
    
#ifdef USE_OPENBLAS
    // 使用OpenBLAS加速矩阵乘法计算 C = A * B
    // 注意：针对列优先存储调整参数
    cblas_dgemm(
        Matrix::is_col_major ? CblasColMajor : CblasRowMajor,  // 存储顺序
        CblasNoTrans,         // A不转置
        CblasNoTrans,         // B不转置
        A.rows,               // A的行数
        B.cols,               // B的列数
        A.cols,               // A的列数（等于B的行数）
        1.0,                  // 乘法系数alpha=1
        A.data.data(),        // A的数据
        Matrix::is_col_major ? A.rows : A.cols,  // A的leading dimension
        B.data.data(),        // B的数据
        Matrix::is_col_major ? B.rows : B.cols,  // B的leading dimension
        0.0,                  // beta=0，不累加到C
        C.data.data(),        // 结果矩阵C
        Matrix::is_col_major ? C.rows : C.cols   // C的leading dimension
    );
#elif defined(USE_EIGEN)
    // 使用Eigen库进行矩阵乘法计算
    // 注意：根据Matrix存储顺序选择Eigen存储顺序
    if (Matrix::is_col_major) {
        // 列优先存储
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::ColMajor>> 
            eigenA(A.data.data(), A.rows, A.cols);
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::ColMajor>> 
            eigenB(B.data.data(), B.rows, B.cols);
        Eigen::Map<Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::ColMajor>> 
            eigenC(C.data.data(), C.rows, C.cols);
        
        eigenC = eigenA * eigenB;
    } else {
        // 行优先存储
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>> 
            eigenA(A.data.data(), A.rows, A.cols);
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>> 
            eigenB(B.data.data(), B.rows, B.cols);
        Eigen::Map<Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>> 
            eigenC(C.data.data(), C.rows, C.cols);
        
        eigenC = eigenA * eigenB;
    }
#else
    // 默认实现：朴素矩阵乘法，考虑存储顺序
    if (Matrix::is_col_major) {
        // 列优先存储，需要调整内循环顺序以提高缓存命中率
        for (Integer j = 0; j < B.cols; ++j) {
            for (Integer i = 0; i < A.rows; ++i) {
                Real sum = 0.0;
                for (Integer k = 0; k < A.cols; ++k) {
                    sum += A(i, k) * B(k, j);
                }
                C(i, j) = sum;
            }
        }
    } else {
        // 行优先存储
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < B.cols; ++j) {
                Real sum = 0.0;
                for (Integer k = 0; k < A.cols; ++k) {
                    sum += A(i, k) * B(k, j);
                }
                C(i, j) = sum;
            }
        }
    }
#endif
    
    return C;
}
inline Matrix operator*(const Matrix& A, const Matrix& B) { return matmul(A,B); }
inline Vector linspace(Real start, Real end, Integer num) {
    Vector result(num);
    if (num == 1) {
        result[0] = start;
        return result;
    }
    Real step = (end - start) / (num - 1);
    for (Integer i = 0; i < num; ++i) {
        result[i] = start + i * step;
    }
    return result;
}
inline Vector matvec(const Matrix& A, const Vector& x) {
    if (A.cols != x.size()) {
        throw std::invalid_argument("Matrix-vector size mismatch");
    }
    Vector result(A.rows);
    
#ifdef USE_OPENBLAS
    // 使用OpenBLAS加速矩阵向量乘法 y = A * x
    cblas_dgemv(
        Matrix::is_col_major ? CblasColMajor : CblasRowMajor,  // 存储顺序
        CblasNoTrans,         // A不转置
        A.rows,               // A的行数
        A.cols,               // A的列数
        1.0,                  // 乘法系数alpha=1
        A.data.data(),        // A的数据
        Matrix::is_col_major ? A.rows : A.cols,  // A的leading dimension
        x.data.data(),        // x的数据
        1,                    // x的步长
        0.0,                  // beta=0，不累加到y
        result.data.data(),   // 结果向量y
        1                     // y的步长
    );
#elif defined(USE_EIGEN)
    // 使用Eigen库进行矩阵向量乘法，根据Matrix存储顺序选择映射方式
    if (Matrix::is_col_major) {
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::ColMajor>> 
            eigenA(A.data.data(), A.rows, A.cols);
        Eigen::Map<const Eigen::VectorXd> eigenX(x.data.data(), x.size());
        Eigen::Map<Eigen::VectorXd> eigenY(result.data.data(), result.size());
        
        eigenY = eigenA * eigenX;
    } else {
        Eigen::Map<const Eigen::Matrix<Real, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>> 
            eigenA(A.data.data(), A.rows, A.cols);
        Eigen::Map<const Eigen::VectorXd> eigenX(x.data.data(), x.size());
        Eigen::Map<Eigen::VectorXd> eigenY(result.data.data(), result.size());
        
        eigenY = eigenA * eigenX;
    }
#else
    // 默认实现：朴素矩阵向量乘法
    for (Integer r = 0; r < A.rows; ++r) {
        Real sum = 0.0;
        for (Integer c = 0; c < A.cols; ++c) {
            sum += A(r, c) * x[c];
        }
        result[r] = sum;
    }
#endif
    
    return result;
}
inline Matrix operator*(Real scalar, const Matrix& mat) { return mat * scalar; }
inline Vector operator*(Real scalar, const Vector& vec) { return vec * scalar; }
// Element-wise addition
inline Matrix operator+(const Matrix& A, const Matrix& B){
    if(A.rows!=B.rows || A.cols!=B.cols) return Matrix();
    Matrix C(A.rows,A.cols);
    for(Integer i=0;i<A.rows;++i)
        for(Integer j=0;j<A.cols;++j) C(i,j)=A(i,j)+B(i,j);
    return C;
} 

//声明
Matrix create_identity(Integer n); 

//定义
inline Matrix create_identity(Integer n) {
    Matrix result(n, n);
    for (Integer i = 0; i < n; ++i) {
        result(i, i) = 1.0;
    }
    return result;
} 