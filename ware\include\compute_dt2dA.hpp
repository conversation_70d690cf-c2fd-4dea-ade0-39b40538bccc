#pragma once
#include "mesh_sphere2dA.hpp"
#include <utility>

/**
 * @brief Calculate simulation time step and total number of steps, consistent with MATLAB version
 * @param OM Array of domain structures
 * @param duration Total simulation duration (s)
 * @return std::pair<double, int> Returns a pair of time step dt and total step count nt
 */
std::pair<double, int> compute_dt2dA(const std::vector<Domain2dA>& OM, double duration);