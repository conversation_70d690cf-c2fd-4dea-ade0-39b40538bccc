/**
 * @file debug_utils.hpp
 * @brief Optimized debugging utilities for precise problem location
 */

#pragma once

#include <iostream>
#include <string>
#include <chrono>
#include <iomanip>

// Debug levels
enum class DebugLevel {
    NONE = 0,      // No debug output
    ERROR = 1,     // Only errors
    WARNING = 2,   // Errors and warnings
    INFO = 3,      // Errors, warnings, and info
    VERBOSE = 4    // All debug output
};

// Global debug level (can be set at runtime)
extern DebugLevel g_debug_level;

// Timing utilities
class Timer {
private:
    std::chrono::high_resolution_clock::time_point start_time;
    std::string name;
    
public:
    Timer(const std::string& timer_name) : name(timer_name) {
        start_time = std::chrono::high_resolution_clock::now();
    }
    
    ~Timer() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        if (g_debug_level >= DebugLevel::INFO) {
            std::cout << "[TIMER] " << name << " took " << duration.count() << " ms" << std::endl;
        }
    }
};

// Scoped debug macros
#define DEBUG_ERROR(msg) \
    if (g_debug_level >= DebugLevel::ERROR) { \
        std::cout << "[ERROR] " << __FUNCTION__ << ":" << __LINE__ << " - " << msg << std::endl; \
    }

#define DEBUG_WARNING(msg) \
    if (g_debug_level >= DebugLevel::WARNING) { \
        std::cout << "[WARNING] " << __FUNCTION__ << ":" << __LINE__ << " - " << msg << std::endl; \
    }

#define DEBUG_INFO(msg) \
    if (g_debug_level >= DebugLevel::INFO) { \
        std::cout << "[INFO] " << __FUNCTION__ << " - " << msg << std::endl; \
    }

#define DEBUG_VERBOSE(msg) \
    if (g_debug_level >= DebugLevel::VERBOSE) { \
        std::cout << "[VERBOSE] " << __FUNCTION__ << ":" << __LINE__ << " - " << msg << std::endl; \
    }

// Function entry/exit tracking
#define DEBUG_FUNCTION_ENTRY() \
    if (g_debug_level >= DebugLevel::VERBOSE) { \
        std::cout << "[ENTRY] " << __FUNCTION__ << std::endl; \
    }

#define DEBUG_FUNCTION_EXIT() \
    if (g_debug_level >= DebugLevel::VERBOSE) { \
        std::cout << "[EXIT] " << __FUNCTION__ << std::endl; \
    }

// Matrix/Vector debugging
#define DEBUG_MATRIX_INFO(mat, name) \
    if (g_debug_level >= DebugLevel::INFO) { \
        std::cout << "[MATRIX] " << name << ": " << mat.rows << "x" << mat.cols << std::endl; \
    }

#define DEBUG_VECTOR_INFO(vec, name) \
    if (g_debug_level >= DebugLevel::INFO) { \
        std::cout << "[VECTOR] " << name << ": size=" << vec.size() << std::endl; \
    }

// Numerical stability checks
#define DEBUG_CHECK_FINITE(value, name) \
    if (!std::isfinite(value)) { \
        DEBUG_ERROR("Non-finite value detected: " << name << " = " << value); \
        throw std::runtime_error("Non-finite value: " + std::string(name)); \
    }

#define DEBUG_CHECK_BOUNDS(index, size, name) \
    if (index < 0 || index >= size) { \
        DEBUG_ERROR("Bounds check failed: " << name << "[" << index << "] with size " << size); \
        throw std::runtime_error("Bounds check failed: " + std::string(name)); \
    }

// Progress tracking for loops
class ProgressTracker {
private:
    size_t total;
    size_t current;
    std::string name;
    size_t last_percent;
    
public:
    ProgressTracker(size_t total_items, const std::string& task_name) 
        : total(total_items), current(0), name(task_name), last_percent(0) {
        if (g_debug_level >= DebugLevel::INFO) {
            std::cout << "[PROGRESS] Starting " << name << " (0/" << total << ")" << std::endl;
        }
    }
    
    void update() {
        current++;
        if (g_debug_level >= DebugLevel::INFO) {
            size_t percent = (current * 100) / total;
            if (percent != last_percent && percent % 10 == 0) {
                std::cout << "[PROGRESS] " << name << " " << percent << "% (" 
                         << current << "/" << total << ")" << std::endl;
                last_percent = percent;
            }
        }
    }
    
    ~ProgressTracker() {
        if (g_debug_level >= DebugLevel::INFO) {
            std::cout << "[PROGRESS] Completed " << name << " (" << current << "/" << total << ")" << std::endl;
        }
    }
};

// Critical section debugging (for identifying bottlenecks)
#define DEBUG_CRITICAL_SECTION(name) \
    Timer _timer(name); \
    DEBUG_INFO("Entering critical section: " << name);

// Memory usage tracking
void debug_memory_usage(const std::string& location);

// Condition number tracking
void debug_condition_number(double cond_num, const std::string& matrix_name);

// Set debug level at runtime
void set_debug_level(DebugLevel level);
void set_debug_level_from_string(const std::string& level_str);

// Initialize debugging system
void init_debug_system();

// Cleanup debugging system
void cleanup_debug_system();
