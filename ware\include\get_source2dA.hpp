#pragma once
#include "mesh_sphere2dA.hpp"
#include "common_types.hpp"
#include "create_source2d.hpp"
#include <vector>

/**
 * @brief Get source information, corresponding to MATLAB version get_source2dA.m
 * 
 * This function generates source structures and configures source parameters
 * 
 * @param OM Array of domain structures
 * @param freq Source frequency (Hz)
 * @param dt Time step
 * @param nt Total number of time steps
 * @return std::vector<SourceStruct> Array of source structures
 */
std::vector<SourceStruct> get_source2dA(const std::vector<Domain2dA>& OM,
                                        double freq,
                                        double dt,
                                        int nt);