#pragma once
#include "mesh_sphere2dA.hpp"
#include "sparse_matrix.hpp" // 包含CSR结构体定义
#include <vector>

/**
 * @brief Initialize the state of all domains, consistent with MATLAB version
 * @param OM Array of domain structures
 * @param nt Total number of time steps, used for pre-allocating receiver arrays, etc.
 * @return Updated array of domain structures
 */
std::vector<Domain2dA> initialize_domain2dA(std::vector<Domain2dA>& OM, int nt);