/**
 * @file memory_safe_matrix.hpp
 * @brief 内存安全的矩阵操作工具类
 * 
 * 提供RAII、异常安全和性能优化的矩阵操作
 */

#pragma once

#include "common_types.hpp"
#include <memory>
#include <vector>
#include <functional>
#include <unordered_map>
#include <mutex>
#include <random>

namespace MemorySafe {

/**
 * @brief 智能矩阵指针类型定义
 */
using MatrixPtr = std::unique_ptr<Matrix>;
using VectorPtr = std::unique_ptr<Vector>;
using SharedMatrixPtr = std::shared_ptr<Matrix>;
using SharedVectorPtr = std::shared_ptr<Vector>;

/**
 * @brief 矩阵内存池 - 减少频繁的内存分配/释放
 */
class MatrixPool {
private:
    std::vector<MatrixPtr> available_matrices;
    std::vector<VectorPtr> available_vectors;
    mutable std::mutex pool_mutex;  // 线程安全
    
    // 统计信息
    size_t total_allocated = 0;
    size_t total_reused = 0;
    
public:
    /**
     * @brief 获取指定大小的矩阵
     */
    MatrixPtr acquire_matrix(Integer rows, Integer cols) {
        std::lock_guard<std::mutex> lock(pool_mutex);
        
        // 尝试重用现有矩阵
        for (auto it = available_matrices.begin(); it != available_matrices.end(); ++it) {
            if ((*it)->rows >= rows && (*it)->cols >= cols) {
                auto matrix = std::move(*it);
                available_matrices.erase(it);
                
                // 调整大小（不重新分配内存）
                matrix->rows = rows;
                matrix->cols = cols;
                matrix->data.resize(rows * cols);
                std::fill(matrix->data.begin(), matrix->data.end(), 0.0);
                
                ++total_reused;
                return matrix;
            }
        }
        
        // 创建新矩阵
        ++total_allocated;
        return std::make_unique<Matrix>(rows, cols);
    }
    
    /**
     * @brief 获取指定大小的向量
     */
    VectorPtr acquire_vector(Integer size) {
        std::lock_guard<std::mutex> lock(pool_mutex);
        
        // 尝试重用现有向量
        for (auto it = available_vectors.begin(); it != available_vectors.end(); ++it) {
            if ((*it)->size() >= size) {
                auto vector = std::move(*it);
                available_vectors.erase(it);
                
                // 调整大小
                vector->rows = size;
                vector->cols = 1;
                vector->data.resize(size);
                std::fill(vector->data.begin(), vector->data.end(), 0.0);
                
                ++total_reused;
                return vector;
            }
        }
        
        // 创建新向量
        ++total_allocated;
        return std::make_unique<Vector>(size);
    }
    
    /**
     * @brief 释放矩阵回池中
     */
    void release_matrix(MatrixPtr matrix) {
        if (matrix) {
            std::lock_guard<std::mutex> lock(pool_mutex);
            available_matrices.push_back(std::move(matrix));
        }
    }
    
    /**
     * @brief 释放向量回池中
     */
    void release_vector(VectorPtr vector) {
        if (vector) {
            std::lock_guard<std::mutex> lock(pool_mutex);
            available_vectors.push_back(std::move(vector));
        }
    }
    
    /**
     * @brief 清空池
     */
    void clear() {
        std::lock_guard<std::mutex> lock(pool_mutex);
        available_matrices.clear();
        available_vectors.clear();
    }
    
    /**
     * @brief 获取统计信息
     */
    std::pair<size_t, size_t> get_stats() const {
        std::lock_guard<std::mutex> lock(pool_mutex);
        return {total_allocated, total_reused};
    }
};

/**
 * @brief 全局矩阵池实例
 */
extern MatrixPool g_matrix_pool;

/**
 * @brief RAII矩阵管理器 - 自动管理矩阵生命周期
 */
class MatrixManager {
private:
    std::vector<MatrixPtr> managed_matrices;
    std::vector<VectorPtr> managed_vectors;

public:
    // 默认构造函数
    MatrixManager() = default;
    /**
     * @brief 创建并管理一个矩阵
     */
    Matrix* create_matrix(Integer rows, Integer cols) {
        auto matrix = g_matrix_pool.acquire_matrix(rows, cols);
        Matrix* ptr = matrix.get();
        managed_matrices.push_back(std::move(matrix));
        return ptr;
    }
    
    /**
     * @brief 创建并管理一个向量
     */
    Vector* create_vector(Integer size) {
        auto vector = g_matrix_pool.acquire_vector(size);
        Vector* ptr = vector.get();
        managed_vectors.push_back(std::move(vector));
        return ptr;
    }
    
    /**
     * @brief 创建并管理一个矩阵（带初始值）
     */
    Matrix* create_matrix(Integer rows, Integer cols, Real value) {
        Matrix* matrix = create_matrix(rows, cols);
        std::fill(matrix->data.begin(), matrix->data.end(), value);
        return matrix;
    }
    
    /**
     * @brief 创建并管理一个向量（带初始值）
     */
    Vector* create_vector(Integer size, Real value) {
        Vector* vector = create_vector(size);
        std::fill(vector->data.begin(), vector->data.end(), value);
        return vector;
    }
    
    /**
     * @brief 析构时自动释放所有管理的矩阵
     */
    ~MatrixManager() {
        // 释放所有矩阵回池中
        for (auto& matrix : managed_matrices) {
            g_matrix_pool.release_matrix(std::move(matrix));
        }
        for (auto& vector : managed_vectors) {
            g_matrix_pool.release_vector(std::move(vector));
        }
    }
    
    // 禁止拷贝和移动（确保RAII语义）
    MatrixManager(const MatrixManager&) = delete;
    MatrixManager& operator=(const MatrixManager&) = delete;
    MatrixManager(MatrixManager&&) = delete;
    MatrixManager& operator=(MatrixManager&&) = delete;
};

/**
 * @brief 异常安全的矩阵操作函数
 */
namespace SafeOps {

/**
 * @brief 异常安全的矩阵乘法
 */
inline Matrix safe_matmul(const Matrix& A, const Matrix& B) {
    if (A.cols != B.rows) {
        throw std::invalid_argument("Matrix dimensions incompatible for multiplication: " +
                                  std::to_string(A.rows) + "x" + std::to_string(A.cols) +
                                  " * " + std::to_string(B.rows) + "x" + std::to_string(B.cols));
    }
    
    try {
        MatrixManager manager;
        Matrix* result = manager.create_matrix(A.rows, B.cols);
        
        // 执行矩阵乘法
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < B.cols; ++j) {
                Real sum = 0.0;
                for (Integer k = 0; k < A.cols; ++k) {
                    sum += A.safe_get(i, k) * B.safe_get(k, j);
                }
                result->at(i, j) = sum;
            }
        }
        
        return *result;  // 拷贝返回，manager析构时自动清理
    } catch (...) {
        // 异常时自动清理，重新抛出
        throw;
    }
}

/**
 * @brief 异常安全的矩阵加法
 */
inline Matrix safe_matadd(const Matrix& A, const Matrix& B) {
    if (A.rows != B.rows || A.cols != B.cols) {
        throw std::invalid_argument("Matrix dimensions incompatible for addition: " +
                                  std::to_string(A.rows) + "x" + std::to_string(A.cols) +
                                  " + " + std::to_string(B.rows) + "x" + std::to_string(B.cols));
    }
    
    try {
        MatrixManager manager;
        Matrix* result = manager.create_matrix(A.rows, A.cols);
        
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < A.cols; ++j) {
                result->at(i, j) = A.safe_get(i, j) + B.safe_get(i, j);
            }
        }
        
        return *result;
    } catch (...) {
        throw;
    }
}

/**
 * @brief 异常安全的矩阵转置
 */
inline Matrix safe_transpose(const Matrix& A) {
    try {
        MatrixManager manager;
        Matrix* result = manager.create_matrix(A.cols, A.rows);
        
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < A.cols; ++j) {
                result->at(j, i) = A.safe_get(i, j);
            }
        }
        
        return *result;
    } catch (...) {
        throw;
    }
}

} // namespace SafeOps

/**
 * @brief 内存使用统计工具
 */
class MemoryProfiler {
private:
    static size_t peak_memory_usage;
    static size_t current_memory_usage;
    
public:
    static void record_allocation(size_t bytes) {
        current_memory_usage += bytes;
        if (current_memory_usage > peak_memory_usage) {
            peak_memory_usage = current_memory_usage;
        }
    }
    
    static void record_deallocation(size_t bytes) {
        current_memory_usage -= bytes;
    }
    
    static size_t get_current_usage() { return current_memory_usage; }
    static size_t get_peak_usage() { return peak_memory_usage; }
    static void reset_stats() { peak_memory_usage = current_memory_usage = 0; }
};

} // namespace MemorySafe
