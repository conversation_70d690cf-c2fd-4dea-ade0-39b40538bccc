/**
 * @file plot_functions.hpp
 * @brief 绘图函数的C++替代实现 - 数据输出版本
 * 
 * 由于C++没有内置的绘图功能，这些函数将数据保存到文件中，
 * 可以用Python、MATLAB或其他工具进行后续绘图。
 */

#pragma once

#include "common_types.hpp"
#include "mesh_sphere2dA.hpp"
#include <vector>
#include <string>

/**
 * @brief 保存域网格数据用于绘图 - 对应MATLAB plot_domain2d函数
 * 
 * 这个函数将域的网格坐标、材料属性等数据保存到CSV文件中，
 * 可以用外部工具进行可视化。
 * 
 * MATLAB原始功能：
 * - 绘制每个域的边界
 * - 显示Vp速度场的颜色图
 * - 设置坐标轴和颜色条
 * 
 * C++替代方案：
 * - 保存域边界坐标到文件
 * - 保存Vp速度场数据到文件
 * - 保存绘图配置信息
 * 
 * @param OM 域数组
 * @param output_dir 输出目录路径
 */
void save_domain_plot_data(const std::vector<Domain2dA>& OM, const std::string& output_dir = "output");

/**
 * @brief 保存波场数据用于绘图 - 对应MATLAB plot_wavefields2dA函数
 * 
 * 这个函数将波场数据保存到文件中，可以用外部工具制作动画。
 * 
 * MATLAB原始功能：
 * - 绘制每个域的网格边界
 * - 显示位移场的颜色图
 * - 实时更新显示
 * 
 * C++替代方案：
 * - 保存网格坐标到文件
 * - 保存位移场数据到文件
 * - 保存时间步信息
 * 
 * @param OM 域数组
 * @param it 时间步索引
 * @param output_dir 输出目录路径
 */
void save_wavefield_plot_data(const std::vector<Domain2dA>& OM, Integer it, const std::string& output_dir = "output");

/**
 * @brief 创建Python绘图脚本
 * 
 * 生成Python脚本来绘制保存的数据，提供与MATLAB类似的可视化效果。
 * 
 * @param output_dir 输出目录路径
 */
void create_python_plot_scripts(const std::string& output_dir = "output");

/**
 * @brief 简化的域信息输出函数
 * 
 * 输出域的基本信息到控制台，用于快速检查网格生成结果。
 * 
 * @param OM 域数组
 */
void print_domain_summary(const std::vector<Domain2dA>& OM);
