#pragma once
#include "mesh_sphere2dA.hpp"
#include "receiver.hpp"
#include <vector>

/**
 * @brief 主求解器函数，实现波场时间积分
 * 
 * 该函数执行完整的时间积分循环，按照MATLAB solver2dA.m的流程：
 * 1. 更新波场 (update_wavefields2dA)
 * 2. 计算内边界变量 (compute_boundary_Uvalue_inn2dA)
 * 3. 计算外边界变量 (compute_boundary_Uvalue_out2dA)
 * 4. 从位移计算应变 (compute_KU2dA)
 * 5. 计算内边界应力 (compute_boundary_Svalue_inn2dA)
 * 6. 计算外边界应力 (compute_boundary_Svalue_out2dA)
 * 7. 从应变计算加速度 (compute_KS2dA)
 * 8. 注入源项 (add_source2dA)
 * 9. 保存波形 (save_waveforms2dA)
 * 10. 定期保存波场 (save_wavefields2dA)
 * 
 * @param OM 域数组
 * @param source 源项数组
 * @param rece 接收器数组
 * @param dt 时间步长
 * @param nt 总时间步数
 * @return std::pair<std::vector<Domain2dA>, std::vector<Receiver2dA>> 更新后的域和接收器
 */
std::pair<std::vector<Domain2dA>, std::vector<Receiver2dA>>
solver2dA(std::vector<Domain2dA>& OM,
          const std::vector<SourceStruct>& source,
          std::vector<Receiver2dA>& rece,
          double dt,
          int nt);