#pragma once
#include "common_types.hpp"
#include <vector>

struct Source2dA {
    int iom{0};           // domain index where source is applied
    Matrix invMsg12;          // influence matrix for U12 component
    Matrix invMsg21;          // influence matrix for U21 component
    Vector Ft;                // time series (force)
};

// Simple Ricker wavelet generator
inline Vector ricker_wavelet(int nt, double dt, double fmax){
    Vector Ft(nt);
    double t0 = 1.0/fmax;
    for(int i=0;i<nt;++i){
        double t = i*dt - t0;
        double pi2 = M_PI*M_PI;
        Ft[i] = (1.0 - 2.0*pi2*fmax*fmax*t*t)* std::exp(-pi2*fmax*fmax*t*t);
    }
    return Ft;
}

inline Source2dA create_source2d(int iom,const Matrix &invMsg12,const Matrix &invMsg21,
                                 double fmax, double dt, int nt){
    Source2dA s; s.iom=iom; s.invMsg12=invMsg12; s.invMsg21=invMsg21; s.Ft=ricker_wavelet(nt,dt,fmax); return s; }