{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "DFDM2DA", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "main2dA::@6890427a1f51a3e7e1df", "jsonFile": "target-main2dA-Debug-777482a168e6d9fee69c.json", "name": "main2dA", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/project/ware/out/build/x64-Debug", "source": "D:/project/ware"}, "version": {"major": 2, "minor": 8}}