{"artifacts": [{"path": "main2dA_d.exe"}, {"path": "main2dA_d.pdb"}], "backtrace": 2, "backtraceGraph": {"commands": ["_add_executable", "add_executable", "target_link_libraries", "add_compile_options", "target_compile_options", "target_compile_definitions", "target_include_directories"], "files": ["C:/Users/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 127, "parent": 0}, {"command": 0, "file": 0, "line": 600, "parent": 1}, {"command": 2, "file": 1, "line": 159, "parent": 0}, {"command": 3, "file": 1, "line": 26, "parent": 0}, {"command": 4, "file": 1, "line": 172, "parent": 0}, {"command": 4, "file": 1, "line": 203, "parent": 0}, {"command": 5, "file": 1, "line": 188, "parent": 0}, {"command": 5, "file": 1, "line": 150, "parent": 0}, {"command": 5, "file": 1, "line": 157, "parent": 0}, {"command": 6, "file": 1, "line": 144, "parent": 0}, {"command": 6, "file": 1, "line": 151, "parent": 0}, {"command": 6, "file": 1, "line": 158, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 4, "fragment": "/utf-8"}, {"backtrace": 5, "fragment": "/wd4819"}, {"backtrace": 5, "fragment": "/wd4996"}, {"backtrace": 5, "fragment": "/wd4267"}, {"backtrace": 5, "fragment": "/wd4244"}, {"backtrace": 5, "fragment": "/wd4305"}, {"backtrace": 5, "fragment": "/wd4018"}, {"backtrace": 5, "fragment": "/W1"}, {"backtrace": 5, "fragment": "/bigobj"}, {"backtrace": 5, "fragment": "/EHsc"}, {"backtrace": 5, "fragment": "/permissive-"}, {"backtrace": 5, "fragment": "/Zc:__cplusplus"}, {"backtrace": 6, "fragment": "/Od"}, {"backtrace": 6, "fragment": "/Zi"}, {"backtrace": 6, "fragment": "/RTC1"}], "defines": [{"backtrace": 7, "define": "NOMINMAX"}, {"backtrace": 8, "define": "USE_EIGEN"}, {"backtrace": 9, "define": "USE_OPENBLAS"}, {"backtrace": 7, "define": "WIN32_LEAN_AND_MEAN"}, {"backtrace": 7, "define": "_CRT_SECURE_NO_WARNINGS"}, {"backtrace": 7, "define": "_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS"}, {"backtrace": 7, "define": "_USE_MATH_DEFINES"}], "includes": [{"backtrace": 10, "path": "D:/project/ware/include"}, {"backtrace": 11, "path": "D:/Eigen/eigen-3.4/eigen-3.4.0/eigen-3.4.0"}, {"backtrace": 12, "path": "D:/OpenBLAS-0.3.30-x64/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47]}], "id": "main2dA::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 3, "fragment": "D:\\OpenBLAS-0.3.30-x64\\lib\\libopenblas.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "main2dA", "nameOnDisk": "main2dA_d.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47]}], "sources": [{"backtrace": 2, "compileGroupIndex": 0, "path": "src/Get_Knot_Vector.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/Get_Knot_Vector_shape.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/add_source2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/assemble_global_matrices_sparse.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/bspln.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/check_stability2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/complex_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_KS2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_KU2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Svalue_inn2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Svalue_out2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Uvalue_inn2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_boundary_Uvalue_out2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/compute_dt2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/config.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/create_domain2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/create_source2d.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/dbspln.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/debug_utils.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/dist2d.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/eigen_wrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/error_handling.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/find_connections2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/gen_DFDMatrices2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/get_receiver2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/get_source2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/global_assembly.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/hat_pts.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/initialize_domain2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/lgwt.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/main2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mass_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/material_model.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/memory_safe_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/mesh_sphere2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/pagemtimes.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/plot_domain2d.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/plot_functions.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/refine_model2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/rotate_full_stress.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/save_wavefields2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/save_waveforms2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/setup_basis.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/solver2dA.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/spmv_omp.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/stiffness_matrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/tensorProduct2D.cpp", "sourceGroupIndex": 0}, {"backtrace": 2, "compileGroupIndex": 0, "path": "src/update_wavefields2dA.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}