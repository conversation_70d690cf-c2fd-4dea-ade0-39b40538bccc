# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: DFDM2DA
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:\project\ware\out\build\x64-Debug\
# =============================================================================
# Object build statements for EXECUTABLE target main2dA


#############################################
# Order-only phony target for main2dA

build cmake_object_order_depends_target_main2dA: phony || .

build CMakeFiles\main2dA.dir\src\Get_Knot_Vector.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\Get_Knot_Vector.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\Get_Knot_Vector_shape.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\Get_Knot_Vector_shape.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\add_source2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\add_source2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\assemble_global_matrices_sparse.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\assemble_global_matrices_sparse.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\bspln.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\bspln.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\check_stability2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\check_stability2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\complex_matrix.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\complex_matrix.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_KS2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_KS2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_KU2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_KU2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_boundary_Svalue_inn2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_boundary_Svalue_inn2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_boundary_Svalue_out2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_boundary_Svalue_out2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_boundary_Uvalue_inn2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_boundary_Uvalue_inn2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_boundary_Uvalue_out2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_boundary_Uvalue_out2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\compute_dt2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\compute_dt2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\config.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\config.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\create_domain2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\create_domain2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\create_source2d.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\create_source2d.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\dbspln.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\dbspln.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\debug_utils.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\debug_utils.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\dist2d.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\dist2d.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\eigen_wrapper.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\eigen_wrapper.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\error_handling.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\error_handling.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\find_connections2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\find_connections2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\gen_DFDMatrices2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\gen_DFDMatrices2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\get_receiver2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\get_receiver2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\get_source2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\get_source2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\global_assembly.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\global_assembly.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\hat_pts.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\hat_pts.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\initialize_domain2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\initialize_domain2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\lgwt.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\lgwt.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\main2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\main2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\mass_matrix.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\mass_matrix.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\material_model.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\material_model.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\memory_safe_matrix.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\memory_safe_matrix.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\mesh_sphere2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\mesh_sphere2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\pagemtimes.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\pagemtimes.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\plot_domain2d.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\plot_domain2d.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\plot_functions.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\plot_functions.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\refine_model2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\refine_model2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\rotate_full_stress.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\rotate_full_stress.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\save_wavefields2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\save_wavefields2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\save_waveforms2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\save_waveforms2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\setup_basis.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\setup_basis.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\solver2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\solver2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\spmv_omp.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\spmv_omp.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\stiffness_matrix.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\stiffness_matrix.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\tensorProduct2D.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\tensorProduct2D.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb

build CMakeFiles\main2dA.dir\src\update_wavefields2dA.cpp.obj: CXX_COMPILER__main2dA_unscanned_Debug D$:\project\ware\src\update_wavefields2dA.cpp || cmake_object_order_depends_target_main2dA
  DEFINES = -DNOMINMAX -DUSE_EIGEN -DUSE_OPENBLAS -DWIN32_LEAN_AND_MEAN -D_CRT_SECURE_NO_WARNINGS -D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS -D_USE_MATH_DEFINES
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd /utf-8 /wd4819 /wd4996 /wd4267 /wd4244 /wd4305 /wd4018 /W1 /bigobj /EHsc /permissive- /Zc:__cplusplus /Od /Zi /RTC1
  INCLUDES = -ID:\project\ware\include -ID:\Eigen\eigen-3.4\eigen-3.4.0\eigen-3.4.0 -ID:\OpenBLAS-0.3.30-x64\include
  OBJECT_DIR = CMakeFiles\main2dA.dir
  OBJECT_FILE_DIR = CMakeFiles\main2dA.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_PDB = main2dA_d.pdb


# =============================================================================
# Link build statements for EXECUTABLE target main2dA


#############################################
# Link the executable main2dA_d.exe

build main2dA_d.exe: CXX_EXECUTABLE_LINKER__main2dA_Debug CMakeFiles\main2dA.dir\src\Get_Knot_Vector.cpp.obj CMakeFiles\main2dA.dir\src\Get_Knot_Vector_shape.cpp.obj CMakeFiles\main2dA.dir\src\add_source2dA.cpp.obj CMakeFiles\main2dA.dir\src\assemble_global_matrices_sparse.cpp.obj CMakeFiles\main2dA.dir\src\bspln.cpp.obj CMakeFiles\main2dA.dir\src\check_stability2dA.cpp.obj CMakeFiles\main2dA.dir\src\complex_matrix.cpp.obj CMakeFiles\main2dA.dir\src\compute_KS2dA.cpp.obj CMakeFiles\main2dA.dir\src\compute_KU2dA.cpp.obj CMakeFiles\main2dA.dir\src\compute_boundary_Svalue_inn2dA.cpp.obj CMakeFiles\main2dA.dir\src\compute_boundary_Svalue_out2dA.cpp.obj CMakeFiles\main2dA.dir\src\compute_boundary_Uvalue_inn2dA.cpp.obj CMakeFiles\main2dA.dir\src\compute_boundary_Uvalue_out2dA.cpp.obj CMakeFiles\main2dA.dir\src\compute_dt2dA.cpp.obj CMakeFiles\main2dA.dir\src\config.cpp.obj CMakeFiles\main2dA.dir\src\create_domain2dA.cpp.obj CMakeFiles\main2dA.dir\src\create_source2d.cpp.obj CMakeFiles\main2dA.dir\src\dbspln.cpp.obj CMakeFiles\main2dA.dir\src\debug_utils.cpp.obj CMakeFiles\main2dA.dir\src\dist2d.cpp.obj CMakeFiles\main2dA.dir\src\eigen_wrapper.cpp.obj CMakeFiles\main2dA.dir\src\error_handling.cpp.obj CMakeFiles\main2dA.dir\src\find_connections2dA.cpp.obj CMakeFiles\main2dA.dir\src\gen_DFDMatrices2dA.cpp.obj CMakeFiles\main2dA.dir\src\get_receiver2dA.cpp.obj CMakeFiles\main2dA.dir\src\get_source2dA.cpp.obj CMakeFiles\main2dA.dir\src\global_assembly.cpp.obj CMakeFiles\main2dA.dir\src\hat_pts.cpp.obj CMakeFiles\main2dA.dir\src\initialize_domain2dA.cpp.obj CMakeFiles\main2dA.dir\src\lgwt.cpp.obj CMakeFiles\main2dA.dir\src\main2dA.cpp.obj CMakeFiles\main2dA.dir\src\mass_matrix.cpp.obj CMakeFiles\main2dA.dir\src\material_model.cpp.obj CMakeFiles\main2dA.dir\src\memory_safe_matrix.cpp.obj CMakeFiles\main2dA.dir\src\mesh_sphere2dA.cpp.obj CMakeFiles\main2dA.dir\src\pagemtimes.cpp.obj CMakeFiles\main2dA.dir\src\plot_domain2d.cpp.obj CMakeFiles\main2dA.dir\src\plot_functions.cpp.obj CMakeFiles\main2dA.dir\src\refine_model2dA.cpp.obj CMakeFiles\main2dA.dir\src\rotate_full_stress.cpp.obj CMakeFiles\main2dA.dir\src\save_wavefields2dA.cpp.obj CMakeFiles\main2dA.dir\src\save_waveforms2dA.cpp.obj CMakeFiles\main2dA.dir\src\setup_basis.cpp.obj CMakeFiles\main2dA.dir\src\solver2dA.cpp.obj CMakeFiles\main2dA.dir\src\spmv_omp.cpp.obj CMakeFiles\main2dA.dir\src\stiffness_matrix.cpp.obj CMakeFiles\main2dA.dir\src\tensorProduct2D.cpp.obj CMakeFiles\main2dA.dir\src\update_wavefields2dA.cpp.obj | D$:\OpenBLAS-0.3.30-x64\lib\libopenblas.lib
  FLAGS = /DWIN32 /D_WINDOWS /GR /EHsc /bigobj /Zi /Ob0 /Od /RTC1 -MDd
  LINK_FLAGS = /machine:x64 /debug /INCREMENTAL /subsystem:console
  LINK_LIBRARIES = D:\OpenBLAS-0.3.30-x64\lib\libopenblas.lib  kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib
  OBJECT_DIR = CMakeFiles\main2dA.dir
  POST_BUILD = C:\windows\system32\cmd.exe /C "cd /D D:\project\ware\out\build\x64-Debug && C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/Users/<USER>/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/project/ware/out/build/x64-Debug/main2dA_d.exe -installedDir C:/Users/<USER>/vcpkg/installed/x64-windows/debug/bin -OutVariable out"
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\main2dA.dir\
  TARGET_FILE = main2dA_d.exe
  TARGET_IMPLIB = main2dA_d.lib
  TARGET_PDB = main2dA_d.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\windows\system32\cmd.exe /C "cd /D D:\project\ware\out\build\x64-Debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\windows\system32\cmd.exe /C "cd /D D:\project\ware\out\build\x64-Debug && "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" --regenerate-during-build -SD:\project\ware -BD:\project\ware\out\build\x64-Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build main2dA: phony main2dA_d.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/project/ware/out/build/x64-Debug

build all: phony main2dA_d.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake C$:\Users\15019\vcpkg\scripts\buildsystems\vcpkg.cmake CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake D$:\project\ware\CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDependentOption.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX-CXXImportStd.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake C$:\Program$ Files\Microsoft$ Visual$ Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake C$:\Users\15019\vcpkg\scripts\buildsystems\vcpkg.cmake CMakeCache.txt CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake D$:\project\ware\CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
