#!/usr/bin/env python3
"""
Domain plotting script - equivalent to MATLAB plot_domain2d function
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def plot_domains(data_dir='output'):
    # Read data
    boundaries = pd.read_csv(f'{data_dir}/domain_boundaries.csv')
    vp_field = pd.read_csv(f'{data_dir}/vp_field.csv')

    # Read configuration
    config = {}
    with open(f'{data_dir}/plot_config.txt', 'r') as f:
        for line in f:
            if ':' in line and not line.startswith('#'):
                key, value = line.strip().split(':', 1)
                config[key.strip()] = value.strip()

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Plot 1: Domain boundaries
    for domain_id in boundaries['domain_id'].unique():
        domain_bounds = boundaries[boundaries['domain_id'] == domain_id]
        for boundary_type in ['left', 'right', 'bottom', 'top']:
            bound_data = domain_bounds[domain_bounds['boundary_type'] == boundary_type]
            ax1.plot(bound_data['x_km'], bound_data['z_km'], '--k', linewidth=1)

    ax1.set_xlabel('km')
    ax1.set_ylabel('km')
    ax1.set_title('Domain Boundaries')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)

    # Plot 2: Vp field
    for domain_id in vp_field['domain_id'].unique():
        domain_data = vp_field[vp_field['domain_id'] == domain_id]
        scatter = ax2.scatter(domain_data['x_km'], domain_data['z_km'],
                            c=domain_data['vp_ms'], cmap='hot', s=1)

    plt.colorbar(scatter, ax=ax2, label='Vp (m/s)')
    ax2.set_xlabel('km')
    ax2.set_ylabel('km')
    ax2.set_title('Vp (m/s)')
    ax2.axis('equal')

    plt.tight_layout()
    plt.savefig(f'{data_dir}/domain_plot.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == '__main__':
    plot_domains()
