#!/usr/bin/env python3
"""
Wavefield plotting script - equivalent to MATLAB plot_wavefields2dA function
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import glob
import os

def plot_wavefield(timestep, data_dir='output'):
    """Plot wavefield for a specific timestep"""
    padded_timestep = f"{timestep:06d}"

    # Read data
    boundary_file = f'{data_dir}/wavefield_boundaries_{padded_timestep}.csv'
    field_file = f'{data_dir}/wavefield_{padded_timestep}.csv'
    info_file = f'{data_dir}/timestep_{padded_timestep}_info.txt'

    if not os.path.exists(field_file):
        print(f"No data found for timestep {timestep}")
        return

    boundaries = pd.read_csv(boundary_file)
    field_data = pd.read_csv(field_file)

    # Read info
    info = {}
    with open(info_file, 'r') as f:
        for line in f:
            if ':' in line and not line.startswith('#'):
                key, value = line.strip().split(':', 1)
                info[key.strip()] = value.strip()

    fig, ax = plt.subplots(figsize=(10, 8))

    # Plot domain boundaries
    for domain_id in boundaries['domain_id'].unique():
        domain_bounds = boundaries[boundaries['domain_id'] == domain_id]
        for boundary_type in ['left', 'right', 'bottom', 'top']:
            bound_data = domain_bounds[domain_bounds['boundary_type'] == boundary_type]
            ax.plot(bound_data['x_km'], bound_data['z_km'], '--k', linewidth=1)

    # Plot displacement field
    scatter = ax.scatter(field_data['x_km'], field_data['z_km'],
                        c=field_data['displacement'], cmap='jet', s=1,
                        vmin=-8e-8, vmax=8e-8)

    plt.colorbar(scatter, ax=ax, label='Displacement')
    ax.set_xlabel('km')
    ax.set_ylabel('km')
    ax.set_title(f'Wavefield - Timestep {timestep}')
    ax.axis('equal')

    max_disp = float(info.get('max_displacement', '0'))
    ax.text(0.02, 0.98, f'Max displacement: {max_disp:.2e}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{data_dir}/wavefield_{padded_timestep}.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_animation(data_dir='output', output_file='wavefield_animation.gif'):
    """Create animation from all timesteps"""
    import matplotlib.animation as animation

    # Find all wavefield files
    field_files = glob.glob(f'{data_dir}/wavefield_??????.csv')
    timesteps = sorted([int(f.split('_')[-1].split('.')[0]) for f in field_files])

    if not timesteps:
        print("No wavefield data found")
        return

    fig, ax = plt.subplots(figsize=(10, 8))

    def animate(frame):
        ax.clear()
        timestep = timesteps[frame]
        padded_timestep = f"{timestep:06d}"

        # Read data
        boundaries = pd.read_csv(f'{data_dir}/wavefield_boundaries_{padded_timestep}.csv')
        field_data = pd.read_csv(f'{data_dir}/wavefield_{padded_timestep}.csv')

        # Plot boundaries
        for domain_id in boundaries['domain_id'].unique():
            domain_bounds = boundaries[boundaries['domain_id'] == domain_id]
            for boundary_type in ['left', 'right', 'bottom', 'top']:
                bound_data = domain_bounds[domain_bounds['boundary_type'] == boundary_type]
                ax.plot(bound_data['x_km'], bound_data['z_km'], '--k', linewidth=1)

        # Plot field
        scatter = ax.scatter(field_data['x_km'], field_data['z_km'],
                            c=field_data['displacement'], cmap='jet', s=1,
                            vmin=-8e-8, vmax=8e-8)

        ax.set_xlabel('km')
        ax.set_ylabel('km')
        ax.set_title(f'Wavefield - Timestep {timestep}')
        ax.axis('equal')

        return scatter,

    anim = animation.FuncAnimation(fig, animate, frames=len(timesteps),
                                  interval=100, blit=False, repeat=True)

    anim.save(f'{data_dir}/{output_file}', writer='pillow', fps=10)
    print(f"Animation saved as {data_dir}/{output_file}")

if __name__ == '__main__':
    import sys
    if len(sys.argv) > 1:
        timestep = int(sys.argv[1])
        plot_wavefield(timestep)
    else:
        print("Usage: python plot_wavefields.py <timestep>")
        print("Or call create_animation() to make an animation")
