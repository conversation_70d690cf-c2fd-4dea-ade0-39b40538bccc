#include "../include/Get_Knot_Vector.hpp"
#include <limits>

/**
 * @file Get_Knot_Vector.cpp
 * @brief Implementation translating MATLAB Get_Knot_Vector.m
 */

Vector Get_Knot_Vector(int n, int k) {
    const double eps_val = 2.220446049250313e-16; // MATLAB eps

    Vector t(n + k);
    for(int i = 0; i < n + k; ++i) {
        t.at(i, 0) = 0.0;
    }

    double dx = 1.0 / (n + 1 - k);

    for(int i = k; i <= n + 1; ++i) {  // MATLAB: k:n+1, 转换为C++ 0-based
        t.at(i - 1, 0) = (i - k) * dx;
    }

    double left_val  = t.at(k - 1, 0) - 20.0 * eps_val;
    double right_val = t.at(n, 0)     + 20.0 * eps_val;

    for(int i = 0; i < k; ++i) {
        t.at(i, 0) = left_val;
    }
    for(int i = n; i < n + k; ++i) {
        t.at(i, 0) = right_val;
    }

    return t;
} 