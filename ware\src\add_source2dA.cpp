#include "../include/add_source2dA.hpp"
#include "../include/common_types.hpp"
#include <cmath>

std::vector<Domain2dA> add_source2dA(std::vector<Domain2dA>& OM,
                                    const std::vector<SourceStruct>& source,
                                    int it)
{
    // 处理每个源
    for (const auto& src : source) {
        // 检查源的域索引是否有效
        if (src.iom < 0 || src.iom >= static_cast<int>(OM.size())) {
            continue; // 无效的域索引，跳过
        }

        // 获取源索引和对应域
        int som = src.iom;

        // 在MATLAB中，源的时间函数是一个向量Ft，我们需要获取当前时间步的值
        // 在C++中，我们使用signal数组存储时间函数
        if (it < static_cast<int>(src.signal.size())) {
            double Fit = src.signal[it];

            // 添加源到加速度场，与MATLAB版本一致
            const Matrix& invMsg12 = src.invMsg12;
            const Matrix& invMsg21 = src.invMsg21;

            // 直接添加源贡献，确保矩阵尺寸匹配
            if (invMsg12.rows == OM[som].state.dU2dtt12.rows && 
                invMsg12.cols == OM[som].state.dU2dtt12.cols) {
                for (int i = 0; i < invMsg12.rows; ++i) {
                    for (int j = 0; j < invMsg12.cols; ++j) {
                        OM[som].state.dU2dtt12(i, j) += Fit * invMsg12(i, j);
                    }
                }
            }

            if (invMsg21.rows == OM[som].state.dU2dtt21.rows &&
                invMsg21.cols == OM[som].state.dU2dtt21.cols) {
                for (int i = 0; i < invMsg21.rows; ++i) {
                    for (int j = 0; j < invMsg21.cols; ++j) {
                        OM[som].state.dU2dtt21(i, j) += Fit * invMsg21(i, j);
                    }
                }
            }
        }
    }

    // 对每个域除以Jacobian和密度，与MATLAB版本完全一致
    for (int iom = 0; iom < static_cast<int>(OM.size()); ++iom) {
        auto& dom = OM[iom];
        const Matrix& Jac12 = dom.Jac12;
        const Matrix& Jac21 = dom.Jac21;
        const Matrix& rho12 = dom.rho12;
        const Matrix& rho21 = dom.rho21;

        // 确保尺寸一致，如果不一致则跳过缩放
        if (Jac12.rows == dom.state.dU2dtt12.rows && 
            Jac12.cols == dom.state.dU2dtt12.cols) {
            for (int i = 0; i < Jac12.rows; ++i) {
                for (int j = 0; j < Jac12.cols; ++j) {
                    dom.state.dU2dtt12(i, j) /= (Jac12(i, j) * rho12(i, j) + EPS);
                }
            }
        }

        if (Jac21.rows == dom.state.dU2dtt21.rows &&
            Jac21.cols == dom.state.dU2dtt21.cols) {
            for (int i = 0; i < Jac21.rows; ++i) {
                for (int j = 0; j < Jac21.cols; ++j) {
                    dom.state.dU2dtt21(i, j) /= (Jac21(i, j) * rho21(i, j) + EPS);
                }
            }
        }
    }

    return OM;
} 