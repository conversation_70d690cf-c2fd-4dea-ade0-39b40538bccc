#include "bspln.hpp"
#include <cmath>
#include <cstdint>
#include <limits>
#include <iostream>

// 🔧 修复：使用与 MATLAB eps 一致的阈值
static constexpr Real safe_eps = 2.2204e-16;  // MATLAB eps

/**
 * @file bspln.cpp
 * @brief Implementation of B-spline basis function evaluation
 *
 * This file corresponds exactly to MATLAB function bspln.m
 * Direct translation maintaining MATLAB's 1-based indexing logic
 */

// B-spline function implementation based on successful DFDM_2D_1.0-unstructured
// This is the corrected version that matches the working implementation
double bspln(const Vector& t, int n, int i, int k, double x) {
    // Use exact EPS value from DFDM_2D_1.0-unstructured for MATLAB compatibility
    const double EPS = 1e-14;
    const double safe_eps = 1e-15;

    // Convert to 0-based indexing for internal calculations
    // MATLAB uses 1-based, but we work with 0-based internally
    if (i < 1 || i > n || k < 1) {
        return 0.0;
    }

    // Convert to 0-based for array access
    Integer i0 = i - 1;  // Convert to 0-based

    // Bounds checking for array access
    // We need to access t(i0 + k) in the recursive case, so check i0 + k < t.size()
    if (i0 + k >= static_cast<Integer>(t.size())) {
        return 0.0;
    }

    // Additional safety check for base case (k=1)
    if (k == 1 && i0 + 1 >= static_cast<Integer>(t.size())) {
        return 0.0;
    }

    // Implementation based on successful DFDM_2D_1.0-unstructured version
    Real b = 0;
    Real c1 = 0, c2 = 0;

    if (k == 1) {
        // 🔧 修复：与 MATLAB 完全一致的基础情况
        if (i0 + 1 >= t.size()) {
            b = 0.0;
        } else {
            // 对应 MATLAB: if x>t(i) && ~(x>t(i+1))
            if (x > t.at(i0, 0) && !(x > t.at(i0 + 1, 0))) {
                b = 1.0;
            } else {
                b = 0.0;
            }
        }
    } else {
        // Recursive case: k > 1 - 使用与一维代码相同的逻辑
        // 添加边界检查
        if (i0 + k >= t.size()) {
            b = 0.0;
        } else {
            // 🔧 修复：与 MATLAB 完全一致的系数计算
            // 对应 MATLAB: if abs(t(i+k-1) - t(i)) > eps
            if (std::abs(t.at(i0 + k - 1, 0) - t.at(i0, 0)) > safe_eps) {
                c1 = (x - t.at(i0, 0)) / (t.at(i0 + k - 1, 0) - t.at(i0, 0));
            } else {
                c1 = 0;
            }

            // 对应 MATLAB: if t(i+k) - t(i+1) > eps
            if (t.at(i0 + k, 0) - t.at(i0 + 1, 0) > safe_eps) {
                c2 = (t.at(i0 + k, 0) - x) / (t.at(i0 + k, 0) - t.at(i0 + 1, 0));
            } else {
                c2 = 0;
            }

            // 🔧 修复：与 MATLAB 完全一致的递归调用
            // 对应 MATLAB: b=c1*bspln(t,n,i,k-1,x)+c2*bspln(t,n,i+1,k-1,x);
            b = c1 * bspln(t, n, i, k - 1, x) + c2 * bspln(t, n, i + 1, k - 1, x);
        }
    }

    return b;
}