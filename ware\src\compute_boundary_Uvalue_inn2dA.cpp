#include "../include/compute_boundary_Uvalue_inn2dA.hpp"
#include "../include/pagemtimes.hpp"
#include "../include/common_types.hpp"

static inline Matrix matmul_local(const Matrix &A,const Matrix &B){ return pagemtimes(A,B);} 
static inline Matrix transpose_local(const Matrix &A){ return transpose(A);} 

static void boundary_from_U(const Matrix &invLxT, const Matrix &invLzT, const Matrix &U,
                            Vector &Umo, Vector &Upo, Vector &Uom, Vector &Uop)
{
    Matrix tmp = matmul_local(invLxT, U);
    // first and last row -> mo/po
    int cols = tmp.cols;
    Umo = Vector(cols);
    Upo = Vector(cols);
    for(int j=0;j<cols;++j){ Umo[j]=tmp(0,j); Upo[j]=tmp(tmp.rows-1,j);} // first row 0

    // operate in z direction
    Matrix tmpT = transpose_local(U);
    tmpT = matmul_local(invLzT, tmpT);
    tmp = transpose_local(tmpT);
    int rows = tmp.rows;
    Uom = Vector(rows);
    Uop = Vector(rows);
    for(int i=0;i<rows;++i){ Uom[i]=tmp(i,0); Uop[i]=tmp(i,tmp.cols-1);}
}

std::vector<Domain2dA> compute_boundary_Uvalue_inn2dA(std::vector<Domain2dA>& OM)
{
    for(int iom=0;iom<static_cast<int>(OM.size()); ++iom){
        Domain2dA &dom = OM[iom];
        Vector U21mo,U21po,U21om,U21op;
        Vector U12mo,U12po,U12om,U12op;

        boundary_from_U(dom.invLxT22, dom.invLzT11, dom.state.U21,
                        U21mo,U21po,U21om,U21op);
        boundary_from_U(dom.invLxT11, dom.invLzT22, dom.state.U12,
                        U12mo,U12po,U12om,U12op);

        dom.state.U12mo_inn = U12mo;
        dom.state.U12po_inn = U12po;
        dom.state.U12om_inn = U12om;
        dom.state.U12op_inn = U12op;

        dom.state.U21mo_inn = U21mo;
        dom.state.U21po_inn = U21po;
        dom.state.U21om_inn = U21om;
        dom.state.U21op_inn = U21op;
    }
    return OM;
} 