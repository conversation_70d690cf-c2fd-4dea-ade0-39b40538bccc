#include "../include/compute_dt2dA.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

std::pair<double, int> compute_dt2dA(const std::vector<Domain2dA>& OM, double duration) {
    // 初始化dt为很大的值
    double dt = 9999999.0;
    // 设置CFL条件
    const double CFL_condition = 0.1;
    
    for (size_t iom = 0; iom < OM.size(); ++iom) {
        const Domain2dA& dom = OM[iom];
        
        int Nx1 = dom.Nx1;
        int Nz1 = dom.Nz1;

        // 计算中心点的索引
        int ix = static_cast<int>(std::ceil((Nx1 + 1.0) / 2.0)) - 1; // -1转为0-based
        int iz = static_cast<int>(std::ceil((Nz1 + 1.0) / 2.0)) - 1; // -1转为0-based
        
        // 确保索引在有效范围内
        ix = std::min(ix, Nx1 - 2);
        iz = std::min(iz, Nz1 - 2);
        ix = std::max(ix, 0);
        iz = std::max(iz, 0);
        
        // 获取坐标点
        double x1 = dom.x2d11(ix, iz);
        double z1 = dom.z2d11(ix, iz);

        double x2 = dom.x2d11(ix + 1, iz + 1);
        double z2 = dom.z2d11(ix + 1, iz + 1);

        // 计算两点之间的距离
        double dxz = std::sqrt((x2 - x1) * (x2 - x1) + (z2 - z1) * (z2 - z1));

        // 最大波速
        double Vmax = 5000.0;

        // 根据CFL条件计算临时dt
        double tmp = CFL_condition * dxz / Vmax;
        
        // 更新dt为所有域中的最小值
        dt = std::min(dt, tmp);
    }
    
    // 与MATLAB版本一致，强制设置dt=0.1
    dt = 0.1;
    
    // 计算总时间步数
    int nt = static_cast<int>(std::ceil(duration / dt));
    
    std::cout << "计算的时间步长dt = " << dt << ", 总时间步数nt = " << nt << std::endl;
    
    return std::make_pair(dt, nt);
} 