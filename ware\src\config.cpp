#include "../include/config.hpp"
#include <iostream>
#include <cstdlib> // getenv

// Simplified config implementation without toml dependency
static SimulationConfig load_cfg(){
    SimulationConfig cfg; // defaults already set

    // For now, just use default values
    // TODO: Add proper config file parsing when toml11 is available
    std::cout << "[config] using default values: ppw=" << cfg.ppw << ", frequency=" << cfg.frequency << "\n";

    return cfg;
}

const SimulationConfig &get_simulation_config(){
    static SimulationConfig cfg = load_cfg();
    return cfg;
} 