/**
 * @file create_domain2dA.cpp
 * @brief 严格按照MATLAB create_domain2dA.m实现的域创建函数
 *
 * 这个实现完全匹配MATLAB版本，使用空值初始化而不是硬编码默认值
 */

#include "../include/create_domain2dA.hpp"

Domain2dA create_domain2dA() {
    Domain2dA domain;

    // 严格按照MATLAB版本：所有字段初始化为空值
    // MATLAB: 'nx', [], 'nz', [], 'xa', [], 'za', [], 'xp', [], 'zp', [], 'rho',[], 'mu', []
    domain.model2dA.nx = 0;
    domain.model2dA.nz = 0;
    // xa, za, xp, zp, rho, mu 矩阵保持默认构造（空矩阵）

    // 基函数结构初始化为空值
    // MATLAB: 'nb', [], 'pb', [], 'xmin', [], 'xmax', [], 'x', [], 'k', [], 'mat', []
    domain.Bx1.nb = 0;
    domain.Bx1.pb = 0;
    domain.Bx1.xmin = 0.0;
    domain.Bx1.xmax = 0.0;
    // x, k, mat 向量/矩阵保持默认构造（空）

    domain.Bx2 = domain.Bx1;  // 复制相同的空初始化
    domain.Bz1 = domain.Bx1;
    domain.Bz2 = domain.Bx1;

    // 状态结构所有字段初始化为空值
    // MATLAB版本中所有状态字段都是 []
    // dU2dtt12, dU2dtt21, U12_0, U12_1, U12, U21_0, U21_1, U21 等
    // 这些矩阵字段保持默认构造（空矩阵）

    // B样条参数初始化为空值
    // MATLAB: 'px1', [], 'pz1', [], 'Nx1', [], 'Nz1', []
    domain.px1 = 0;
    domain.pz1 = 0;
    domain.Nx1 = 0;
    domain.Nz1 = 0;

    // 所有矩阵字段保持默认构造（空矩阵）
    // x2d11, z2d11, bxT1, bxT2, bzT1, bzT2
    // kkx12, kkz12, kkx21, kkz21
    // invLx11, invLz11, invLx22, invLz22
    // invLxT11, invLzT11, invLxT22, invLzT22

    // 边界耦合参数初始化为空值
    // MATLAB: 'alpha_mo', [], 'alpha_po', [], 'alpha_om', [], 'alpha_op', []
    domain.alpha_mo = 0.0;
    domain.alpha_po = 0.0;
    domain.alpha_om = 0.0;
    domain.alpha_op = 0.0;

    // 邻居域索引初始化为空值
    // MATLAB: 'iNbr_mo', [], 'iNbr_po', [], 'iNbr_om', [], 'iNbr_op', []
    domain.iNbr_mo = -1;  // 使用-1表示无邻居
    domain.iNbr_po = -1;
    domain.iNbr_om = -1;
    domain.iNbr_op = -1;

    // 面连接类型初始化为空值
    // MATLAB: 'iFace_mo', [], 'iFace_po', [], 'iFace_om', [], 'iFace_op', []
    domain.iFace_mo = 0;
    domain.iFace_po = 0;
    domain.iFace_om = 0;
    domain.iFace_op = 0;

    // 旋转矩阵初始化为空值
    // MATLAB: 'rot_mo', [], 'rot_po', [], 'rot_om', [], 'rot_op', []
    // 这些矩阵字段保持默认构造（空矩阵）

    // 坐标变换矩阵初始化为空值
    // MATLAB: 'dxpdx11', [], 'dzpdx11', [], 'dxpdz11', [], 'dzpdz11', []
    // 以及所有其他坐标变换矩阵
    // 这些矩阵字段保持默认构造（空矩阵）

    // Jacobian矩阵初始化为空值
    // MATLAB: 'Jac11', [], 'Jac22', [], 'Jac12', [], 'Jac21', []
    // 这些矩阵字段保持默认构造（空矩阵）

    // 材料属性矩阵初始化为空值
    // MATLAB: 'rho12', [], 'rho21', [], 'mu11', [], 'mu22', []
    // 这些矩阵字段保持默认构造（空矩阵）

    return domain;
}