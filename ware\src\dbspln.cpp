#include "../include/dbspln.hpp"
#include "../include/bspln.hpp"
#include <limits>

// 🔧 修复：与 MATLAB dbspln 完全一致的实现
double dbspln(const Vector& t, int n, int i, int k, double x, int m) {
    // 使用与 MATLAB 一致的 eps
    const double matlab_eps = 2.2204e-16;

    if(i < 1 || i > n || k < 1 || m < 0) return 0.0;
    int idx = i - 1;
    if(idx + k >= static_cast<int>(t.size())) return 0.0;

    if(m == 0) {
        return bspln(t, n, i, k, x);
    }
    if(m > 0 && m < k) {
        double c1 = 0.0, c2 = 0.0;

        // 🔧 修复：与 MATLAB 完全一致的条件检查和计算
        // 对应 MATLAB: if abs(t(i+k-1) - t(i)) > eps
        if(std::abs(t.at(idx + k - 1, 0) - t.at(idx, 0)) > matlab_eps) {
            // 对应 MATLAB: c1 = (k-1)/(t(i+k-1)-t(i));
            c1 = static_cast<double>(k - 1) / (t.at(idx + k - 1, 0) - t.at(idx, 0));
        }

        // 对应 MATLAB: if abs(t(i+k) - t(i+1)) > eps
        if(std::abs(t.at(idx + k, 0) - t.at(idx + 1, 0)) > matlab_eps) {
            // 对应 MATLAB: c2 = (k-1)/(t(i+k)-t(i+1));
            c2 = static_cast<double>(k - 1) / (t.at(idx + k, 0) - t.at(idx + 1, 0));
        }

        // 对应 MATLAB: b = c1*dbspln(t,n,i,k-1,x,m-1) - c2*dbspln(t,n,i+1,k-1,x,m-1);
        return c1 * dbspln(t, n, i, k - 1, x, m - 1) - c2 * dbspln(t, n, i + 1, k - 1, x, m - 1);
    }
    return 0.0;
}