/**
 * @file debug_utils.cpp
 * @brief Implementation of optimized debugging utilities
 */

#include "../include/debug_utils.hpp"
#include <fstream>
#include <cstdlib>

// Global debug level
DebugLevel g_debug_level = DebugLevel::INFO;

void set_debug_level(DebugLevel level) {
    g_debug_level = level;
}

void set_debug_level_from_string(const std::string& level_str) {
    if (level_str == "NONE" || level_str == "0") {
        g_debug_level = DebugLevel::NONE;
    } else if (level_str == "ERROR" || level_str == "1") {
        g_debug_level = DebugLevel::ERROR;
    } else if (level_str == "WARNING" || level_str == "2") {
        g_debug_level = DebugLevel::WARNING;
    } else if (level_str == "INFO" || level_str == "3") {
        g_debug_level = DebugLevel::INFO;
    } else if (level_str == "VERBOSE" || level_str == "4") {
        g_debug_level = DebugLevel::VERBOSE;
    } else {
        std::cout << "[WARNING] Unknown debug level: " << level_str << ", using INFO" << std::endl;
        g_debug_level = DebugLevel::INFO;
    }
}

void init_debug_system() {
    // Check for environment variable
    const char* debug_env = std::getenv("DFDM_DEBUG_LEVEL");
    if (debug_env) {
        set_debug_level_from_string(std::string(debug_env));
    }
    
    if (g_debug_level >= DebugLevel::INFO) {
        std::cout << "[DEBUG] Debug system initialized with level: ";
        switch (g_debug_level) {
            case DebugLevel::NONE: std::cout << "NONE"; break;
            case DebugLevel::ERROR: std::cout << "ERROR"; break;
            case DebugLevel::WARNING: std::cout << "WARNING"; break;
            case DebugLevel::INFO: std::cout << "INFO"; break;
            case DebugLevel::VERBOSE: std::cout << "VERBOSE"; break;
        }
        std::cout << std::endl;
    }
}

void cleanup_debug_system() {
    if (g_debug_level >= DebugLevel::INFO) {
        std::cout << "[DEBUG] Debug system cleanup complete" << std::endl;
    }
}

void debug_memory_usage(const std::string& location) {
    if (g_debug_level >= DebugLevel::INFO) {
        // Simple memory usage tracking (platform-specific implementations can be added)
        std::cout << "[MEMORY] Checkpoint at " << location << std::endl;
    }
}

void debug_condition_number(double cond_num, const std::string& matrix_name) {
    if (cond_num > 1e12) {
        if (cond_num > 1e20) {
            DEBUG_ERROR("Extremely ill-conditioned matrix " << matrix_name << ": cond=" << std::scientific << cond_num);
        } else {
            DEBUG_WARNING("Ill-conditioned matrix " << matrix_name << ": cond=" << std::scientific << cond_num);
        }
    } else if (g_debug_level >= DebugLevel::VERBOSE) {
        DEBUG_VERBOSE("Matrix " << matrix_name << " condition number: " << std::scientific << cond_num);
    }
}
