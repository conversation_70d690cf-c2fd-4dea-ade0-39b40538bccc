#include "../include/eigen_wrapper.hpp"
#include "../include/common_types.hpp"
#include "../include/error_handling.hpp"
#include <stdexcept>
#include <cmath>
#include <string>
#include <iostream>
#include <algorithm>
#include <limits>
#include <vector>

#ifdef USE_EIGEN
#include <Eigen/Dense>
#include <Eigen/LU>
#include <Eigen/SVD>
#include <Eigen/Cholesky>
#include <Eigen/Eigenvalues>
#endif

#ifdef USE_OPENBLAS
// 暂时禁用OpenBLAS以避免编译错误
// extern "C" {
// #include <cblas.h>
// }
#endif

namespace EigenWrapper {

// MATLAB-compatible constants - 🔧 修复：更接近 MATLAB 的宽容行为
static constexpr Real EPS = 2.2204e-16;  // MATLAB's eps
static constexpr Real DEFAULT_TOL = 1e-12;  // 🔧 更宽松的容忍度，接近 MATLAB
static constexpr Real COND_THRESHOLD = 1e40; // 🔧 大幅提高阈值，处理 10^36+ 量级
static constexpr Real EXTREME_COND_THRESHOLD = 1e50; // 🔧 极端阈值，几乎不触发
static constexpr Real MATLAB_RCOND_THRESHOLD = 1e-15;  // 🔧 更接近 MATLAB 的 eps

/**
 * @brief Check if matrix is square
 */
inline void check_square(const Matrix& A) {
    if (A.rows != A.cols) {
        MatlabError::error("check_square", "Matrix must be square",
                       MatlabError::ErrorType::DimensionMismatch);
    }
}

// Error handling function
static void error_handler(const std::string& func_name, const std::string& message) {
    std::cerr << "Error in " << func_name << ": " << message << std::endl;
    throw std::runtime_error(func_name + ": " + message);
}

// MATLAB-style warning handler
static void warning_handler(const std::string& func_name, const std::string& message) {
    std::cout << "Warning: " << func_name << " - " << message << std::endl;
}

/**
 * @brief Matrix transpose (EigenWrapper namespace version)
 */
Matrix transpose(const Matrix& A) {
    return A.transpose();  // Use Matrix's built-in transpose method
}

/**
 * @brief MATLAB-style matrix inversion with high tolerance for ill-conditioned matrices
 */
Matrix inv(const Matrix& A) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        // 确保所有变量在正确的作用域内
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // MATLAB-style multi-level fault-tolerant matrix inversion
        // Level 1: Try standard LU decomposition with enhanced tolerance
        Eigen::FullPivLU<Eigen::MatrixXd> lu(E);
        Real det_threshold = EPS * std::pow(10, n);  // 更宽松的行列式阈值
        if (std::abs(lu.determinant()) > det_threshold) {
            try {
                Eigen::MatrixXd Einv = lu.inverse();
                Matrix B(n, n);
                for (Integer i = 0; i < n; ++i) {
                    for (Integer j = 0; j < n; ++j) {
                        B(i, j) = Einv(i, j);
                    }
                }
                return B;
            } catch (...) {
                // Continue to next level
            }
        }

        // Level 2: Check condition number and use regularization if needed
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E, Eigen::ComputeFullU | Eigen::ComputeFullV);
        Eigen::VectorXd sigma = svd.singularValues();

        if (sigma.size() == 0 || sigma(sigma.size()-1) == 0) {
            // Matrix is singular, use pseudo-inverse
            warning_handler("inv", "Matrix is singular. Using pseudo-inverse.");
            return pinv(A, DEFAULT_TOL);
        }

        Real cond_num = sigma(0) / sigma(sigma.size()-1);

        if (cond_num > EXTREME_COND_THRESHOLD) {
            // 🔧 修复：极端病态矩阵使用更宽松的容忍度，接近 MATLAB 行为
            warning_handler("inv", "Matrix is extremely ill-conditioned (cond=" + std::to_string(cond_num) +
                          "). Using high-tolerance pseudo-inverse.");
            return pinv(A, 1e-10);  // 🔧 更宽松的容忍度，接近 MATLAB
        }
        else if (cond_num > COND_THRESHOLD) {
            warning_handler("inv", "Matrix is ill-conditioned (cond=" + std::to_string(cond_num) +
                          "). Using regularized inverse with adaptive tolerance.");

            // 自适应正则化参数：根据条件数调整
            Real adaptive_reg = sigma(0) * std::max(EPS * n, 1e-12 * std::sqrt(cond_num));
            Eigen::MatrixXd E_reg = E + adaptive_reg * Eigen::MatrixXd::Identity(n, n);

            try {
                Eigen::FullPivLU<Eigen::MatrixXd> lu_reg(E_reg);
                Eigen::MatrixXd Einv = lu_reg.inverse();
                Matrix B(n, n);
                for (Integer i = 0; i < n; ++i) {
                    for (Integer j = 0; j < n; ++j) {
                        B(i, j) = Einv(i, j);
                    }
                }
                return B;
            } catch (...) {
                // Continue to pseudo-inverse
            }
        }

        // Level 3: Use SVD pseudo-inverse (MATLAB final fallback)
        warning_handler("inv", "Using SVD pseudo-inverse for robust inversion.");
        return pinv(A, DEFAULT_TOL);

#else
        // Fallback implementation using Gauss-Jordan elimination with pivoting
        Matrix B(n, n);
        Matrix C = A;
        
        // Initialize as identity matrix
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                B(i, j) = (i == j) ? 1.0 : 0.0;
            }
        }
        
        // Gauss-Jordan elimination with partial pivoting
        for (Integer i = 0; i < n; ++i) {
            // Find pivot
            Integer pivot_row = i;
            for (Integer k = i + 1; k < n; ++k) {
                if (std::abs(C(k, i)) > std::abs(C(pivot_row, i))) {
                    pivot_row = k;
                }
            }
            
            // Swap rows if needed
            if (pivot_row != i) {
                for (Integer j = 0; j < n; ++j) {
                    std::swap(C(i, j), C(pivot_row, j));
                    std::swap(B(i, j), B(pivot_row, j));
                }
            }
            
            Real pivot = C(i, i);
            if (std::abs(pivot) < DEFAULT_TOL) {
                error_handler("inv", "Matrix is singular or ill-conditioned");
                return Matrix();
            }
            
            // Normalize current row
            for (Integer j = 0; j < n; ++j) {
                C(i, j) /= pivot;
                B(i, j) /= pivot;
            }
            
            // Eliminate other rows
            for (Integer k = 0; k < n; ++k) {
                if (k != i) {
                    Real factor = C(k, i);
                    for (Integer j = 0; j < n; ++j) {
                        C(k, j) -= factor * C(i, j);
                        B(k, j) -= factor * B(i, j);
                    }
                }
            }
        }
        return B;
#endif
    } catch (const std::exception& e) {
        error_handler("inv", std::string("Matrix inversion failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief Symmetric positive definite matrix inversion with MATLAB-style tolerance
 */
Matrix invSPD(const Matrix& A, Real eps) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // Try Cholesky decomposition with MATLAB-style tolerance
        Eigen::LLT<Eigen::MatrixXd> llt(E);
        if (llt.info() == Eigen::Success) {
            Eigen::MatrixXd X = llt.solve(Eigen::MatrixXd::Identity(n, n));
            Matrix B(n, n);
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = 0; j < n; ++j) {
                    B(i, j) = X(i, j);
                }
            }
            return B;
        } else {
            // Add diagonal regularization (MATLAB approach for near-singular SPD matrices)
            Real reg_eps = (eps > 0) ? eps : EPS * n;
            E.diagonal().array() += reg_eps;
            
            llt.compute(E);
            if (llt.info() == Eigen::Success) {
                Eigen::MatrixXd X = llt.solve(Eigen::MatrixXd::Identity(n, n));
                Matrix B(n, n);
                for (Integer i = 0; i < n; ++i) {
                    for (Integer j = 0; j < n; ++j) {
                        B(i, j) = X(i, j);
                    }
                }
                warning_handler("invSPD", "Added diagonal regularization for near-singular matrix");
                return B;
            } else {
                // Fallback to general inversion
                warning_handler("invSPD", "Cholesky failed, using general matrix inversion");
                return inv(A);
            }
        }
#else
        // Fallback to general inversion
        return inv(A);
#endif
    }
    catch (const std::exception& e) {
        error_handler("invSPD", std::string("SPD matrix inversion failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief Matrix-vector multiplication (EigenWrapper namespace version)
 */
Vector matvec(const Matrix& A, const Vector& x) {
    if (A.cols != x.size()) {
        throw std::runtime_error("Matrix-vector size mismatch");
    }
    Vector y(A.rows);

// 暂时禁用OpenBLAS以避免编译错误
#if 0 // def USE_OPENBLAS
    // 使用OpenBLAS的cblas_dgemv进行高效矩阵向量乘法
    // 注意：我们的Matrix使用列优先存储，所以需要使用CblasColMajor
    // y = alpha * A * x + beta * y
    // 其中 alpha = 1.0, beta = 0.0
    cblas_dgemv(CblasColMajor, CblasNoTrans,
                static_cast<int>(A.rows), static_cast<int>(A.cols),
                1.0,                    // alpha
                A.data_ptr(), static_cast<int>(A.rows),  // A matrix, lda (列优先时lda=rows)
                x.data_ptr(), 1,            // x vector, incx
                0.0,                    // beta
                y.data_ptr(), 1);           // y vector, incy
#else
    // 回退到纯C++实现
    for (Integer i = 0; i < A.rows; ++i) {
        Real sum = 0.0;
        for (Integer j = 0; j < A.cols; ++j) {
            sum += A(i, j) * x(j);
        }
        y(i) = sum;
    }
#endif

    return y;
}

/**
 * @brief Matrix multiplication (EigenWrapper namespace version)
 */
Matrix matmul(const Matrix& A, const Matrix& B) {
    if (A.cols != B.rows) {
        MatlabError::error("matmul", "Matrix dimensions mismatch",
                       MatlabError::ErrorType::DimensionMismatch);
        return Matrix();
    }
    
    Matrix C(A.rows, B.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < B.cols; ++j) {
            Real sum = 0.0;
            for (Integer k = 0; k < A.cols; ++k) {
                sum += A(i, k) * B(k, j);
            }
            C(i, j) = sum;
        }
    }
    return C;
}

/**
 * @brief MATLAB-style pseudo-inverse with high tolerance for ill-conditioned matrices
 */
Matrix pinv(const Matrix& A, Real tol) {
    try {
        Integer m = A.rows;
        Integer n = A.cols;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(m, n);
        for (Integer i = 0; i < m; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // Use SVD to compute pseudo-inverse with MATLAB-compatible tolerance
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E, Eigen::ComputeFullU | Eigen::ComputeFullV);
        Eigen::VectorXd singularValues = svd.singularValues();

        // Set tolerance using MATLAB's approach
        Real tolerance = (tol > 0) ? tol : EPS * std::max(m, n) * singularValues(0);

        // Compute pseudo-inverse singular values
        Eigen::VectorXd invSingularValues(singularValues.size());
        for (Integer i = 0; i < singularValues.size(); ++i) {
            if (singularValues(i) > tolerance) {
                invSingularValues(i) = 1.0 / singularValues(i);
            } else {
                invSingularValues(i) = 0.0;
            }
        }

        // Compute pseudo-inverse: A+ = V * S+ * U^T
        Eigen::MatrixXd pinvE = svd.matrixV() * invSingularValues.asDiagonal() * svd.matrixU().transpose();

        Matrix result(n, m);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < m; ++j) {
                result(i, j) = pinvE(i, j);
            }
        }
        return result;
#else
        // Simple implementation: for square matrices use inv, for non-square use transpose
        if (m == n) {
            return inv(A);
        } else {
            return A.transpose();
        }
#endif
    }
    catch (const std::exception& e) {
        error_handler("pinv", std::string("Pseudo-inverse failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief Cholesky decomposition with MATLAB-style error handling
 */
Matrix chol(const Matrix& A, bool lower) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        Eigen::LLT<Eigen::MatrixXd> llt(E);
        if (llt.info() != Eigen::Success) {
            error_handler("chol", "Cholesky decomposition failed - matrix not positive definite");
            return Matrix();
        }

        Eigen::MatrixXd L = llt.matrixL();
        Matrix result(n, n);

        if (lower) {
            // Return lower triangular matrix
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = 0; j <= i; ++j) {
                    result(i, j) = L(i, j);
                }
            }
        } else {
            // Return upper triangular matrix (L^T)
            for (Integer i = 0; i < n; ++i) {
                for (Integer j = i; j < n; ++j) {
                    result(i, j) = L(j, i);
                }
            }
        }
        return result;
#else
        // Simple Cholesky decomposition implementation
        Matrix L(n, n);

        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j <= i; ++j) {
                if (i == j) {
                    Real sum = 0.0;
                    for (Integer k = 0; k < j; ++k) {
                        sum += L(j, k) * L(j, k);
                    }
                    Real val = A(j, j) - sum;
                    if (val <= 0) {
                        error_handler("chol", "Matrix not positive definite");
                        return Matrix();
                    }
                    L(j, j) = std::sqrt(val);
                } else {
                    Real sum = 0.0;
                    for (Integer k = 0; k < j; ++k) {
                        sum += L(i, k) * L(j, k);
                    }
                    L(i, j) = (A(i, j) - sum) / L(j, j);
                }
            }
        }

        if (lower) {
            return L;
        } else {
            return L.transpose();
        }
#endif
    }
    catch (const std::exception& e) {
        error_handler("chol", std::string("Cholesky decomposition failed: ") + e.what());
        return Matrix();
    }
}

/**
 * @brief Matrix square root with MATLAB-style tolerance
 */
Matrix sqrtm(const Matrix& A) {
    try {
        check_square(A);
        Integer n = A.rows;

#ifdef USE_EIGEN
        Eigen::MatrixXd E(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                E(i, j) = A(i, j);
            }
        }

        // Use eigenvalue decomposition
        Eigen::SelfAdjointEigenSolver<Eigen::MatrixXd> eigenSolver(E);
        if (eigenSolver.info() != Eigen::Success) {
            error_handler("sqrtm", "Eigendecomposition failed");
            return Matrix();
        }

        Eigen::VectorXd D = eigenSolver.eigenvalues();
        Eigen::MatrixXd V = eigenSolver.eigenvectors();

        // Compute sqrt(D) with enhanced handling of negative eigenvalues
        Real neg_threshold = -DEFAULT_TOL * 100;  // 更宽松的负值阈值，适应数值误差
        Integer neg_count = 0;
        for (Integer i = 0; i < n; ++i) {
            if (D(i) < neg_threshold) {
                neg_count++;
                if (neg_count <= 3) {  // 只显示前3个警告，避免输出过多
                    std::cout << "DEBUG: sqrtm negative eigenvalue " << D(i) << " set to zero" << std::endl;
                }
                D(i) = 0;
            } else if (D(i) < 0) {
                D(i) = 0;  // Small negative values due to numerical errors
            } else {
                D(i) = std::sqrt(D(i));
            }
        }

        Eigen::MatrixXd sqrtE = V * D.asDiagonal() * V.transpose();
        Matrix S(n, n);
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                S(i, j) = sqrtE(i, j);
            }
        }
        return S;

#else
        // Simple diagonal matrix square root
        Matrix S(n, n);
        for (Integer i = 0; i < n; ++i) {
            if (A(i, i) >= 0) {
                S(i, i) = std::sqrt(A(i, i));
            } else {
                warning_handler("sqrtm", "Negative diagonal element");
                S(i, i) = 0;
            }
        }
        return S;
#endif
    }
    catch (const std::exception& e) {
        error_handler("sqrtm", std::string("Matrix square root failed: ") + e.what());
        return Matrix();
    }
}

#ifdef USE_EIGEN
// Type conversion functions for Eigen integration
Eigen::MatrixXd toEigen(const Matrix& A) {
    Eigen::MatrixXd result(A.rows, A.cols);
    for (Integer i = 0; i < A.rows; ++i) {
        for (Integer j = 0; j < A.cols; ++j) {
            result(i, j) = A(i, j);
        }
    }
    return result;
}

Matrix fromEigen(const Eigen::MatrixXd& A) {
    Matrix result(static_cast<Integer>(A.rows()), static_cast<Integer>(A.cols()));
    for (Integer i = 0; i < A.rows(); ++i) {
        for (Integer j = 0; j < A.cols(); ++j) {
            result(i, j) = A(i, j);
        }
    }
    return result;
}
#endif

// MATLAB-compatible matrix functions
Matrix diag(const Vector& v) {
    Integer n = v.size();
    Matrix result(n, n);
    for (Integer i = 0; i < n; ++i) {
        result(i, i) = v(i);
    }
    return result;
}

Vector diag(const Matrix& A) {
    Integer n = std::min(A.rows, A.cols);
    Vector result(n);
    for (Integer i = 0; i < n; ++i) {
        result(i) = A(i, i);
    }
    return result;
}

Matrix identity(Integer n) {
    Matrix I(n, n);
    for (Integer i = 0; i < n; ++i) {
        I(i, i) = 1.0;
    }
    return I;
}

Matrix zeros(Integer m, Integer n) {
    return Matrix(m, n);  // Matrix constructor initializes to zero
}

Matrix ones(Integer m, Integer n) {
    Matrix result(m, n);
    for (Integer i = 0; i < m; ++i) {
        for (Integer j = 0; j < n; ++j) {
            result(i, j) = 1.0;
        }
    }
    return result;
}

// MATLAB-style condition number calculation
Real cond(const Matrix& A) {
    try {
#ifdef USE_EIGEN
        Eigen::MatrixXd E = toEigen(A);
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E);
        Eigen::VectorXd singularValues = svd.singularValues();

        if (singularValues.size() == 0 || singularValues(singularValues.size()-1) == 0) {
            return std::numeric_limits<Real>::infinity();
        }
        return singularValues(0) / singularValues(singularValues.size()-1);
#else
        // Simple estimation using norm ratio
        Matrix Ainv = inv(A);
        return norm(A, "2") * norm(Ainv, "2");
#endif
    }
    catch (const std::exception&) {
        return std::numeric_limits<Real>::infinity();
    }
}

// MATLAB-style determinant calculation
Real det(const Matrix& A) {
    try {
        check_square(A);
#ifdef USE_EIGEN
        Eigen::MatrixXd E = toEigen(A);
        return E.determinant();
#else
        // Simple LU decomposition for determinant calculation
        Integer n = A.rows;
        Matrix L = A;
        Real det_val = 1.0;

        for (Integer i = 0; i < n; ++i) {
            // Find pivot
            Integer pivot = i;
            for (Integer j = i + 1; j < n; ++j) {
                if (std::abs(L(j, i)) > std::abs(L(pivot, i))) {
                    pivot = j;
                }
            }

            if (std::abs(L(pivot, i)) < EPS) {
                return 0.0; // Singular matrix
            }

            if (pivot != i) {
                // Swap rows
                for (Integer j = 0; j < n; ++j) {
                    std::swap(L(i, j), L(pivot, j));
                }
                det_val = -det_val; // Row swap changes sign
            }

            det_val *= L(i, i);

            // Elimination
            for (Integer j = i + 1; j < n; ++j) {
                Real factor = L(j, i) / L(i, i);
                for (Integer k = i; k < n; ++k) {
                    L(j, k) -= factor * L(i, k);
                }
            }
        }
        return det_val;
#endif
    }
    catch (const std::exception& e) {
        error_handler("det", std::string("Determinant calculation failed: ") + e.what());
        return 0.0;
    }
}

// MATLAB-style matrix rank calculation
Integer rank(const Matrix& A, Real tol) {
    try {
#ifdef USE_EIGEN
        Eigen::MatrixXd E = toEigen(A);
        Eigen::JacobiSVD<Eigen::MatrixXd> svd(E);
        Eigen::VectorXd singularValues = svd.singularValues();

        Real tolerance = (tol > 0) ? tol : EPS * std::max(A.rows, A.cols) * singularValues(0);

        Integer rank_val = 0;
        for (Integer i = 0; i < singularValues.size(); ++i) {
            if (singularValues(i) > tolerance) {
                rank_val++;
            }
        }
        return rank_val;
#else
        // Simple estimation
        return std::min(A.rows, A.cols);
#endif
    }
    catch (const std::exception& e) {
        error_handler("rank", std::string("Rank calculation failed: ") + e.what());
        return 0;
    }
}

// Matrix norm calculation
Real norm(const Matrix& A, const std::string& type) {
    try {
        if (type == "fro" || type == "F") {
            // Frobenius norm
            Real sum = 0.0;
            for (Integer i = 0; i < A.rows; ++i) {
                for (Integer j = 0; j < A.cols; ++j) {
                    sum += A(i, j) * A(i, j);
                }
            }
            return std::sqrt(sum);
        } else if (type == "2") {
            // 2-norm (largest singular value)
#ifdef USE_EIGEN
            Eigen::MatrixXd E = toEigen(A);
            Eigen::JacobiSVD<Eigen::MatrixXd> svd(E);
            return svd.singularValues()(0);
#else
            // Fallback to Frobenius norm
            return norm(A, "fro");
#endif
        } else if (type == "inf") {
            // Infinity norm (maximum row sum)
            Real max_sum = 0.0;
            for (Integer i = 0; i < A.rows; ++i) {
                Real row_sum = 0.0;
                for (Integer j = 0; j < A.cols; ++j) {
                    row_sum += std::abs(A(i, j));
                }
                max_sum = std::max(max_sum, row_sum);
            }
            return max_sum;
        } else {
            // Default to Frobenius norm
            return norm(A, "fro");
        }
    }
    catch (const std::exception& e) {
        error_handler("norm", std::string("Norm calculation failed: ") + e.what());
        return 0.0;
    }
}

} // namespace EigenWrapper

// 简单的测试main函数
#ifdef TEST_EIGEN_WRAPPER
int main() {
    std::cout << "Testing eigen_wrapper compilation..." << std::endl;

    // 简单的矩阵测试
    Matrix A(2, 2);
    A(0, 0) = 1.0; A(0, 1) = 2.0;
    A(1, 0) = 3.0; A(1, 1) = 4.0;

    std::cout << "Matrix A created successfully" << std::endl;
    std::cout << "eigen_wrapper compilation test passed!" << std::endl;

    return 0;
}
#endif
