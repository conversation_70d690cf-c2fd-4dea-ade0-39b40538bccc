#include "../include/error_handling.hpp"
#include <fstream>
#include <ctime>

namespace MatlabError {

// 初始化全局错误选项
ErrorOptions error_options;

/**
 * @brief 获取当前时间字符串
 * 
 * @return 格式化的时间字符串
 */
std::string current_time_string() {
    std::time_t now = std::time(nullptr);
    char buffer[80];
    std::tm timeinfo_buf;
    std::tm* timeinfo = nullptr;
#ifdef _WIN32
    localtime_s(&timeinfo_buf, &now);
    timeinfo = &timeinfo_buf;
#else
    timeinfo = std::localtime(&now);
#endif
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", timeinfo);
    return buffer;
}

/**
 * @brief 记录消息到日志文件
 * 
 * @param message 要记录的消息
 */
void log_message(const std::string& message) {
    if (!error_options.log_to_file) return;
    
    std::ofstream log_file(error_options.log_file, std::ios_base::app);
    if (log_file.is_open()) {
        log_file << current_time_string() << " - " << message << std::endl;
        log_file.close();
    }
}

void set_error_options(const ErrorOptions& options) {
    error_options = options;
}

void error(const std::string& function_name, const std::string& message, ErrorType error_type) {
    std::ostringstream full_message;
    full_message << "Error in " << function_name << ": " << message;
    
    if (error_options.verbose) {
        std::cerr << full_message.str() << std::endl;
    }
    
    log_message(full_message.str());
    
    if (error_options.throw_exceptions) {
        switch (error_type) {
            case ErrorType::DimensionMismatch:
                throw std::invalid_argument(full_message.str());
            case ErrorType::SingularMatrix:
                throw std::domain_error(full_message.str());
            case ErrorType::NotImplemented:
                throw std::logic_error(full_message.str());
            case ErrorType::OutOfBounds:
                throw std::out_of_range(full_message.str());
            case ErrorType::ValueError:
                throw std::invalid_argument(full_message.str());
            case ErrorType::InternalError:
            case ErrorType::RuntimeError:
            default:
                throw std::runtime_error(full_message.str());
        }
    }
}

void warning(const std::string& function_name, const std::string& message, WarningLevel level) {
    std::string level_str;
    switch (level) {
        case WarningLevel::Info:
            level_str = "Info";
            break;
        case WarningLevel::Caution:
            level_str = "Caution";
            break;
        case WarningLevel::Warning:
            level_str = "Warning";
            break;
        case WarningLevel::Severe:
            level_str = "Severe Warning";
            break;
    }
    
    std::ostringstream full_message;
    full_message << level_str << " in " << function_name << ": " << message;
    
    if (error_options.verbose) {
        std::cerr << full_message.str() << std::endl;
    }
    
    log_message(full_message.str());
}

bool check_dimensions(const std::pair<int, int>& A, const std::pair<int, int>& B, 
                     const std::string& operation, const std::string& function_name) {
    if (A.first != B.first || A.second != B.second) {
        std::ostringstream msg;
        msg << "Dimension mismatch for " << operation << ". "
            << "First matrix is " << A.first << "x" << A.second
            << ", second matrix is " << B.first << "x" << B.second;
        
        error(function_name, msg.str(), ErrorType::DimensionMismatch);
        return false;
    }
    return true;
}

bool check_square(const std::pair<int, int>& A, const std::string& function_name) {
    if (A.first != A.second) {
        std::ostringstream msg;
        msg << "Matrix must be square. Got " << A.first << "x" << A.second;
        
        error(function_name, msg.str(), ErrorType::ValueError);
        return false;
    }
    return true;
}

bool check_bounds(int index, int max_index, const std::string& function_name) {
    if (index < 0 || index >= max_index) {
        std::ostringstream msg;
        msg << "Index " << index << " out of bounds [0, " << (max_index-1) << "]";
        
        error(function_name, msg.str(), ErrorType::OutOfBounds);
        return false;
    }
    return true;
}

} // namespace MatlabError 