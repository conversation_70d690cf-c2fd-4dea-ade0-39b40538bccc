#include "../include/common_types.hpp"
#include "../include/mesh_sphere2dA.hpp"
#include "../include/eigen_wrapper.hpp"
#include "../include/setup_basis.hpp"  // For Basis struct and related functions
#include "../include/lgwt.hpp"         // For lgwt function
#include "../include/bspln.hpp"        // For bspln function
#include "../include/stiffness_matrix.hpp"  // For stiffness_matrix function
#include "../include/mass_matrix.hpp"       // For mass_matrix function
#include <iostream>
#include <vector>
#include <cmath>
#include <tuple>  // For std::tuple and std::get

// Forward declarations for helper functions - using exact signatures from header files
Vector Get_Knot_Vector(int n, int k);
double bspln(const Vector& t, int n, int i, int k, double x);
Matrix stiffness_matrix(int p, int N1, const Vector& t1, const Vector& t2,
                       const Matrix& xint, const Matrix& wint, int option);
Matrix mass_matrix(int p, int N1, int N2, const Vector& t1, const Vector& t2,
                  const Matrix& xint, const Matrix& wint, int der);
Matrix inner_product2(const Basis& basis1, const Basis& basis2);

/**
 * @brief Generate DFD matrices for 2D domains - Perfect MATLAB translation
 * This function exactly follows the MATLAB gen_DFDMatrices2dA.m implementation
 */
void gen_DFDMatrices2dA(std::vector<Domain2dA>& OM) {
    Integer ndomains = static_cast<Integer>(OM.size());

    // First pass: Initialize all Basis structures for all domains
    for (Integer iom = 0; iom < ndomains; ++iom) {
        Domain2dA& dom = OM[iom];
        Integer px1 = dom.px1;
        Integer px2 = px1 - 1;
        Integer pz1 = dom.pz1;
        Integer pz2 = pz1 - 1;
        Integer Nx1 = dom.Nx1;
        Integer Nx2 = Nx1 - 1;
        Integer Nz1 = dom.Nz1;
        Integer Nz2 = Nz1 - 1;
        Integer kx1 = px1 + 1;
        Integer kz1 = pz1 + 1;
        Integer kx2 = kx1 - 1;
        Integer kz2 = kz1 - 1;

        // Generate and store knot vectors for this domain
        dom.Bx1.tx = Get_Knot_Vector(Nx1, kx1);
        dom.Bx2.tx = Get_Knot_Vector(Nx2, kx2);
        dom.Bz1.tx = Get_Knot_Vector(Nz1, kz1);
        dom.Bz2.tx = Get_Knot_Vector(Nz2, kz2);
        dom.Bx1.nb = Nx1;
        dom.Bx1.pb = px1;
        dom.Bx2.nb = Nx2;
        dom.Bx2.pb = px2;
        dom.Bz1.nb = Nz1;
        dom.Bz1.pb = pz1;
        dom.Bz2.nb = Nz2;
        dom.Bz2.pb = pz2;
    }

    // Second pass: Process DFD matrices for each domain
    for (Integer iom = 0; iom < ndomains; ++iom) {
        std::cout << "...preparing all the DFD Matrices of domain " << (iom + 1) 
                  << " with " << OM[iom].Nx1 << " and " << OM[iom].Nz1 << " points ... " << std::endl;
        
        try {
            Domain2dA& dom = OM[iom];

            // Extract parameters exactly as in MATLAB (lines 9-16)
            Integer px1 = dom.px1;
            Integer px2 = px1 - 1;
            Integer pz1 = dom.pz1;
            Integer pz2 = pz1 - 1;
            
            // Gaussian integration order (line 13)
            Integer ord_gi = 6;
            
            Integer Nx1 = dom.Nx1;
            Integer Nx2 = Nx1 - 1;
            Integer Nz1 = dom.Nz1;
            Integer Nz2 = Nz1 - 1;
            
            // Grid spacing (lines 18-19)
            Real dx = 1.0 / (Real(Nx1) - 1.0);
            Real dz = 1.0 / (Real(Nz1) - 1.0);
            
            // Grid points (lines 21-22)
            Vector xps1(Nx1);
            Vector zps1(Nz1);
            for (Integer j = 0; j < Nx1; ++j) {
                xps1.at(j, 0) = j * dx;  // MATLAB: xps1 = 0:dx:1 - 正确的Vector访问
            }
            for (Integer j = 0; j < Nz1; ++j) {
                zps1.at(j, 0) = j * dz;  // MATLAB: zps1 = 0:dz:1 - 正确的Vector访问
            }
            
            // Knot vector parameters (lines 25-28)
            Integer kx1 = px1 + 1;
            Integer kz1 = pz1 + 1;
            Integer kx2 = kx1 - 1;
            Integer kz2 = kz1 - 1;
            
            // Use knot vectors already generated in first pass
            Vector tx1 = dom.Bx1.tx;
            Vector tx2 = dom.Bx2.tx;
            Vector tz1 = dom.Bz1.tx;
            Vector tz2 = dom.Bz2.tx;
            
            // Integration intervals setup (lines 36-45)
            Integer NBx_intervals = Nx1 - px1;
            Integer NBz_intervals = Nz1 - pz1;

            Matrix xintervals(NBx_intervals, 2);
            Matrix zintervals(NBz_intervals, 2);
            
            // MATLAB: xintervals(:,1) = tx1((px1+1):Nx1);
            for (Integer i = 0; i < NBx_intervals; ++i) {
                Integer idx1 = px1 + i;
                Integer idx2 = px1 + 1 + i;
                if (idx1 >= tx1.size() || idx2 >= tx1.size()) {
                    throw std::runtime_error("tx1 access out of bounds");
                }
                xintervals(i, 0) = tx1.at(idx1, 0);      // tx1((px1+1):Nx1) - 正确的Vector访问
                xintervals(i, 1) = tx1.at(idx2, 0);  // tx1((px1+2):Nx1+1) - 正确的Vector访问
            }
            
            // MATLAB: zintervals(:,1) = tz1((pz1+1):Nz1);
            for (Integer i = 0; i < NBz_intervals; ++i) {
                Integer idx1 = pz1 + i;
                Integer idx2 = pz1 + 1 + i;
                if (idx1 >= tz1.size() || idx2 >= tz1.size()) {
                    throw std::runtime_error("tz1 access out of bounds");
                }
                zintervals(i, 0) = tz1.at(idx1, 0);      // tz1((pz1+1):Nz1) - 正确的Vector访问
                zintervals(i, 1) = tz1.at(idx2, 0);  // tz1((pz1+2):Nz1+1) - 正确的Vector访问
            }
            
            // Integration points and weights (lines 48-58)
            Matrix xint(NBx_intervals, ord_gi);
            Matrix zint(NBz_intervals, ord_gi);
            Matrix wxint(NBx_intervals, ord_gi);
            Matrix wzint(NBz_intervals, ord_gi);
            
            // Generate Gauss-Legendre quadrature points for X direction

            for (Integer kd = 0; kd < NBx_intervals; ++kd) {

                std::tuple<Vector, Vector> result = lgwt(ord_gi, xintervals(kd, 0), xintervals(kd, 1));
                Vector x_points = std::get<0>(result);
                Vector x_weights = std::get<1>(result);

                for (Integer g = 0; g < ord_gi; ++g) {
                    if (g >= x_points.size()) {

                        throw std::runtime_error("x_points access out of bounds");
                    }
                    xint(kd, g) = x_points.at(g, 0);   // Integration points - 正确的Vector访问方式
                    wxint(kd, g) = x_weights.at(g, 0); // Weights - 正确的Vector访问方式
                }
            }

            // Generate Gauss-Legendre quadrature points for Z direction
            for (Integer kd = 0; kd < NBz_intervals; ++kd) {
                std::tuple<Vector, Vector> result = lgwt(ord_gi, zintervals(kd, 0), zintervals(kd, 1));
                Vector z_points = std::get<0>(result);
                Vector z_weights = std::get<1>(result);
                for (Integer g = 0; g < ord_gi; ++g) {
                    if (g >= z_points.size()) {
                        throw std::runtime_error("z_points access out of bounds");
                    }
                    zint(kd, g) = z_points.at(g, 0);   // Integration points - 正确的Vector访问方式
                    wzint(kd, g) = z_weights.at(g, 0); // Weights - 正确的Vector访问方式
                }
            }

            // Basis function matrices - X direction (lines 61-77)

            Matrix bx1(Nx1, Nx1);
            for (Integer i = 1; i <= Nx1; ++i) {
                for (Integer j = 1; j <= Nx1; ++j) {
                    if (j - 1 >= xps1.size()) {
                        throw std::runtime_error("xps1 access out of bounds");
                    }
                    Real xj = xps1.at(j - 1, 0);  // MATLAB: xj = xps1(j) - 正确的Vector访问
                    bx1(i - 1, j - 1) = bspln(tx1, Nx1, i, kx1, xj);
                }
            }
            Matrix bx1T = bx1.transpose();  // MATLAB: bx1T = bx1'
            
            Matrix bx2(Nx2, Nx1);  // each col is one B2
            for (Integer i = 1; i <= Nx2; ++i) {
                for (Integer j = 1; j <= Nx1; ++j) {
                    Real x0 = xps1.at(j - 1, 0);  // MATLAB: x0 = xps1(j) - 正确的Vector访问
                    bx2(i - 1, j - 1) = bspln(tx2, Nx2, i, kx2, x0);
                }
            }
            Matrix bx2T = bx2.transpose();  // MATLAB: bx2T = bx2'

            // Basis function matrices - Z direction (lines 79-95)
            Matrix bz1(Nz1, Nz1);
            for (Integer i = 1; i <= Nz1; ++i) {
                for (Integer j = 1; j <= Nz1; ++j) {
                    Real zj = zps1.at(j - 1, 0);  // MATLAB: zj = zps1(j) - 正确的Vector访问
                    bz1(i - 1, j - 1) = bspln(tz1, Nz1, i, kz1, zj);
                }
            }
            Matrix bz1T = bz1.transpose();  // MATLAB: bz1T = bz1'

            Matrix bz2(Nz2, Nz1);  // each col is one B2
            for (Integer i = 1; i <= Nz2; ++i) {
                for (Integer j = 1; j <= Nz1; ++j) {
                    Real z0 = zps1.at(j - 1, 0);  // MATLAB: z0 = zps1(j) - 正确的Vector访问
                    bz2(i - 1, j - 1) = bspln(tz2, Nz2, i, kz2, z0);  // Note: kz2, not kx2!
                }
            }
            Matrix bz2T = bz2.transpose();  // MATLAB: bz2T = bz2'
            
            // Stiffness matrices (lines 97-105)
            Matrix kkx12 = stiffness_matrix(px1, Nx1, tx1, tx2, xint, wxint, 0);
            Matrix kkz12 = stiffness_matrix(pz1, Nz1, tz1, tz2, zint, wzint, 0);
            Matrix kkx21 = stiffness_matrix(px1, Nx1, tx1, tx2, xint, wxint, 1);
            Matrix kkz21 = stiffness_matrix(pz1, Nz1, tz1, tz2, zint, wzint, 1);
            
            // Mass matrices (lines 108-116)
            Matrix mmx11 = mass_matrix(px1, Nx1, Nx1, tx1, tx1, xint, wxint, 0);
            Matrix mmz11 = mass_matrix(pz1, Nz1, Nz1, tz1, tz1, zint, wzint, 0);
            Matrix mmx22 = mass_matrix(px1, Nx2, Nx2, tx2, tx2, xint, wxint, 1);
            Matrix mmz22 = mass_matrix(pz1, Nz2, Nz2, tz2, tz2, zint, wzint, 1);
            
            // Matrix square roots and inverses (lines 119-141)
            // X direction matrices
            Matrix LxT11 = EigenWrapper::sqrtm(mmx11);    // upper triangle
            Matrix Lx11 = LxT11.transpose(); // lower triangle
            Matrix invLx11 = EigenWrapper::inv(Lx11);
            Matrix invLxT11 = EigenWrapper::inv(LxT11);

            Matrix LxT22 = EigenWrapper::sqrtm(mmx22);
            Matrix Lx22 = LxT22.transpose();
            Matrix invLx22 = EigenWrapper::inv(Lx22);
            Matrix invLxT22 = EigenWrapper::inv(LxT22);

            // Z direction matrices
            Matrix LzT11 = EigenWrapper::sqrtm(mmz11);    // upper triangle
            Matrix Lz11 = LzT11.transpose(); // lower triangle
            Matrix invLz11 = EigenWrapper::inv(Lz11);
            Matrix invLzT11 = EigenWrapper::inv(LzT11);

            Matrix LzT22 = EigenWrapper::sqrtm(mmz22);
            Matrix Lz22 = LzT22.transpose();
            Matrix invLz22 = EigenWrapper::inv(Lz22);
            Matrix invLzT22 = EigenWrapper::inv(LzT22);
            
            // Store basic matrices in domain structure (lines 144-160)
            dom.bxT1 = bx1T;
            dom.bxT2 = bx2T;
            dom.bzT1 = bz1T;
            dom.bzT2 = bz2T;
            
            dom.kkx12 = kkx12;
            dom.kkx21 = kkx21;
            dom.kkz12 = kkz12;
            dom.kkz21 = kkz21;
            
            dom.invLx11 = invLx11;
            dom.invLx22 = invLx22;
            dom.invLxT11 = invLxT11;
            dom.invLxT22 = invLxT22;
            dom.invLz11 = invLz11;
            dom.invLz22 = invLz22;
            dom.invLzT11 = invLzT11;
            dom.invLzT22 = invLzT22;
            
            // Setup basis functions for neighbor connections (lines 165-168)
            // Create basis structures for current domain
            Basis basis_Nx1;
            basis_Nx1.nb = Nx1;
            basis_Nx1.pb = px1;
            basis_Nx1.tx = dom.Bx1.tx;

            Basis basis_Nx2;
            basis_Nx2.nb = Nx2;
            basis_Nx2.pb = px2;
            basis_Nx2.tx = dom.Bx2.tx;

            Basis basis_Nz1;
            basis_Nz1.nb = Nz1;
            basis_Nz1.pb = pz1;
            basis_Nz1.tx = dom.Bz1.tx;

            Basis basis_Nz2;
            basis_Nz2.nb = Nz2;
            basis_Nz2.pb = pz2;
            basis_Nz2.tx = dom.Bz2.tx;
            
            // Process neighbor connections exactly as in MATLAB (lines 169-279)

            // Connected left domain (lines 169-194)
            Integer iNbr_mo = dom.iNbr_mo;
            if (iNbr_mo != 0) {  // MATLAB: if iNbr_mo~=0, C++: if iNbr_mo~=0
                Integer flip = dom.iFace_mo;
                if (std::abs(flip) == 1 || std::abs(flip) == 2) {
                    // get z from z; so Dzz
                    Integer Mz1 = OM[iNbr_mo - 1].Nz1;  // MATLAB 1-based to C++ 0-based
                    Integer Mz2 = Mz1 - 1;
                    Integer pMz1 = OM[iNbr_mo - 1].pz1;
                    Integer pMz2 = pMz1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mz1;
                    basis_Mz1.nb = Mz1;
                    basis_Mz1.pb = pMz1;
                    basis_Mz1.tx = OM[iNbr_mo - 1].Bz1.tx;

                    Basis basis_Mz2;
                    basis_Mz2.nb = Mz2;
                    basis_Mz2.pb = pMz2;
                    basis_Mz2.tx = OM[iNbr_mo - 1].Bz2.tx;

                    dom.Dzz210mo = inner_product2(basis_Nz2, basis_Mz1);
                    dom.Dzz120mo = inner_product2(basis_Nz1, basis_Mz2);
                    dom.Dzz110mo = inner_product2(basis_Nz1, basis_Mz1);
                    dom.Dzz220mo = inner_product2(basis_Nz2, basis_Mz2);
                } else {
                    // flip = 3 or 4, get z from x; so Dzx
                    Integer Mx1 = OM[iNbr_mo - 1].Nx1;
                    Integer Mx2 = Mx1 - 1;
                    Integer pMx1 = OM[iNbr_mo - 1].px1;
                    Integer pMx2 = pMx1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mx1;
                    basis_Mx1.nb = Mx1;
                    basis_Mx1.pb = pMx1;
                    basis_Mx1.tx = OM[iNbr_mo - 1].Bx1.tx;

                    Basis basis_Mx2;
                    basis_Mx2.nb = Mx2;
                    basis_Mx2.pb = pMx2;
                    basis_Mx2.tx = OM[iNbr_mo - 1].Bx2.tx;

                    dom.Dzx210mo = inner_product2(basis_Nz2, basis_Mx1);
                    dom.Dzx120mo = inner_product2(basis_Nz1, basis_Mx2);
                    dom.Dzx110mo = inner_product2(basis_Nz1, basis_Mx1);
                    dom.Dzx220mo = inner_product2(basis_Nz2, basis_Mx2);
                }
            }

            // Connected right domain (lines 197-223)
            Integer iNbr_po = dom.iNbr_po;
            if (iNbr_po != 0) {  // MATLAB: if iNbr_po~=0, C++: if iNbr_po~=0
                Integer flip = dom.iFace_po;
                if (std::abs(flip) == 1 || std::abs(flip) == 2) {
                    // get z from z; so Dzz
                    Integer Mz1 = OM[iNbr_po - 1].Nz1;
                    Integer Mz2 = Mz1 - 1;
                    Integer pMz1 = OM[iNbr_po - 1].pz1;
                    Integer pMz2 = pMz1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mz1;
                    basis_Mz1.nb = Mz1;
                    basis_Mz1.pb = pMz1;
                    basis_Mz1.tx = OM[iNbr_po - 1].Bz1.tx;

                    Basis basis_Mz2;
                    basis_Mz2.nb = Mz2;
                    basis_Mz2.pb = pMz2;
                    basis_Mz2.tx = OM[iNbr_po - 1].Bz2.tx;

                    dom.Dzz210po = inner_product2(basis_Nz2, basis_Mz1);
                    dom.Dzz120po = inner_product2(basis_Nz1, basis_Mz2);
                    dom.Dzz110po = inner_product2(basis_Nz1, basis_Mz1);
                    dom.Dzz220po = inner_product2(basis_Nz2, basis_Mz2);
                } else {
                    // flip = 3 or 4, get z from x; so Dzx
                    Integer Mx1 = OM[iNbr_po - 1].Nx1;
                    Integer Mx2 = Mx1 - 1;
                    Integer pMx1 = OM[iNbr_po - 1].px1;
                    Integer pMx2 = pMx1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mx1;
                    basis_Mx1.nb = Mx1;
                    basis_Mx1.pb = pMx1;
                    basis_Mx1.tx = OM[iNbr_po - 1].Bx1.tx;

                    Basis basis_Mx2;
                    basis_Mx2.nb = Mx2;
                    basis_Mx2.pb = pMx2;
                    basis_Mx2.tx = OM[iNbr_po - 1].Bx2.tx;

                    dom.Dzx210po = inner_product2(basis_Nz2, basis_Mx1);
                    dom.Dzx120po = inner_product2(basis_Nz1, basis_Mx2);
                    dom.Dzx110po = inner_product2(basis_Nz1, basis_Mx1);
                    dom.Dzx220po = inner_product2(basis_Nz2, basis_Mx2);
                }
            }

            // Connected lower domain (lines 226-251)
            Integer iNbr_om = dom.iNbr_om;
            if (iNbr_om != 0) {  // MATLAB: if iNbr_om~=0, C++: if iNbr_om~=0
                Integer flip = dom.iFace_om;
                if (std::abs(flip) == 3 || std::abs(flip) == 4) {
                    // get x from x; so Dxx
                    Integer Mx1 = OM[iNbr_om - 1].Nx1;
                    Integer Mx2 = Mx1 - 1;
                    Integer pMx1 = OM[iNbr_om - 1].px1;
                    Integer pMx2 = pMx1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mx1;
                    basis_Mx1.nb = Mx1;
                    basis_Mx1.pb = pMx1;
                    basis_Mx1.tx = OM[iNbr_om - 1].Bx1.tx;

                    Basis basis_Mx2;
                    basis_Mx2.nb = Mx2;
                    basis_Mx2.pb = pMx2;
                    basis_Mx2.tx = OM[iNbr_om - 1].Bx2.tx;

                    dom.Dxx210om = inner_product2(basis_Nx2, basis_Mx1);
                    dom.Dxx120om = inner_product2(basis_Nx1, basis_Mx2);
                    dom.Dxx110om = inner_product2(basis_Nx1, basis_Mx1);
                    dom.Dxx220om = inner_product2(basis_Nx2, basis_Mx2);
                } else {
                    // flip = 1 or 2, get x from z; so Dxz
                    Integer Mz1 = OM[iNbr_om - 1].Nz1;
                    Integer Mz2 = Mz1 - 1;
                    Integer pMz1 = OM[iNbr_om - 1].pz1;
                    Integer pMz2 = pMz1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mz1;
                    basis_Mz1.nb = Mz1;
                    basis_Mz1.pb = pMz1;
                    basis_Mz1.tx = OM[iNbr_om - 1].Bz1.tx;

                    Basis basis_Mz2;
                    basis_Mz2.nb = Mz2;
                    basis_Mz2.pb = pMz2;
                    basis_Mz2.tx = OM[iNbr_om - 1].Bz2.tx;

                    dom.Dxz210om = inner_product2(basis_Nx2, basis_Mz1);
                    dom.Dxz120om = inner_product2(basis_Nx1, basis_Mz2);
                    dom.Dxz110om = inner_product2(basis_Nx1, basis_Mz1);
                    dom.Dxz220om = inner_product2(basis_Nx2, basis_Mz2);
                }
            }

            // Connected upper domain (lines 254-279)
            Integer iNbr_op = dom.iNbr_op;
            if (iNbr_op != 0) {  // MATLAB: if iNbr_op~=0, C++: if iNbr_op~=0
                Integer flip = dom.iFace_op;
                if (std::abs(flip) == 3 || std::abs(flip) == 4) {
                    // get x from x; so Dxx
                    Integer Mx1 = OM[iNbr_op - 1].Nx1;
                    Integer Mx2 = Mx1 - 1;
                    Integer pMx1 = OM[iNbr_op - 1].px1;
                    Integer pMx2 = pMx1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mx1;
                    basis_Mx1.nb = Mx1;
                    basis_Mx1.pb = pMx1;
                    basis_Mx1.tx = OM[iNbr_op - 1].Bx1.tx;

                    Basis basis_Mx2;
                    basis_Mx2.nb = Mx2;
                    basis_Mx2.pb = pMx2;
                    basis_Mx2.tx = OM[iNbr_op - 1].Bx2.tx;

                    dom.Dxx210op = inner_product2(basis_Nx2, basis_Mx1);
                    dom.Dxx120op = inner_product2(basis_Nx1, basis_Mx2);
                    dom.Dxx110op = inner_product2(basis_Nx1, basis_Mx1);
                    dom.Dxx220op = inner_product2(basis_Nx2, basis_Mx2);
                } else {
                    // flip = 1 or 2, get x from z; so Dxz
                    Integer Mz1 = OM[iNbr_op - 1].Nz1;
                    Integer Mz2 = Mz1 - 1;
                    Integer pMz1 = OM[iNbr_op - 1].pz1;
                    Integer pMz2 = pMz1 - 1;

                    // Create basis structures for neighbor domain
                    Basis basis_Mz1;
                    basis_Mz1.nb = Mz1;
                    basis_Mz1.pb = pMz1;
                    basis_Mz1.tx = OM[iNbr_op - 1].Bz1.tx;

                    Basis basis_Mz2;
                    basis_Mz2.nb = Mz2;
                    basis_Mz2.pb = pMz2;
                    basis_Mz2.tx = OM[iNbr_op - 1].Bz2.tx;

                    dom.Dxz210op = inner_product2(basis_Nx2, basis_Mz1);
                    dom.Dxz120op = inner_product2(basis_Nx1, basis_Mz2);
                    dom.Dxz110op = inner_product2(basis_Nx1, basis_Mz1);
                    dom.Dxz220op = inner_product2(basis_Nx2, basis_Mz2);
                }
            }

            std::cout << "  All DFD matrices computed successfully for domain " << (iom + 1) << std::endl;

        } catch (const std::exception& e) {
            std::cerr << "Error processing domain " << (iom + 1) << ": " << e.what() << std::endl;
            throw;
        }
    }

    std::cout << "gen_DFDMatrices2dA completed successfully for all " << ndomains << " domains" << std::endl;
}
