#include "../include/get_receiver2dA.hpp"
#include "../include/bspln.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include <iostream>
#include <vector>

// 创建空的接收器结构体
Receiver2dA create_Receiver2dA() {
    Receiver2dA rece;
    return rece;
}

std::vector<Receiver2dA> get_receiver2dA(const std::vector<Domain2dA>& OM,
                                       double dt,
                                       int nt) {
    // 创建接收器数组
    std::vector<Receiver2dA> receivers;
    
    // 创建一个接收器
    Receiver2dA rece = create_Receiver2dA();
    
    // 初始化ur数组，对应MATLAB中的 rece.ur = zeros(nt,3)
    rece.ur = Vector(nt);
    
    // 设置时间数组，对应MATLAB中的 rece.ur(:,1) = (1:nt)'*dt
    rece.time.resize(nt);
    for (int i = 0; i < nt; ++i) {
        rece.time[i] = (i + 1) * dt;
    }

    // 设置接收器所在域
    rece.iom = 0;  // 使用0-based索引，对应MATLAB的1
    int rom = rece.iom;

    // 接收器的局部坐标
    rece.xlocal = 1.0;  // 和MATLAB版本一致
    rece.zlocal = 1.0;  // 和MATLAB版本一致

    double xs = rece.xlocal;
    double zs = rece.zlocal;

    // 检查索引有效性
    if (rom >= static_cast<int>(OM.size())) {
        std::cerr << "false(" << rom << ")too many" << OM.size() << std::endl;
        // 添加到返回数组并返回，避免后续错误
        receivers.push_back(rece);
        return receivers;
    }
    
    // 获取域参数
    int Nx1 = OM[rom].Nx1;
    int Nx2 = Nx1 - 1;
    int Nz1 = OM[rom].Nz1;
    int Nz2 = Nz1 - 1;

    int px1 = OM[rom].px1;
    int px2 = px1 - 1;
    int pz1 = OM[rom].pz1;
    int pz2 = pz1 - 1;

    int kx1 = px1 + 1;
    int kz1 = pz1 + 1;
    int kx2 = px2 + 1;
    int kz2 = pz2 + 1;
    
    // 获取节点向量
    Vector tx1 = Get_Knot_Vector(Nx1, kx1);
    Vector tx2 = Get_Knot_Vector(Nx2, kx2);
    Vector tz1 = Get_Knot_Vector(Nz1, kz1);
    Vector tz2 = Get_Knot_Vector(Nz2, kz2);
    
    // 计算B样条基函数值
    rece.rbx1 = Vector(Nx1);
    rece.rbx2 = Vector(Nx2);
    rece.rbz1 = Vector(Nz1);
    rece.rbz2 = Vector(Nz2);
    
    for (int i = 0; i < Nx1; ++i) {
        rece.rbx1.at(i, 0) = bspln(tx1, Nx1, i, kx1, xs);
    }

    for (int i = 0; i < Nx2; ++i) {
        rece.rbx2.at(i, 0) = bspln(tx2, Nx2, i, kx2, xs);
    }

    for (int j = 0; j < Nz1; ++j) {
        rece.rbz1.at(j, 0) = bspln(tz1, Nz1, j, kz1, zs);
    }

    for (int j = 0; j < Nz2; ++j) {
        rece.rbz2.at(j, 0) = bspln(tz2, Nz2, j, kz2, zs);
    }
    
    // 初始化波形数组
    rece.trace_u12.resize(nt, 0.0);
    rece.trace_u21.resize(nt, 0.0);
    
    // 添加到接收器数组
    receivers.push_back(rece);
    
    return receivers;
} 