#include "../include/get_source2dA.hpp"
#include "../include/bspln.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include <iostream>
#include <cmath>

// 如果M_PI未定义，则定义它
#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

std::vector<SourceStruct> get_source2dA(const std::vector<Domain2dA>& OM,
                                      double freq,
                                      double dt,
                                      int nt) {
    std::vector<SourceStruct> source;
    
    // 创建单个震源
    SourceStruct src = create_empty_source2d();
    
    // 与MATLAB版本一致，设置震源参数
    src.iom = 3;  // 震源所在域的索引
    if (src.iom >= static_cast<int>(OM.size())) {
        std::cout << "警告: 震源域索引超出范围，使用域0" << std::endl;
        src.iom = 0;
    }

    int som = src.iom;
    
    // 震源的局部坐标
    src.xlocal = 0.5;
    src.zlocal = 0.5;
    
    double xs = src.xlocal;
    double zs = src.zlocal;

    // 获取域参数
    int Nx1 = OM[som].Nx1;
    int Nx2 = Nx1 - 1;
    int Nz1 = OM[som].Nz1;
    int Nz2 = Nz1 - 1;

    int px1 = OM[som].px1;
    int px2 = px1 - 1;
    int pz1 = OM[som].pz1;
    int pz2 = pz1 - 1;

    int kx1 = px1 + 1;
    int kz1 = pz1 + 1;
    int kx2 = px2 + 1;
    int kz2 = pz2 + 1;
    
    // 获取节点向量
    Vector tx1 = Get_Knot_Vector(Nx1, kx1);
    Vector tx2 = Get_Knot_Vector(Nx2, kx2);
    Vector tz1 = Get_Knot_Vector(Nz1, kz1);
    Vector tz2 = Get_Knot_Vector(Nz2, kz2);
    
    // 计算B样条基函数值
    Vector sgbx1(Nx1);
    Vector sgbx2(Nx2);
    Vector sgbz1(Nz1);
    Vector sgbz2(Nz2);
    
    for (int i = 0; i < Nx1; ++i) {
        sgbx1.at(i, 0) = bspln(tx1, Nx1, i, kx1, xs);
    }

    for (int i = 0; i < Nx2; ++i) {
        sgbx2.at(i, 0) = bspln(tx2, Nx2, i, kx2, xs);
    }

    for (int j = 0; j < Nz1; ++j) {
        sgbz1.at(j, 0) = bspln(tz1, Nz1, j, kz1, zs);
    }

    for (int j = 0; j < Nz2; ++j) {
        sgbz2.at(j, 0) = bspln(tz2, Nz2, j, kz2, zs);
    }
    
    // 使用质量矩阵的逆进行处理
    const Matrix& invLx11 = OM[som].invLx11;
    const Matrix& invLx22 = OM[som].invLx22;
    const Matrix& invLz11 = OM[som].invLz11;
    const Matrix& invLz22 = OM[som].invLz22;
    
    // 计算源在网格1中的影响矩阵
    Matrix invMsg12(Nx1, Nz2);
    // 修复：Matrix乘以Vector返回Matrix，需要转换为Vector
    Vector sgbx_inv1(Nx1);
    for (int i = 0; i < Nx1; ++i) {
        sgbx_inv1(i) = 0.0;
        for (int k = 0; k < invLx11.cols; ++k) {
            if (k < sgbx1.size()) {
                sgbx_inv1(i) += invLx11(i, k) * sgbx1(k);
            }
        }
    }

    Vector sgbz_inv2(Nz2);
    for (int j = 0; j < Nz2; ++j) {
        sgbz_inv2(j) = 0.0;
        for (int k = 0; k < invLz22.cols; ++k) {
            if (k < sgbz2.size()) {
                sgbz_inv2(j) += invLz22(j, k) * sgbz2(k);
            }
        }
    }

    for (int i = 0; i < Nx1; ++i) {
        for (int j = 0; j < Nz2; ++j) {
            invMsg12(i, j) = sgbx_inv1(i) * sgbz_inv2(j);
        }
    }
    
    // 计算源在网格2中的影响矩阵
    Matrix invMsg21(Nx2, Nz1);
    // 修复：Matrix乘以Vector返回Matrix，需要转换为Vector
    Vector sgbx_inv2(Nx2);
    for (int i = 0; i < Nx2; ++i) {
        sgbx_inv2(i) = 0.0;
        for (int k = 0; k < invLx22.cols; ++k) {
            if (k < sgbx2.size()) {
                sgbx_inv2(i) += invLx22(i, k) * sgbx2(k);
            }
        }
    }

    Vector sgbz_inv1(Nz1);
    for (int j = 0; j < Nz1; ++j) {
        sgbz_inv1(j) = 0.0;
        for (int k = 0; k < invLz11.cols; ++k) {
            if (k < sgbz1.size()) {
                sgbz_inv1(j) += invLz11(j, k) * sgbz1(k);
            }
        }
    }

    for (int i = 0; i < Nx2; ++i) {
        for (int j = 0; j < Nz1; ++j) {
            invMsg21(i, j) = sgbx_inv2(i) * sgbz_inv1(j);
        }
    }
    
    src.invMsg12 = invMsg12;
    src.invMsg21 = invMsg21;
    
    // 生成源时间函数
    Vector Ft(nt);
    double amp = 1e5;  // 震源振幅，与MATLAB中一致

    for (int i = 0; i < nt; ++i) {
        double re = M_PI * freq * ((i + 1) * dt - 1.5 / freq);
        Ft(i) = amp * (1.0 - 2.0 * re * re) * exp(-re * re);
    }
    
    src.Ft = Ft;
    
    // 添加到源数组
    source.push_back(src);
    
    return source;
} 