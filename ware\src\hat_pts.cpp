#include "../include/hat_pts.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include "../include/lgwt.hpp"
#include "../include/mass_matrix.hpp"
#include "../include/eigen_wrapper.hpp"
#include "../include/bspln.hpp"
#include <cmath>
#include <iostream>

std::tuple<Vector, Vector> hat_pts(Integer N, Integer p) {

    Integer k = p + 1;
    Vector t1 = Get_Knot_Vector(N, k);
    Vector t2 = Get_Knot_Vector(N-1, k-1);

    Integer NB_intervals = N - p;
    Integer ord_gi = p; // 🔧 修复：使用 p 作为 ord_gi，与 MATLAB 一致

    Matrix xint(NB_intervals, ord_gi), wint(NB_intervals, ord_gi);
    for(Integer kd=0; kd<NB_intervals; ++kd){
        Real a = t1.at(p+kd, 0);
        Real b = t1.at(p+kd+1, 0);
        auto [xg,wg] = lgwt(ord_gi, a, b);
        for(Integer j=0;j<ord_gi;++j){
            xint(kd,j) = xg.at(j, 0);
            wint(kd,j) = wg.at(j, 0);
        }
    }

    Matrix mm11 = mass_matrix(p, N,   N,   t1, t1, xint, wint, 0);
    Matrix mm22 = mass_matrix(p, N-1, N-1, t2, t2, xint, wint, 1);

    // 🔧 修复：使用与 MATLAB 一致的矩阵平方根分解
    Matrix L11T = EigenWrapper::sqrtm(mm11);  // 对应 MATLAB sqrtm(mm11)
    Matrix L22T = EigenWrapper::sqrtm(mm22);  // 对应 MATLAB sqrtm(mm22)
    Matrix L11  = L11T.transpose();
    Matrix L22  = L22T.transpose();

    // 🔧 修复：使用与 MATLAB 一致的直接求逆，避免 SPD 特殊处理
    Matrix invL11 = EigenWrapper::inv(L11);  // 对应 MATLAB inv(L11)
    Matrix invL22 = EigenWrapper::inv(L22);  // 对应 MATLAB inv(L22)

    // Integrals of B-splines and x*B-splines
    Vector intB1(N,0.0), intxB1(N,0.0);
    for(Integer ib1=1; ib1<=N; ++ib1){
        for(Integer kd=0; kd<NB_intervals; ++kd){
            for(Integer lpt=0; lpt<ord_gi; ++lpt){
                Real b1 = bspln(t1, N, ib1, k, xint(kd,lpt));
                Real w  = wint(kd,lpt);
                intB1(ib1-1)  += b1 * w;
                intxB1(ib1-1) += b1 * xint(kd,lpt) * w;
            }
        }
    }

    Vector intB2(N-1,0.0), intxB2(N-1,0.0);
    Integer k_minus1 = k-1;
    for(Integer ib2=1; ib2<=N-1; ++ib2){
        for(Integer kd=0; kd<NB_intervals; ++kd){
            for(Integer lpt=0; lpt<ord_gi; ++lpt){
                Real b2 = bspln(t2, N-1, ib2, k_minus1, xint(kd,lpt));
                Real w  = wint(kd,lpt);
                intB2(ib2-1)  += b2 * w;
                intxB2(ib2-1) += b2 * xint(kd,lpt) * w;
            }
        }
    }

    auto matvec_lambda = [&](const Matrix &M, const Vector &v){ return EigenWrapper::matvec(M,v); };
    Vector num1 = matvec_lambda(invL11, intxB1);
    Vector den1 = matvec_lambda(invL11, intB1);

    Vector hatpoints1(N);
    // 🔧 修复：与 MATLAB 完全一致，直接除法，不添加 EPS
    for(Integer i=0;i<N;++i){ hatpoints1.at(i, 0) = num1.at(i, 0)/den1.at(i, 0); }

    Vector num2 = matvec_lambda(invL22, intxB2);
    Vector den2 = matvec_lambda(invL22, intB2);
    Vector hatpoints2(N-1);
    // 🔧 修复：与 MATLAB 完全一致，直接除法，不添加 EPS
    for(Integer i=0;i<N-1;++i){ hatpoints2.at(i, 0) = num2.at(i, 0)/den2.at(i, 0); }
    // 🔧 修复：移除强制边界设置，与 MATLAB 一致

    return {hatpoints1, hatpoints2};
} 