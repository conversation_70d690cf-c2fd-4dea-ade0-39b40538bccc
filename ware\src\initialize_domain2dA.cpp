#include "../include/initialize_domain2dA.hpp"
#include "../include/common_types.hpp"
#include <iostream>

std::vector<Domain2dA> initialize_domain2dA(std::vector<Domain2dA>& OM, int nt)
{
    std::cout << "Initializing domains with nt = " << nt << "..." << std::endl;
    
    // 第一阶段：初始化每个域的状态变量
    for(size_t iom = 0; iom < OM.size(); ++iom) {
        std::cout << "... Initialize the state of " << iom+1 << " Domain ... " << std::endl;
        
        auto &dom = OM[iom];
        int Nx1 = dom.Nx1;
        int Nz1 = dom.Nz1;

        // 增强的维度检查
        if(Nx1 <= 0 || Nz1 <= 0) {
            std::cout << "WARNING: Domain " << iom << " has invalid dimensions: Nx1="
                      << Nx1 << ", Nz1=" << Nz1 << ". Skipping initialization." << std::endl;
            continue;
        }

        int Nx2 = Nx1 - 1;
        int Nz2 = Nz1 - 1;

        // 🔧 修复关键问题：设置域结构体的 Nx2 和 Nz2
        dom.Nx2 = Nx2;
        dom.Nz2 = Nz2;

        std::cout << "  设置域 " << iom << " 网格参数: Nx1=" << Nx1 << ", Nz1=" << Nz1
                  << ", Nx2=" << Nx2 << ", Nz2=" << Nz2 << std::endl;

        // 确保Nx2和Nz2也是正数
        if(Nx2 <= 0 || Nz2 <= 0) {
            std::cout << "WARNING: Domain " << iom << " has invalid derived dimensions: Nx2="
                      << Nx2 << ", Nz2=" << Nz2 << ". Skipping initialization." << std::endl;
            continue;
        }
        
        // 加速度
        dom.state.dU2dtt12 = Matrix(Nx1, Nz2, 0.0);
        dom.state.dU2dtt21 = Matrix(Nx2, Nz1, 0.0);
        
        // 位移
        // U12
        dom.state.U12_0 = Matrix(Nx1, Nz2, 0.0);
        dom.state.U12_1 = Matrix(Nx1, Nz2, 0.0);
        dom.state.U12 = Matrix(Nx1, Nz2, 0.0);
        // U21
        dom.state.U21_0 = Matrix(Nx2, Nz1, 0.0);
        dom.state.U21_1 = Matrix(Nx2, Nz1, 0.0);
        dom.state.U21 = Matrix(Nx2, Nz1, 0.0);
        
        // 位移边界值 左 + 右
        // U12 (注意：创建行向量，与MATLAB保持一致)
        dom.state.U12mo = Vector(Nz2, 0.0);
        dom.state.U12po = Vector(Nz2, 0.0);
        dom.state.U12mo_inn = Vector(Nz2, 0.0);
        dom.state.U12po_inn = Vector(Nz2, 0.0);
        dom.state.U12mo_out = Vector(Nz2, 0.0);
        dom.state.U12po_out = Vector(Nz2, 0.0);
        
        // U21
        dom.state.U21mo = Vector(Nz1, 0.0);
        dom.state.U21po = Vector(Nz1, 0.0);
        dom.state.U21mo_inn = Vector(Nz1, 0.0);
        dom.state.U21po_inn = Vector(Nz1, 0.0);
        dom.state.U21mo_out = Vector(Nz1, 0.0);
        dom.state.U21po_out = Vector(Nz1, 0.0);
        
        // 位移边界值 下 + 上
        // U12
        dom.state.U12om = Vector(Nx1, 0.0);
        dom.state.U12op = Vector(Nx1, 0.0);
        dom.state.U12om_inn = Vector(Nx1, 0.0);
        dom.state.U12op_inn = Vector(Nx1, 0.0);
        dom.state.U12om_out = Vector(Nx1, 0.0);
        dom.state.U12op_out = Vector(Nx1, 0.0);
        
        // U21
        dom.state.U21om = Vector(Nx2, 0.0);
        dom.state.U21op = Vector(Nx2, 0.0);
        dom.state.U21om_inn = Vector(Nx2, 0.0);
        dom.state.U21op_inn = Vector(Nx2, 0.0);
        dom.state.U21om_out = Vector(Nx2, 0.0);
        dom.state.U21op_out = Vector(Nx2, 0.0);
        
        // 应力场
        // S11
        dom.state.Sxx11 = Matrix(Nx1, Nz1, 0.0);
        dom.state.Szz11 = Matrix(Nx1, Nz1, 0.0);
        // S22
        dom.state.Sxx22 = Matrix(Nx2, Nz2, 0.0);
        dom.state.Szz22 = Matrix(Nx2, Nz2, 0.0);
        
        // 应力场边界值 (之前缺失的部分)
        // 左 + 右
        dom.state.Sxx11mo = Vector(Nz1, 0.0);
        dom.state.Sxx11po = Vector(Nz1, 0.0);
        dom.state.Sxx11mo_inn = Vector(Nz1, 0.0);
        dom.state.Sxx11po_inn = Vector(Nz1, 0.0);
        dom.state.Sxx11mo_out = Vector(Nz1, 0.0);
        dom.state.Sxx11po_out = Vector(Nz1, 0.0);
        
        dom.state.Sxx11om_inn = Vector(Nx1, 0.0);
        dom.state.Sxx11op_inn = Vector(Nx1, 0.0);
        
        // 左 + 右 (S22)
        dom.state.Sxx22mo = Vector(Nz2, 0.0);
        dom.state.Sxx22po = Vector(Nz2, 0.0);
        dom.state.Sxx22mo_inn = Vector(Nz2, 0.0);
        dom.state.Sxx22po_inn = Vector(Nz2, 0.0);
        dom.state.Sxx22mo_out = Vector(Nz2, 0.0);
        dom.state.Sxx22po_out = Vector(Nz2, 0.0);
        
        dom.state.Sxx22om_inn = Vector(Nx2, 0.0);
        dom.state.Sxx22op_inn = Vector(Nx2, 0.0);
        
        // 应力场边界值 下 + 上
        dom.state.Szz11om = Vector(Nx1, 0.0);
        dom.state.Szz11op = Vector(Nx1, 0.0);
        dom.state.Szz11om_inn = Vector(Nx1, 0.0);
        dom.state.Szz11op_inn = Vector(Nx1, 0.0);
        dom.state.Szz11om_out = Vector(Nx1, 0.0);
        dom.state.Szz11op_out = Vector(Nx1, 0.0);
        
        dom.state.Szz11mo_inn = Vector(Nz1, 0.0);
        dom.state.Szz11po_inn = Vector(Nz1, 0.0);
        
        // 下 + 上 (S22)
        dom.state.Szz22om = Vector(Nx2, 0.0);
        dom.state.Szz22op = Vector(Nx2, 0.0);
        dom.state.Szz22om_inn = Vector(Nx2, 0.0);
        dom.state.Szz22op_inn = Vector(Nx2, 0.0);
        dom.state.Szz22om_out = Vector(Nx2, 0.0);
        dom.state.Szz22op_out = Vector(Nx2, 0.0);
        
        dom.state.Szz22mo_inn = Vector(Nz2, 0.0);
        dom.state.Szz22po_inn = Vector(Nz2, 0.0);
        
        // Umid 存储 - 根据 nt 分配 3D 数组
        // 在 C++ 中使用 2D 矩阵向量表示 3D 数组
        if(nt > 0) {
            // 创建 nt 个时间步长的存储，与MATLAB的floor(nt)保持一致
            int nt_floor = static_cast<int>(std::floor(static_cast<double>(nt)));
            std::cout << "  Allocating Umid storage for " << nt_floor << " timesteps" << std::endl;

            dom.state.Umid.clear();
            dom.state.Umid.reserve(nt_floor);
            for (int t = 0; t < nt_floor; ++t) {
                dom.state.Umid.push_back(Matrix(Nx1, Nz1, 0.0));
            }
        }
        
        // ------------------------------------------------------------------
        // Material property matrices & geometric Jacobian placeholders
        // ------------------------------------------------------------------
        
        double rho_val = dom.rho;         // scalar density (default 2000)
        double mu_val  = dom.mu;          // scalar shear modulus (default set)
        
        dom.rho12 = Matrix(Nx1, Nz2, rho_val);
        dom.rho21 = Matrix(Nx2, Nz1, rho_val);
        
        dom.mu11  = Matrix(Nx1, Nz1, mu_val);
        dom.mu22  = Matrix(Nx2, Nz2, mu_val);
        
        // 🔧 修复关键问题：不要在这里初始化雅可比矩阵！
        // refine_model2dA 会正确计算这些矩阵，这里初始化为单位矩阵会覆盖正确的计算结果
        //
        // 注释掉错误的初始化代码：
        // dom.Jac11 = Matrix(Nx1, Nz1, 1.0);  // ❌ 这会覆盖 refine_model2dA 的正确计算
        // dom.dxpdx11 = Matrix(Nx1, Nz1, 1.0); // ❌ 这会覆盖 refine_model2dA 的正确计算

        std::cout << "  跳过雅可比矩阵初始化，等待 refine_model2dA 正确计算" << std::endl;
    }
    
    // 第二阶段：设置边界耦合 alpha 值 (在MATLAB中第117-153行)
    for(size_t iom = 0; iom < OM.size(); ++iom) {
        std::cout << "... set the alpha values of " << iom+1 << " Domain ... " << std::endl;
        
        auto &dom = OM[iom];
        
        // 检查邻域，设置相应的alpha值
        int iNbr_mo = dom.iNbr_mo;
        int iNbr_po = dom.iNbr_po;
        int iNbr_om = dom.iNbr_om;
        int iNbr_op = dom.iNbr_op;
        
        // 左邻域
        if(iNbr_mo == 0) {
            dom.alpha_mo = 0.0;
        } else {
            dom.alpha_mo = 0.5;
        }
        
        // 右邻域
        if(iNbr_po == 0) {
            dom.alpha_po = 0.0;
        } else {
            dom.alpha_po = 0.5;
        }
        
        // 下邻域
        if(iNbr_om == 0) {
            dom.alpha_om = 0.0;
        } else {
            dom.alpha_om = 0.5;
        }
        
        // 上邻域
        if(iNbr_op == 0) {
            dom.alpha_op = 0.0;
        } else {
            dom.alpha_op = 0.5;
        }
    }
    
    return OM;
} 