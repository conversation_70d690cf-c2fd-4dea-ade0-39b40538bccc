#include "../include/lgwt.hpp"
#include <cmath>
#include <tuple>
#include <algorithm>

// Helper: maximum absolute difference between two vectors
static Real max_abs_diff(const Vector &a, const Vector &b)
{
    Real m = 0.0;
    Integer n = std::min(a.size(), b.size());
    for (Integer i = 0; i < n; ++i)
    {
        m = std::max(m, std::abs(a[i] - b[i]));
    }
    return m;
}

std::tuple<Vector, Vector> lgwt(int n, double a, double b)
{
    if (n <= 0)
    {
        return {Vector(0), Vector(0)};
    }

    // Convert to MATLAB-style N (order-1)
    Integer N = n - 1;
    Integer N1 = N + 1;
    Integer N2 = N + 2;

    // Initial abscissae (xu) equally spaced in [-1,1]
    Vector xu = linspace(-1.0, 1.0, N1);

    // Initial guess for roots (equation from lgwt.m)
    Vector y(N1);
    for (Integer i = 0; i < N1; ++i)
    {
        Real cos_term = std::cos((2.0 * (i) + 1.0) * PI / (2.0 * N + 2.0));
        Real sin_term = std::sin(PI * xu[i] * N / N2);
        y[i] = cos_term + (0.27 / N1) * sin_term;
    }

    // Legendre–Gauss Vandermonde matrix and derivative
    Matrix L(N1, N2);
    Vector Lp(N1);

    Vector y0(N1, 2.0); // ensure loop enters

    // 🔧 修复：使用与 MATLAB 完全一致的收敛条件
    const Real tol = 2.2204e-16;  // MATLAB eps
    int max_iter = 100;
    int iter = 0;
    while (max_abs_diff(y, y0) > tol && iter < max_iter)
    {
        y0 = y;

        // Compute the Legendre-Gauss Vandermonde matrix
        for (Integer i = 0; i < N1; ++i)
        {
            L(i, 0) = 1.0;
            L(i, 1) = y[i];
        }
        for (Integer k = 2; k < N2; ++k)
        {
            for (Integer i = 0; i < N1; ++i)
            {
                L(i, k) = ((2.0 * k - 1.0) * y[i] * L(i, k - 1) - (k - 1.0) * L(i, k - 2)) / k;
            }
        }

        // Derivative
        for (Integer i = 0; i < N1; ++i)
        {
            Lp[i] = N2 * (L(i, N1 - 1) - y[i] * L(i, N2 - 1)) / (1.0 - y[i] * y[i]);
        }

        // Newton step
        for (Integer i = 0; i < N1; ++i)
        {
            y[i] = y[i] - L(i, N2 - 1) / Lp[i];
        }
        ++iter;
    }

    // Linear map from [-1,1] to [a,b]
    Vector x(N1);
    Vector w(N1);
    for (Integer i = 0; i < N1; ++i)
    {
        x[i] = (a * (1.0 - y[i]) + b * (1.0 + y[i])) / 2.0;
        Real denom = (1.0 - y[i] * y[i]) * Lp[i] * Lp[i];
        Real weight = (b - a) / denom * (static_cast<Real>(N2) / N1) * (static_cast<Real>(N2) / N1);
        w[i] = weight;
    }

    // Flip order (ascending x)
    for (Integer i = 0; i < N1 / 2; ++i)
    {
        std::swap(x[i], x[N1 - 1 - i]);
        std::swap(w[i], w[N1 - 1 - i]);
    }

    return {x, w};
} 