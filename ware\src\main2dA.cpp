#include "../include/mesh_sphere2dA.hpp"
#include "../include/find_connections2dA.hpp"
#include "../include/refine_model2dA.hpp"
#include "../include/create_source2d.hpp"
#include "../include/get_source2dA.hpp"
#include "../include/get_receiver2dA.hpp"
#include "../include/compute_dt2dA.hpp"
#include "../include/add_source2dA.hpp"
#include "../include/gen_DFDMatrices2dA.hpp"
#include "../include/initialize_domain2dA.hpp"
#include "../include/compute_KU2dA.hpp"
#include "../include/compute_KS2dA.hpp"
#include "../include/compute_boundary_Uvalue_inn2dA.hpp"
#include "../include/compute_boundary_Uvalue_out2dA.hpp"
#include "../include/compute_boundary_Svalue_inn2dA.hpp"
#include "../include/compute_boundary_Svalue_out2dA.hpp"
#include "../include/update_wavefields2dA.hpp"
#include "../include/check_stability2dA.hpp"
#include "../include/solver2dA.hpp"
#include "../include/plot_functions.hpp"
#include "../include/Get_Knot_Vector.hpp"
#include <iostream>
#include <iomanip>
#include <vector>
#include <algorithm>
#include <fstream>
#include <filesystem>
#include "../include/lgwt.hpp"
#include "../include/common_types.hpp"
#include "../include/debug_utils.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <tuple>    // For std::tuple and std::get
#include <iomanip>  // For setprecision




// Helper function: Print matrix data to terminal AND save to file
void print_matrix_data(const Matrix& mat, const std::string& filepath) {
    // 🔧 修复：创建目录并写入文件
    std::filesystem::create_directories(std::filesystem::path(filepath).parent_path());

    // 输出到控制台（简化版）
    std::cout << "保存矩阵到: " << filepath << " (" << mat.rows << "x" << mat.cols << ")" << std::endl;

    // 🔧 修复：同时写入文件
    std::ofstream file(filepath);
    if (file.is_open()) {
        for (Integer i = 0; i < mat.rows; ++i) {
            for (Integer j = 0; j < mat.cols; ++j) {
                file << std::scientific << std::setprecision(16) << mat(i, j);
                if (j < mat.cols - 1) file << " ";
            }
            file << std::endl;
        }
        file.close();
        std::cout << "✅ 矩阵文件已保存" << std::endl;
    } else {
        std::cout << "❌ 无法创建矩阵文件: " << filepath << std::endl;
    }
}

// Helper function: Print vector data to terminal AND save to file
void print_vector_data(const Vector& vec, const std::string& filepath) {
    // 🔧 修复：创建目录并写入文件
    std::filesystem::create_directories(std::filesystem::path(filepath).parent_path());

    // 输出到控制台（简化版）
    std::cout << "保存向量到: " << filepath << " (" << vec.size() << " 元素)" << std::endl;

    // 🔧 修复：同时写入文件
    std::ofstream file(filepath);
    if (file.is_open()) {
        for (Integer i = 0; i < vec.size(); ++i) {
            file << std::scientific << std::setprecision(16) << vec(i) << std::endl;
        }
        file.close();
        std::cout << "✅ 向量文件已保存" << std::endl;
    } else {
        std::cout << "❌ 无法创建向量文件: " << filepath << std::endl;
    }
}

// Helper function: Print domain parameters to terminal AND save to file
void print_domain_parameters(const Domain2dA& dom, const std::string& filepath) {
    // 🔧 修复：创建目录并写入文件
    std::filesystem::create_directories(std::filesystem::path(filepath).parent_path());

    // 输出到控制台
    std::cout << "=== 保存参数到: " << filepath << " ===" << std::endl;
    std::cout << "iom = " << dom.iom << std::endl;
    std::cout << "region = " << dom.region << std::endl;
    std::cout << "x_min = " << std::scientific << std::setprecision(16) << dom.x_min << std::endl;
    std::cout << "x_max = " << std::scientific << std::setprecision(16) << dom.x_max << std::endl;
    std::cout << "z_min = " << std::scientific << std::setprecision(16) << dom.z_min << std::endl;
    std::cout << "z_max = " << std::scientific << std::setprecision(16) << dom.z_max << std::endl;
    std::cout << "Nx1 = " << dom.Nx1 << std::endl;
    std::cout << "Nz1 = " << dom.Nz1 << std::endl;
    std::cout << "Nx2 = " << dom.Nx2 << std::endl;
    std::cout << "Nz2 = " << dom.Nz2 << std::endl;
    std::cout << "px1 = " << dom.px1 << std::endl;
    std::cout << "pz1 = " << dom.pz1 << std::endl;
    std::cout << "mu = " << std::scientific << std::setprecision(16) << dom.mu << std::endl;
    std::cout << "rho = " << std::scientific << std::setprecision(16) << dom.rho << std::endl;
    std::cout << "iNbr_mo = " << dom.iNbr_mo << std::endl;
    std::cout << "iNbr_po = " << dom.iNbr_po << std::endl;
    std::cout << "iNbr_om = " << dom.iNbr_om << std::endl;
    std::cout << "iNbr_op = " << dom.iNbr_op << std::endl;

    // 🔧 修复：同时写入文件
    std::ofstream file(filepath);
    if (file.is_open()) {
        file << "# C++ Domain Parameters" << std::endl;
        file << "iom = " << dom.iom << std::endl;
        file << "region = " << dom.region << std::endl;
        file << "x_min = " << std::scientific << std::setprecision(16) << dom.x_min << std::endl;
        file << "x_max = " << std::scientific << std::setprecision(16) << dom.x_max << std::endl;
        file << "z_min = " << std::scientific << std::setprecision(16) << dom.z_min << std::endl;
        file << "z_max = " << std::scientific << std::setprecision(16) << dom.z_max << std::endl;
        file << "Nx1 = " << dom.Nx1 << std::endl;
        file << "Nz1 = " << dom.Nz1 << std::endl;
        file << "Nx2 = " << dom.Nx2 << std::endl;
        file << "Nz2 = " << dom.Nz2 << std::endl;
        file << "px1 = " << dom.px1 << std::endl;
        file << "pz1 = " << dom.pz1 << std::endl;
        file << "mu = " << std::scientific << std::setprecision(16) << dom.mu << std::endl;
        file << "rho = " << std::scientific << std::setprecision(16) << dom.rho << std::endl;
        file << "iNbr_mo = " << dom.iNbr_mo << std::endl;
        file << "iNbr_po = " << dom.iNbr_po << std::endl;
        file << "iNbr_om = " << dom.iNbr_om << std::endl;
        file << "iNbr_op = " << dom.iNbr_op << std::endl;
        file.close();
        std::cout << "✅ 参数文件已保存" << std::endl;
    } else {
        std::cout << "❌ 无法创建参数文件: " << filepath << std::endl;
    }
    std::cout << std::endl;
}

int main(){
    try {
        // 使用与MATLAB完全一致的参数设置
        Vector rad(2);
        rad(0) = 600.0*1000;  // 600 km -> 600000 m
        rad(1) = 1221.5*1000; // 1221.5 km -> 1221500 m
        Real dx = 10000.0;     // 网格间距 (单位: 米)
        Integer nparts = 2;    // 分区数量
        Real freq = 0.01;      // 频率 (Hz)
        Real ppw = 3.5;        // 每波长点数 (Points per wavelength)
        Real duration = 480.0; // 模拟持续时间 (秒)

        // 其他重要参数
        Real vp = 2750.0;      // P波速度 (m/s)
        Real vs = 0.0;         // S波速度 (m/s)
        Real rho = 2000.0;     // 密度 (kg/m³)
        Real mu = rho*vp*vp;   // 剪切模量
        Real CFL_condition = 0.1;  // CFL稳定性条件
        Real Vmax = 5000.0;    // 最大波速 (m/s)
        Integer p = 5;         // 多项式阶数
        Real fmax = freq*3.0;  // 最大频率
        Integer nmin = 3;      // 最小网格点数
        Real inflat = 0.44;    // 中心方形膨胀系数

        auto OM = mesh_sphere2dA(rad, dx, nparts, freq, ppw);
        OM = find_connections2dA(OM);

        // 🔧 修复执行顺序：先计算 dt/nt，然后初始化域，最后精化模型
        auto [dt, nt] = compute_dt2dA(OM, duration);
        OM = initialize_domain2dA(OM, nt);  // 移到 refine_model2dA 之前

        OM = refine_model2dA(OM);           // 这会正确计算雅可比矩阵
        gen_DFDMatrices2dA(OM);
        std::vector<SourceStruct> sources = get_source2dA(OM, freq, dt, nt);
        std::vector<Receiver2dA> receivers = get_receiver2dA(OM, dt, nt);
        auto result = solver2dA(OM, sources, receivers, dt, nt);
        OM = result.first;
        receivers = result.second;

        // Output domain 0 data to terminal in the specified format
        if (!OM.empty()) {
            const auto& dom = OM[0];

            // 🔍 添加详细调试信息
            std::cout << "\n=== 域 0 最终状态调试信息 ===" << std::endl;
            std::cout << "Nx1=" << dom.Nx1 << ", Nz1=" << dom.Nz1 << std::endl;
            std::cout << "Nx2=" << dom.Nx2 << ", Nz2=" << dom.Nz2 << std::endl;
            std::cout << "域边界: x=[" << dom.x_min << ", " << dom.x_max
                      << "], z=[" << dom.z_min << ", " << dom.z_max << "]" << std::endl;

            // 检查关键矩阵
            if (dom.dxpdx11.rows > 0 && dom.dxpdx11.cols > 0) {
                // 计算矩阵的最小值和最大值
                Real dxpdx_min = *std::min_element(dom.dxpdx11.data.begin(), dom.dxpdx11.data.end());
                Real dxpdx_max = *std::max_element(dom.dxpdx11.data.begin(), dom.dxpdx11.data.end());
                std::cout << "dxpdx11 矩阵: " << dom.dxpdx11.rows << "x" << dom.dxpdx11.cols
                          << ", 值范围: [" << dxpdx_min << ", " << dxpdx_max << "]" << std::endl;
            } else {
                std::cout << "dxpdx11 矩阵: 空" << std::endl;
            }

            if (dom.x2d11.rows > 0 && dom.x2d11.cols > 0) {
                // 计算矩阵的最小值和最大值
                Real x_min = *std::min_element(dom.x2d11.data.begin(), dom.x2d11.data.end());
                Real x_max = *std::max_element(dom.x2d11.data.begin(), dom.x2d11.data.end());
                std::cout << "x2d11 矩阵: " << dom.x2d11.rows << "x" << dom.x2d11.cols
                          << ", 值范围: [" << x_min << ", " << x_max << "]" << std::endl;
            } else {
                std::cout << "x2d11 矩阵: 空" << std::endl;
            }
            std::cout << "================================\n" << std::endl;

            // Print domain parameters
            print_domain_parameters(dom, "D:\\project\\ware\\output_cpp\\domain_0\\parameters.txt");

            // Print basis function matrices
            if (dom.bxT1.rows > 0 && dom.bxT1.cols > 0) {
                print_matrix_data(dom.bxT1, "D:\\project\\ware\\output_cpp\\domain_0\\bxT1.txt");
            }
            if (dom.bxT2.rows > 0 && dom.bxT2.cols > 0) {
                print_matrix_data(dom.bxT2, "D:\\project\\ware\\output_cpp\\domain_0\\bxT2.txt");
            }
            if (dom.bzT1.rows > 0 && dom.bzT1.cols > 0) {
                print_matrix_data(dom.bzT1, "D:\\project\\ware\\output_cpp\\domain_0\\bzT1.txt");
            }
            if (dom.bzT2.rows > 0 && dom.bzT2.cols > 0) {
                print_matrix_data(dom.bzT2, "D:\\project\\ware\\output_cpp\\domain_0\\bzT2.txt");
            }

            // Print coordinate transformation matrices
            std::cout << "\n=== 雅可比矩阵检查 ===" << std::endl;
            if (dom.dxpdx11.rows > 0 && dom.dxpdx11.cols > 0) {
                print_matrix_data(dom.dxpdx11, "D:\\project\\ware\\output_cpp\\domain_0\\dxpdx11.txt");
            } else {
                std::cout << "❌ dxpdx11 矩阵为空 (" << dom.dxpdx11.rows << "x" << dom.dxpdx11.cols << ")" << std::endl;
            }
            if (dom.dxpdx22.rows > 0 && dom.dxpdx22.cols > 0) {
                print_matrix_data(dom.dxpdx22, "D:\\project\\ware\\output_cpp\\domain_0\\dxpdx22.txt");
            } else {
                std::cout << "❌ dxpdx22 矩阵为空 (" << dom.dxpdx22.rows << "x" << dom.dxpdx22.cols << ")" << std::endl;
            }
            if (dom.dxpdz11.rows > 0 && dom.dxpdz11.cols > 0) {
                print_matrix_data(dom.dxpdz11, "D:\\project\\ware\\output_cpp\\domain_0\\dxpdz11.txt");
            } else {
                std::cout << "❌ dxpdz11 矩阵为空 (" << dom.dxpdz11.rows << "x" << dom.dxpdz11.cols << ")" << std::endl;
            }
            if (dom.dxpdz22.rows > 0 && dom.dxpdz22.cols > 0) {
                print_matrix_data(dom.dxpdz22, "D:\\project\\ware\\output_cpp\\domain_0\\dxpdz22.txt");
            } else {
                std::cout << "❌ dxpdz22 矩阵为空 (" << dom.dxpdz22.rows << "x" << dom.dxpdz22.cols << ")" << std::endl;
            }
            if (dom.dzpdx11.rows > 0 && dom.dzpdx11.cols > 0) {
                print_matrix_data(dom.dzpdx11, "D:\\project\\ware\\output_cpp\\domain_0\\dzpdx11.txt");
            } else {
                std::cout << "❌ dzpdx11 矩阵为空 (" << dom.dzpdx11.rows << "x" << dom.dzpdx11.cols << ")" << std::endl;
            }
            if (dom.dzpdx22.rows > 0 && dom.dzpdx22.cols > 0) {
                print_matrix_data(dom.dzpdx22, "D:\\project\\ware\\output_cpp\\domain_0\\dzpdx22.txt");
            } else {
                std::cout << "❌ dzpdx22 矩阵为空 (" << dom.dzpdx22.rows << "x" << dom.dzpdx22.cols << ")" << std::endl;
            }
            if (dom.dzpdz11.rows > 0 && dom.dzpdz11.cols > 0) {
                print_matrix_data(dom.dzpdz11, "D:\\project\\ware\\output_cpp\\domain_0\\dzpdz11.txt");
            } else {
                std::cout << "❌ dzpdz11 矩阵为空 (" << dom.dzpdz11.rows << "x" << dom.dzpdz11.cols << ")" << std::endl;
            }
            if (dom.dzpdz22.rows > 0 && dom.dzpdz22.cols > 0) {
                print_matrix_data(dom.dzpdz22, "D:\\project\\ware\\output_cpp\\domain_0\\dzpdz22.txt");
            } else {
                std::cout << "❌ dzpdz22 矩阵为空 (" << dom.dzpdz22.rows << "x" << dom.dzpdz22.cols << ")" << std::endl;
            }

            // 注意：中间变量 dxdxp11_hat, dzdzp22_hat, Jac22_hat, hatpoints1, hatpoints2
            // 是 refine_model2dA 函数中的局部变量，不在 Domain2dA 结构体中
            std::cout << "\n=== 雅可比矩阵调试完成 ===" << std::endl;

            // Print inverse L matrices
            if (dom.invLx11.rows > 0 && dom.invLx11.cols > 0) {
                print_matrix_data(dom.invLx11, "D:\\project\\ware\\output_cpp\\domain_0\\invLx11.txt");
            }
            if (dom.invLx22.rows > 0 && dom.invLx22.cols > 0) {
                print_matrix_data(dom.invLx22, "D:\\project\\ware\\output_cpp\\domain_0\\invLx22.txt");
            }
            if (dom.invLxT11.rows > 0 && dom.invLxT11.cols > 0) {
                print_matrix_data(dom.invLxT11, "D:\\project\\ware\\output_cpp\\domain_0\\invLxT11.txt");
            }
            if (dom.invLxT22.rows > 0 && dom.invLxT22.cols > 0) {
                print_matrix_data(dom.invLxT22, "D:\\project\\ware\\output_cpp\\domain_0\\invLxT22.txt");
            }
            if (dom.invLz11.rows > 0 && dom.invLz11.cols > 0) {
                print_matrix_data(dom.invLz11, "D:\\project\\ware\\output_cpp\\domain_0\\invLz11.txt");
            }
            if (dom.invLz22.rows > 0 && dom.invLz22.cols > 0) {
                print_matrix_data(dom.invLz22, "D:\\project\\ware\\output_cpp\\domain_0\\invLz22.txt");
            }
            if (dom.invLzT11.rows > 0 && dom.invLzT11.cols > 0) {
                print_matrix_data(dom.invLzT11, "D:\\project\\ware\\output_cpp\\domain_0\\invLzT11.txt");
            }
            if (dom.invLzT22.rows > 0 && dom.invLzT22.cols > 0) {
                print_matrix_data(dom.invLzT22, "D:\\project\\ware\\output_cpp\\domain_0\\invLzT22.txt");
            }

            // Print Jacobian matrices
            if (dom.Jac11.rows > 0 && dom.Jac11.cols > 0) {
                print_matrix_data(dom.Jac11, "D:\\project\\ware\\output_cpp\\domain_0\\Jac11.txt");
            }
            if (dom.Jac22.rows > 0 && dom.Jac22.cols > 0) {
                print_matrix_data(dom.Jac22, "D:\\project\\ware\\output_cpp\\domain_0\\Jac22.txt");
            }

            // Print DFD matrices
            if (dom.kkx12.rows > 0 && dom.kkx12.cols > 0) {
                print_matrix_data(dom.kkx12, "D:\\project\\ware\\output_cpp\\domain_0\\kkx12.txt");
            }
            if (dom.kkx21.rows > 0 && dom.kkx21.cols > 0) {
                print_matrix_data(dom.kkx21, "D:\\project\\ware\\output_cpp\\domain_0\\kkx21.txt");
            }
            if (dom.kkz12.rows > 0 && dom.kkz12.cols > 0) {
                print_matrix_data(dom.kkz12, "D:\\project\\ware\\output_cpp\\domain_0\\kkz12.txt");
            }
            if (dom.kkz21.rows > 0 && dom.kkz21.cols > 0) {
                print_matrix_data(dom.kkz21, "D:\\project\\ware\\output_cpp\\domain_0\\kkz21.txt");
            }

            // Print material property matrices
            if (dom.mu11.rows > 0 && dom.mu11.cols > 0) {
                print_matrix_data(dom.mu11, "D:\\project\\ware\\output_cpp\\domain_0\\mu11.txt");
            }
            if (dom.mu22.rows > 0 && dom.mu22.cols > 0) {
                print_matrix_data(dom.mu22, "D:\\project\\ware\\output_cpp\\domain_0\\mu22.txt");
            }

            // Print state matrices
            if (dom.state.Sxx11.rows > 0 && dom.state.Sxx11.cols > 0) {
                print_matrix_data(dom.state.Sxx11, "D:\\project\\ware\\output_cpp\\domain_0\\state_Sxx11.txt");
            }
            if (dom.state.Sxx22.rows > 0 && dom.state.Sxx22.cols > 0) {
                print_matrix_data(dom.state.Sxx22, "D:\\project\\ware\\output_cpp\\domain_0\\state_Sxx22.txt");
            }
            if (dom.state.Szz11.rows > 0 && dom.state.Szz11.cols > 0) {
                print_matrix_data(dom.state.Szz11, "D:\\project\\ware\\output_cpp\\domain_0\\state_Szz11.txt");
            }
            if (dom.state.Szz22.rows > 0 && dom.state.Szz22.cols > 0) {
                print_matrix_data(dom.state.Szz22, "D:\\project\\ware\\output_cpp\\domain_0\\state_Szz22.txt");
            }
            if (dom.state.U12.rows > 0 && dom.state.U12.cols > 0) {
                print_matrix_data(dom.state.U12, "D:\\project\\ware\\output_cpp\\domain_0\\state_U12.txt");
            }
            if (dom.state.U21.rows > 0 && dom.state.U21.cols > 0) {
                print_matrix_data(dom.state.U21, "D:\\project\\ware\\output_cpp\\domain_0\\state_U21.txt");
            }

            // Print boundary vectors
            if (dom.state.U12mo.size() > 0) {
                print_vector_data(dom.state.U12mo, "D:\\project\\ware\\output_cpp\\domain_0\\state_U12mo.txt");
            }
            if (dom.state.U12om.size() > 0) {
                print_vector_data(dom.state.U12om, "D:\\project\\ware\\output_cpp\\domain_0\\state_U12om.txt");
            }
            if (dom.state.U12op.size() > 0) {
                print_vector_data(dom.state.U12op, "D:\\project\\ware\\output_cpp\\domain_0\\state_U12op.txt");
            }
            if (dom.state.U12po.size() > 0) {
                print_vector_data(dom.state.U12po, "D:\\project\\ware\\output_cpp\\domain_0\\state_U12po.txt");
            }
            if (dom.state.U21mo.size() > 0) {
                print_vector_data(dom.state.U21mo, "D:\\project\\ware\\output_cpp\\domain_0\\state_U21mo.txt");
            }
            if (dom.state.U21om.size() > 0) {
                print_vector_data(dom.state.U21om, "D:\\project\\ware\\output_cpp\\domain_0\\state_U21om.txt");
            }
            if (dom.state.U21op.size() > 0) {
                print_vector_data(dom.state.U21op, "D:\\project\\ware\\output_cpp\\domain_0\\state_U21op.txt");
            }
            if (dom.state.U21po.size() > 0) {
                print_vector_data(dom.state.U21po, "D:\\project\\ware\\output_cpp\\domain_0\\state_U21po.txt");
            }
        }

        return 0;

    } catch(const std::exception& ex){
        return -1;
    } catch(...) {
        return -2;
    }
}