#include "../include/mass_matrix.hpp"
#include "../include/bspln.hpp"
#include "../include/dbspln.hpp"

// Numerical mass (or stiffness) matrix assembly using 1-D Gauss nodes/weights
// der=0 → ∫ B_i B_j ; der=1 → ∫ B'_i B'_j  etc.

Matrix mass_matrix(int p,
                   int N1, int N2,
                   const Vector& t1, const Vector& t2,
                   const Matrix& xint, const Matrix& wint,
                   int der)
{
    int NB_intervals = xint.rows; // rows correspond to element intervals
    int nGauss       = xint.cols;

    Matrix M(N1, N2, 0.0);

#ifdef USE_EIGEN
    // Convert to Eigen for faster outer-product accumulation
    Eigen::MatrixXd MEig = Eigen::MatrixXd::Zero(N1, N2);

    Eigen::VectorXd bi(N1);
    Eigen::VectorXd bj(N2);

    for(int kd = 0; kd < NB_intervals; ++kd){
        for(int g = 0; g < nGauss; ++g){
            double x  = xint(kd, g);
            double w  = wint(kd, g);

            // 🔧 修复：回到原始的全矩阵计算，避免复杂的部分矩阵逻辑
            // MATLAB: if option==0 then k=p+1; elseif option==1 then k=p
            int k = (der == 0) ? (p + 1) : p;

            // 计算全部基函数
            for(int i = 1; i <= N1; ++i){
                bi(i-1) = (der==0) ? bspln(t1, N1, i, k, x)
                                   : dbspln(t1, N1, i, k, x, der);
            }
            for(int j = 1; j <= N2; ++j){
                bj(j-1) = (der==0) ? bspln(t2, N2, j, k, x)
                                   : dbspln(t2, N2, j, k, x, der);
            }

            MEig += w * bi * bj.transpose(); // rank-1 outer product
        }
    }

    // Convert back to custom Matrix
    for(int i=0;i<N1;++i)
        for(int j=0;j<N2;++j)
            M(i,j) = MEig(i,j);
#else
    // Fallback scalar implementation
    // MATLAB: if option==0 then k=p+1; elseif option==1 then k=p
    int k = (der == 0) ? (p + 1) : p;
    for(int kd = 0; kd < NB_intervals; ++kd){
        for(int g = 0; g < nGauss; ++g){
            double x  = xint(kd, g);
            double w  = wint(kd, g);

            // 🔧 修复：回到全矩阵计算，避免复杂的部分矩阵逻辑
            for(int i=1;i<=N1;++i){
                double bi = (der==0) ? bspln(t1, N1, i, k, x)
                                   : dbspln(t1, N1, i, k, x, der);
                for(int j=1;j<=N2;++j){
                    double bj = (der==0) ? bspln(t2, N2, j, k, x)
                                       : dbspln(t2, N2, j, k, x, der);
                    M(i-1,j-1) += bi * bj * w;
                }
            }
        }
    }
#endif

    return M;
} 