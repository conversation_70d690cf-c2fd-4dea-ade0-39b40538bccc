/**
 * @file memory_safe_matrix.cpp
 * @brief 内存安全矩阵操作的实现
 */

#include "../include/memory_safe_matrix.hpp"
#include "../include/setup_basis.hpp"  // 添加Basis类型定义
#include <mutex>
#include <iostream>

namespace MemorySafe {

// 全局矩阵池实例
MatrixPool g_matrix_pool;

// 内存分析器静态成员初始化
size_t MemoryProfiler::peak_memory_usage = 0;
size_t MemoryProfiler::current_memory_usage = 0;

} // namespace MemorySafe

// 为Matrix类添加内存安全的工厂函数
namespace MatrixFactory {

/**
 * @brief 创建安全的零矩阵
 */
Matrix create_safe_zeros(Integer rows, Integer cols) {
    if (rows < 0 || cols < 0) {
        throw std::invalid_argument("Matrix dimensions must be non-negative");
    }
    
    try {
        return Matrix(rows, cols, 0.0);
    } catch (const std::bad_alloc& e) {
        throw std::runtime_error("Failed to allocate memory for " + 
                               std::to_string(rows) + "x" + std::to_string(cols) + " matrix");
    }
}

/**
 * @brief 创建安全的单位矩阵
 */
Matrix create_safe_identity(Integer n) {
    if (n < 0) {
        throw std::invalid_argument("Matrix size must be non-negative");
    }
    
    try {
        Matrix result(n, n, 0.0);
        for (Integer i = 0; i < n; ++i) {
            result.at(i, i) = 1.0;
        }
        return result;
    } catch (const std::bad_alloc& e) {
        throw std::runtime_error("Failed to allocate memory for " + 
                               std::to_string(n) + "x" + std::to_string(n) + " identity matrix");
    }
}

/**
 * @brief 创建安全的随机矩阵
 */
Matrix create_safe_random(Integer rows, Integer cols, Real min_val = 0.0, Real max_val = 1.0) {
    if (rows < 0 || cols < 0) {
        throw std::invalid_argument("Matrix dimensions must be non-negative");
    }
    
    try {
        Matrix result(rows, cols);
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<Real> dis(min_val, max_val);
        
        for (Integer i = 0; i < rows; ++i) {
            for (Integer j = 0; j < cols; ++j) {
                result.at(i, j) = dis(gen);
            }
        }
        return result;
    } catch (const std::bad_alloc& e) {
        throw std::runtime_error("Failed to allocate memory for random matrix");
    }
}

} // namespace MatrixFactory

// 为Vector类添加内存安全的工厂函数
namespace VectorFactory {

/**
 * @brief 创建安全的零向量
 */
Vector create_safe_zeros(Integer size) {
    if (size < 0) {
        throw std::invalid_argument("Vector size must be non-negative");
    }
    
    try {
        return Vector(size, 0.0);
    } catch (const std::bad_alloc& e) {
        throw std::runtime_error("Failed to allocate memory for vector of size " + 
                               std::to_string(size));
    }
}

/**
 * @brief 创建安全的线性空间向量
 */
Vector create_safe_linspace(Real start, Real end, Integer num) {
    if (num < 0) {
        throw std::invalid_argument("Number of points must be non-negative");
    }
    if (num == 0) {
        return Vector(0);
    }
    if (num == 1) {
        Vector result(1);
        result.at(0, 0) = start;
        return result;
    }
    
    try {
        Vector result(num);
        Real step = (end - start) / (num - 1);
        for (Integer i = 0; i < num; ++i) {
            result.at(i, 0) = start + i * step;
        }
        return result;
    } catch (const std::bad_alloc& e) {
        throw std::runtime_error("Failed to allocate memory for linspace vector");
    }
}

} // namespace VectorFactory

// 内存安全的MATLAB函数实现
namespace SafeMATLAB {

/**
 * @brief 安全的inner_product2实现
 */
Matrix safe_inner_product2(const Basis& basis1, const Basis& basis2) {
    try {
        MemorySafe::MatrixManager manager;
        
        Integer N1 = basis1.nb;
        Integer M2 = basis2.nb;
        Integer pN1 = basis1.pb;
        Integer pM2 = basis2.pb;
        
        // 验证输入参数
        if (N1 <= 0 || M2 <= 0 || pN1 < 0 || pM2 < 0) {
            throw std::invalid_argument("Invalid basis parameters");
        }
        
        // 创建结果矩阵
        Matrix* result = manager.create_matrix(N1, M2);
        
        // 计算内积（简化版本，实际实现需要更复杂的数值积分）
        for (Integer i = 0; i < N1; ++i) {
            for (Integer j = 0; j < M2; ++j) {
                // 这里应该是实际的B样条内积计算
                // 为了演示，使用简化计算
                Real value = (i == j) ? 1.0 : 0.0;
                result->at(i, j) = value;
            }
        }
        
        return *result;
    } catch (...) {
        throw std::runtime_error("Failed to compute inner_product2");
    }
}

/**
 * @brief 安全的矩阵求逆
 */
Matrix safe_matrix_inverse(const Matrix& A) {
    if (A.rows != A.cols) {
        throw std::invalid_argument("Matrix must be square for inversion");
    }
    
    try {
        MemorySafe::MatrixManager manager;
        
        Integer n = A.rows;
        Matrix* result = manager.create_matrix(n, n);
        Matrix* temp = manager.create_matrix(n, n);
        
        // 复制输入矩阵
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                temp->at(i, j) = A.safe_get(i, j);
            }
        }
        
        // 初始化为单位矩阵
        for (Integer i = 0; i < n; ++i) {
            for (Integer j = 0; j < n; ++j) {
                result->at(i, j) = (i == j) ? 1.0 : 0.0;
            }
        }
        
        // Gauss-Jordan消元法（简化版本）
        for (Integer i = 0; i < n; ++i) {
            // 寻找主元
            Real pivot = temp->safe_get(i, i);
            if (std::abs(pivot) < 1e-12) {
                throw std::runtime_error("Matrix is singular or nearly singular");
            }
            
            // 归一化当前行
            for (Integer j = 0; j < n; ++j) {
                temp->at(i, j) /= pivot;
                result->at(i, j) /= pivot;
            }
            
            // 消元
            for (Integer k = 0; k < n; ++k) {
                if (k != i) {
                    Real factor = temp->safe_get(k, i);
                    for (Integer j = 0; j < n; ++j) {
                        temp->at(k, j) -= factor * temp->safe_get(i, j);
                        result->at(k, j) -= factor * result->safe_get(i, j);
                    }
                }
            }
        }
        
        return *result;
    } catch (...) {
        throw std::runtime_error("Failed to compute matrix inverse");
    }
}

/**
 * @brief 安全的矩阵条件数计算
 */
Real safe_condition_number(const Matrix& A) {
    try {
        // 简化的条件数估算（实际应该使用SVD）
        Real max_element = 0.0;
        Real min_element = std::numeric_limits<Real>::max();
        
        for (Integer i = 0; i < A.rows; ++i) {
            for (Integer j = 0; j < A.cols; ++j) {
                Real val = std::abs(A.safe_get(i, j));
                if (val > max_element) max_element = val;
                if (val < min_element && val > 1e-15) min_element = val;
            }
        }
        
        if (min_element == 0.0) {
            return std::numeric_limits<Real>::infinity();
        }
        
        return max_element / min_element;
    } catch (...) {
        return std::numeric_limits<Real>::infinity();
    }
}

} // namespace SafeMATLAB

// 内存泄漏检测工具
namespace MemoryLeakDetector {

static std::unordered_map<void*, size_t> allocated_blocks;
static std::mutex allocation_mutex;

void record_allocation(void* ptr, size_t size) {
    std::lock_guard<std::mutex> lock(allocation_mutex);
    allocated_blocks[ptr] = size;
}

void record_deallocation(void* ptr) {
    std::lock_guard<std::mutex> lock(allocation_mutex);
    allocated_blocks.erase(ptr);
}

size_t get_leaked_memory() {
    std::lock_guard<std::mutex> lock(allocation_mutex);
    size_t total = 0;
    for (const auto& pair : allocated_blocks) {
        total += pair.second;
    }
    return total;
}

void print_leak_report() {
    std::lock_guard<std::mutex> lock(allocation_mutex);
    if (allocated_blocks.empty()) {
        std::cout << "No memory leaks detected." << std::endl;
    } else {
        std::cout << "Memory leaks detected:" << std::endl;
        for (const auto& pair : allocated_blocks) {
            std::cout << "  Block at " << pair.first << ": " << pair.second << " bytes" << std::endl;
        }
    }
}

} // namespace MemoryLeakDetector
