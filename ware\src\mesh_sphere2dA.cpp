/**
 * @file mesh_sphere2dA.cpp
 * @brief 完全按照MATLAB mesh_sphere2dA.m重新实现的网格生成函数
 *
 * 这个实现严格遵循MATLAB版本的9个区域逻辑：
 * Region 1,2: 底部区域 (bottom)
 * Region 3,4: 左侧区域 (left)
 * Region 5: 中心区域 (center)
 * Region 6,7: 右侧区域 (right)
 * Region 8,9: 顶部区域 (top)
 */

#include "../include/mesh_sphere2dA.hpp"
#include "../include/create_domain2dA.hpp"
#include "../include/material_model.hpp"
#include <iostream>
#include <cmath>
#include <algorithm>

// PI和EPS常量已在common_types.hpp中定义

// 前向声明
static std::pair<Integer, Integer> compute_grid_size(const Matrix& xa, const Matrix& za,
                                                    Real freq, Real ppw, Integer region_type);
static void process_region_1289(std::vector<Domain2dA>& OM, Integer iom, const Vector& allrad,
                               Integer jb, Integer ib, Integer len_rad, Integer nparts,
                               Integer region, Real inflat, const Vector& rad,
                               Real freq, Real ppw, Real dx);
static void process_region_3467(std::vector<Domain2dA>& OM, Integer iom, const Vector& allrad,
                                Integer jb, Integer ib, Integer len_rad, Integer nparts,
                                Integer region, Real inflat, const Vector& rad,
                                Real freq, Real ppw, Real dx);
static void process_region_5(std::vector<Domain2dA>& OM, Integer iom, const Vector& allrad,
                            Integer jb, Integer ib, Integer len_rad, Integer nparts,
                            Integer region, Real inflat, const Vector& rad,
                            Real freq, Real ppw, Real dx);

// 验证网格生成的关键特性
static void validate_mesh(const std::vector<Domain2dA>& OM) {
    std::cout << "=========== Mesh Validation ============" << std::endl;
    std::cout << "Total domains: " << OM.size() << std::endl;

    for(size_t i=0; i<OM.size(); ++i) {
        const auto& dom = OM[i];
        std::cout << "  Domain " << i << ": region=" << dom.region
                  << ", nx=" << dom.model2dA.nx << ", nz=" << dom.model2dA.nz
                  << ", Nx1=" << dom.Nx1 << ", Nz1=" << dom.Nz1 << std::endl;
    }
    std::cout << "========================================" << std::endl;
}

/**
 * @brief 处理区域1,2,8,9 - 底部和顶部区域
 * 严格按照MATLAB mesh_sphere2dA.m中case {1,2,8,9}的逻辑
 */
static void process_region_1289(std::vector<Domain2dA>& OM, Integer iom, const Vector& allrad,
                               Integer jb, Integer ib, Integer len_rad, Integer nparts,
                               Integer region, Real inflat, const Vector& rad,
                               Real freq, Real ppw, Real dx) {

    Real r1 = allrad(jb - 1);      // MATLAB: r1 = allrad(jb)
    Real r2 = allrad(jb);          // MATLAB: r2 = allrad(jb+1)

    Real aR = std::abs(r2 + r1) / 2.0;
    Real dR = std::abs(r2 - r1);

    Integer nx = std::max(3, static_cast<Integer>(std::ceil((aR * PI / 2.0) / nparts / dx)));
    Integer nz = std::max(3, static_cast<Integer>(std::ceil(dR / dx)));



    Matrix xa(nx, nz), za(nx, nz);

    // 计算theta范围
    Real theta1, theta2;
    if (region == 1 || region == 2) {
        theta1 = 5.0 * PI / 4.0 + static_cast<Real>(ib - len_rad) / nparts * PI / 2.0;
        theta2 = 5.0 * PI / 4.0 + static_cast<Real>(ib + 1 - len_rad) / nparts * PI / 2.0;
    } else { // region == 8 || region == 9
        theta1 = 3.0 * PI / 4.0 - static_cast<Real>(ib - len_rad) / nparts * PI / 2.0;
        theta2 = 3.0 * PI / 4.0 - static_cast<Real>(ib + 1 - len_rad) / nparts * PI / 2.0;
    }

    // 生成网格点
    for (Integer jj = 1; jj <= nz; ++jj) {
        for (Integer ii = 1; ii <= nx; ++ii) {
            Real theta = static_cast<Real>(ii - 1) / (nx - 1) * (theta2 - theta1) + theta1;
            Real r = static_cast<Real>(jj - 1) / (nz - 1) * (r2 - r1) + r1;
            Real rabs = std::abs(r);
            Real xtmp = rabs * std::cos(theta);
            Real ztmp = rabs * std::sin(theta);

            // 计算xp和zp
            Real zs = r / std::sqrt(2.0);
            Real coeff;
            if (region == 1 || region == 2) {
                coeff = 1.0 - 2.0 * (static_cast<Real>(ib - len_rad) + static_cast<Real>(ii - 1) / (nx - 1)) / nparts;
            } else { // region == 8 || region == 9
                coeff = -1.0 + 2.0 * (static_cast<Real>(ib - len_rad) + static_cast<Real>(ii - 1) / (nx - 1)) / nparts;
            }
            Real xs = coeff * zs;

            if (region == 2 || region == 8) {
                // 内球面区域，需要混合
                Real alpha = (1.0 - inflat) * (1.0 - (rabs - rad(0)) / (rad(1) - rad(0)));
                xa(ii - 1, jj - 1) = alpha * xs + (1.0 - alpha) * xtmp;
                za(ii - 1, jj - 1) = alpha * zs + (1.0 - alpha) * ztmp;
            } else {
                xa(ii - 1, jj - 1) = xtmp;
                za(ii - 1, jj - 1) = ztmp;
            }
        }
    }

    // 存储域信息
    OM[iom].model2dA.nx = nx;
    OM[iom].model2dA.nz = nz;
    OM[iom].model2dA.xa = xa;
    OM[iom].model2dA.za = za;
    OM[iom].region = region;
    OM[iom].iom = iom;

    // 计算网格尺寸
    std::pair<Integer, Integer> grid_size = compute_grid_size(xa, za, freq, ppw, region);
    OM[iom].Nx1 = grid_size.first;
    OM[iom].Nz1 = grid_size.second;
    OM[iom].Nx2 = grid_size.first - 1;   // 添加缺失的 Nx2 设置
    OM[iom].Nz2 = grid_size.second - 1;  // 添加缺失的 Nz2 设置
}

/**
 * @brief 计算网格尺寸 - 严格按照MATLAB逻辑
 */
static std::pair<Integer, Integer> compute_grid_size(const Matrix& xa, const Matrix& za,
                                                    Real freq, Real ppw, Integer region_type) {
    constexpr Real vp = 2750.0;
    constexpr Real fmax_factor = 3.0;
    constexpr Integer nmin = 3;

    Real fmax = freq * fmax_factor;
    Real wavemin = vp / fmax;

    // 计算区域边界
    Real xmin = 1e30, xmax = -1e30, zmin = 1e30, zmax = -1e30;
    Real rs_min = 1e30, rs_max = -1e30;

    for (Integer i = 0; i < xa.rows; ++i) {
        for (Integer j = 0; j < xa.cols; ++j) {
            Real x = xa(i, j), z = za(i, j);
            xmin = std::min(xmin, x); xmax = std::max(xmax, x);
            zmin = std::min(zmin, z); zmax = std::max(zmax, z);

            Real r0 = std::sqrt(x*x + z*z);
            rs_min = std::min(rs_min, r0);
            rs_max = std::max(rs_max, r0);
        }
    }

    Real rmin = rs_min * (1.0 + 10.0 * EPS);
    Real rmax = rs_max * (1.0 - 10.0 * EPS);
    Real lx = xmax - xmin;
    Real lz = zmax - zmin;

    Integer nnx = nmin, nnz = nmin;

    // 根据区域类型计算网格尺寸
    for (Integer i = 0; i < xa.rows; ++i) {
        for (Integer j = 0; j < xa.cols; ++j) {
            Real r0 = std::sqrt(xa(i,j)*xa(i,j) + za(i,j)*za(i,j));
            Real r = std::min(std::max(rmin, r0), rmax);

            Integer nx_cell, nz_cell;

            if (region_type == 1 || region_type == 2 || region_type == 8 || region_type == 9) {
                Real dtheta = 2.0 * PI / (2.0 * 4.0); // nparts=2
                nx_cell = static_cast<Integer>(std::ceil(dtheta * r / wavemin * ppw));
                nz_cell = static_cast<Integer>(std::ceil((rmax - rmin) / wavemin * ppw));
            } else if (region_type == 3 || region_type == 4 || region_type == 6 || region_type == 7) {
                Real dtheta = 2.0 * PI / (2.0 * 4.0); // nparts=2
                nz_cell = static_cast<Integer>(std::ceil(dtheta * r / wavemin * ppw));
                nx_cell = static_cast<Integer>(std::ceil((rmax - rmin) / wavemin * ppw));
            } else {
                // 中心区域 (region 5)
                nx_cell = static_cast<Integer>(std::ceil(lx / wavemin * ppw));
                nz_cell = static_cast<Integer>(std::ceil(lz / wavemin * ppw));
            }

            nnx = std::max(nnx, nx_cell);
            nnz = std::max(nnz, nz_cell);
        }
    }

    return {nnx, nnz};
}

/**
 * @brief 处理区域3,4,6,7 - 左侧和右侧区域
 * 严格按照MATLAB mesh_sphere2dA.m中case {3,4,6,7}的逻辑
 */
static void process_region_3467(std::vector<Domain2dA>& OM, Integer iom, const Vector& allrad,
                                Integer jb, Integer ib, Integer len_rad, Integer nparts,
                                Integer region, Real inflat, const Vector& rad,
                                Real freq, Real ppw, Real dx) {

    Real r1 = allrad(ib - 1);      // MATLAB: r1 = allrad(ib)
    Real r2 = allrad(ib);          // MATLAB: r2 = allrad(ib+1)

    Real aR = std::abs(r2 + r1) / 2.0;
    Real dR = std::abs(r2 - r1);

    Integer nx = std::max(3, static_cast<Integer>(std::ceil(dR / dx)));
    Integer nz = std::max(3, static_cast<Integer>(std::ceil((aR * PI / 2.0) / nparts / dx)));



    Matrix xa(nx, nz), za(nx, nz);

    // 计算theta范围
    Real theta1, theta2;
    if (region == 3 || region == 4) {
        theta1 = 5.0 * PI / 4.0 - static_cast<Real>(jb - len_rad) / nparts * PI / 2.0;
        theta2 = 5.0 * PI / 4.0 - static_cast<Real>(jb + 1 - len_rad) / nparts * PI / 2.0;
    } else { // region == 6 || region == 7
        theta1 = 7.0 * PI / 4.0 + static_cast<Real>(jb - len_rad) / nparts * PI / 2.0;
        theta2 = 7.0 * PI / 4.0 + static_cast<Real>(jb + 1 - len_rad) / nparts * PI / 2.0;
    }

    // 生成网格点
    for (Integer jj = 1; jj <= nz; ++jj) {
        for (Integer ii = 1; ii <= nx; ++ii) {
            Real theta = static_cast<Real>(jj - 1) / (nz - 1) * (theta2 - theta1) + theta1;
            Real r = static_cast<Real>(ii - 1) / (nx - 1) * (r2 - r1) + r1;
            Real rabs = std::abs(r);
            Real xtmp = rabs * std::cos(theta);
            Real ztmp = rabs * std::sin(theta);

            // 计算xp和zp
            Real xs = r / std::sqrt(2.0);
            Real coeff;
            if (region == 6 || region == 7) {
                coeff = -1.0 + 2.0 * (static_cast<Real>(jb - len_rad) + static_cast<Real>(jj - 1) / (nz - 1)) / nparts;
            } else { // region == 3 || region == 4
                coeff = 1.0 - 2.0 * (static_cast<Real>(jb - len_rad) + static_cast<Real>(jj - 1) / (nz - 1)) / nparts;
            }
            Real zs = xs * coeff;

            if (region == 4 || region == 6) {
                // 内球面区域，需要混合
                Real alpha = (1.0 - inflat) * (1.0 - (rabs - rad(0)) / (rad(1) - rad(0)));
                xa(ii - 1, jj - 1) = alpha * xs + (1.0 - alpha) * xtmp;
                za(ii - 1, jj - 1) = alpha * zs + (1.0 - alpha) * ztmp;
            } else {
                xa(ii - 1, jj - 1) = xtmp;
                za(ii - 1, jj - 1) = ztmp;
            }
        }
    }

    // 存储域信息
    OM[iom].model2dA.nx = nx;
    OM[iom].model2dA.nz = nz;
    OM[iom].model2dA.xa = xa;
    OM[iom].model2dA.za = za;
    OM[iom].region = region;
    OM[iom].iom = iom;

    // 计算网格尺寸
    std::pair<Integer, Integer> grid_size = compute_grid_size(xa, za, freq, ppw, region);
    OM[iom].Nx1 = grid_size.first;
    OM[iom].Nz1 = grid_size.second;
    OM[iom].Nx2 = grid_size.first - 1;   // 添加缺失的 Nx2 设置
    OM[iom].Nz2 = grid_size.second - 1;  // 添加缺失的 Nz2 设置
}

/**
 * @brief 处理区域5 - 中心区域
 * 严格按照MATLAB mesh_sphere2dA.m中case 5的逻辑
 */
static void process_region_5(std::vector<Domain2dA>& OM, Integer iom, const Vector& allrad,
                            Integer jb, Integer ib, Integer len_rad, Integer nparts,
                            Integer region, Real inflat, const Vector& rad,
                            Real freq, Real ppw, Real dx) {

    Real rx1 = allrad(ib - 1);     // MATLAB: rx1 = allrad(ib)
    Real rx2 = allrad(ib);         // MATLAB: rx2 = allrad(ib+1)
    Integer nx = std::max(3, static_cast<Integer>(std::ceil((rx2 - rx1) / dx)));

    Real rz1 = allrad(jb - 1);     // MATLAB: rz1 = allrad(jb)
    Real rz2 = allrad(jb);         // MATLAB: rz2 = allrad(jb+1)
    Integer nz = std::max(3, static_cast<Integer>(std::ceil((rz2 - rz1) / dx)));



    Matrix xa(nx, nz), za(nx, nz);

    // 生成网格点 - 中心方形区域
    for (Integer jj = 1; jj <= nz; ++jj) {
        for (Integer ii = 1; ii <= nx; ++ii) {
            // 方形：xp和zp是参考点切线方形
            Real xs = static_cast<Real>(ii - 1) / (nx - 1) * (rx2 - rx1) + rx1;
            Real zs = static_cast<Real>(jj - 1) / (nz - 1) * (rz2 - rz1) + rz1;

            Real theta, r;

            // 确定在哪个象限
            if ((zs <= -xs) && (zs <= xs)) {
                // bottom
                theta = (xs - zs) / std::abs(zs) * PI / 4.0 + 5.0 * PI / 4.0;
                r = std::abs(zs);
            } else if ((xs <= -zs) && (xs <= zs)) {
                // left
                theta = (xs - zs) / std::abs(xs) * PI / 4.0 + 5.0 * PI / 4.0;
                r = std::abs(xs);
            } else if ((xs >= -zs) && (xs >= zs)) {
                // right
                theta = (zs - xs) / std::abs(xs) * PI / 4.0 + PI / 4.0;
                r = std::abs(xs);
            } else if ((zs >= -xs) && (zs >= xs)) {
                // up
                theta = (zs - xs) / std::abs(zs) * PI / 4.0 + PI / 4.0;
                r = std::abs(zs);
            }

            if (std::abs(r) < EPS) {
                r = 0.0;
                theta = 0.0;
            }

            Real alpha = r / rad(0) * inflat;
            // 除以sqrt(2)的原因是外切正方形sqrt(2)到1
            xa(ii - 1, jj - 1) = (1.0 - alpha) * xs / std::sqrt(2.0) + alpha * r * std::cos(theta);
            za(ii - 1, jj - 1) = (1.0 - alpha) * zs / std::sqrt(2.0) + alpha * r * std::sin(theta);
        }
    }

    // 存储域信息
    OM[iom].model2dA.nx = nx;
    OM[iom].model2dA.nz = nz;
    OM[iom].model2dA.xa = xa;
    OM[iom].model2dA.za = za;
    OM[iom].region = region;
    OM[iom].iom = iom;

    // 计算网格尺寸
    std::pair<Integer, Integer> grid_size = compute_grid_size(xa, za, freq, ppw, region);
    OM[iom].Nx1 = grid_size.first;
    OM[iom].Nz1 = grid_size.second;
    OM[iom].Nx2 = grid_size.first - 1;   // 添加缺失的 Nx2 设置
    OM[iom].Nz2 = grid_size.second - 1;  // 添加缺失的 Nz2 设置
}
/**
 * @brief 主要的mesh_sphere2dA函数 - 完全按照MATLAB实现
 */
std::vector<Domain2dA> mesh_sphere2dA(const Vector& rad, Real dx, Integer nparts, Real freq, Real ppw) {
    // MATLAB参数设置
    constexpr Real inflat = 0.44;  // 中心方形膨胀系数
    constexpr Real vp = 2750.0;
    constexpr Real vs = 0.0;
    constexpr Real rho = 2000.0;
    constexpr Real mu = rho * vp * vp;
    constexpr Real fmax_factor = 3.0;
    constexpr Integer nmin = 3;

    Real fmax = freq * fmax_factor;

    // 构建allrad数组 - 严格按照MATLAB逻辑
    Integer len_rad = rad.size();
    Integer len_allrad = 2 * len_rad + nparts - 1;
    Vector allrad(len_allrad);

    // allrad(1:len_rad) = -rad(len_rad:-1:1)
    for (Integer i = 0; i < len_rad; ++i) {
        allrad(i) = -rad(len_rad - 1 - i);
    }

    // for ib = 1:nparts-1, allrad(len_rad+ib) = ib/nparts*2*rad(1)-rad(1)
    for (Integer ib = 1; ib <= nparts - 1; ++ib) {
        allrad(len_rad + ib - 1) = static_cast<Real>(ib) / nparts * 2.0 * rad(0) - rad(0);
    }

    // allrad(len_rad+nparts:end) = rad
    for (Integer i = 0; i < len_rad; ++i) {
        allrad(len_rad + nparts - 1 + i) = rad(i);
    }

    // 计算域数量 - 严格按照MATLAB公式
    Integer ndomain = (len_allrad - 1) * (len_allrad - 1) - 4 * (len_rad - 1) * (len_rad - 1);
    std::vector<Domain2dA> OM(ndomain);

    // 初始化所有域
    for (Integer i = 0; i < ndomain; ++i) {
        OM[i] = create_domain2dA();
    }

    Real dtheta = 2.0 * PI / (nparts * 4.0);
    Integer iom = 0;



    // 主循环 - 严格按照MATLAB的双重循环逻辑
    for (Integer jb = 1; jb <= len_allrad - 1; ++jb) {      // z方向块数
        for (Integer ib = 1; ib <= len_allrad - 1; ++ib) {  // x方向块数

            // 区域分类逻辑 - 严格按照MATLAB
            Integer region = 0;

            if (jb < len_rad - 1 && ib >= len_rad && ib < len_rad + nparts) {
                region = 1; // bottom
            } else if (jb == len_rad - 1 && ib >= len_rad && ib < len_rad + nparts) {
                region = 2; // bottom innermost sphere
            } else if (ib < len_rad - 1 && jb >= len_rad && jb < len_rad + nparts) {
                region = 3; // left
            } else if (ib == len_rad - 1 && jb >= len_rad && jb < len_rad + nparts) {
                region = 4; // left innermost sphere
            } else if (ib >= len_rad && ib < len_rad + nparts && jb >= len_rad && jb < len_rad + nparts) {
                region = 5; // center cube innermost sphere
            } else if (ib == len_rad + nparts && jb >= len_rad && jb < len_rad + nparts) {
                region = 6; // right inner sphere
            } else if (ib > len_rad + nparts && jb >= len_rad && jb < len_rad + nparts) {
                region = 7; // right
            } else if (jb == len_rad + nparts && ib >= len_rad && ib < len_rad + nparts) {
                region = 8; // top inner sphere
            } else if (jb > len_rad + nparts && ib >= len_rad && ib < len_rad + nparts) {
                region = 9; // top
            }

            // 只处理有效区域
            if (region == 0) continue;



            // 处理不同区域类型
            if (region == 1 || region == 2 || region == 8 || region == 9) {
                // 底部和顶部区域
                process_region_1289(OM, iom, allrad, jb, ib, len_rad, nparts, region, inflat, rad, freq, ppw, dx);
                iom++;
            } else if (region == 3 || region == 4 || region == 6 || region == 7) {
                // 左侧和右侧区域
                process_region_3467(OM, iom, allrad, jb, ib, len_rad, nparts, region, inflat, rad, freq, ppw, dx);
                iom++;
            } else if (region == 5) {
                // 中心区域
                process_region_5(OM, iom, allrad, jb, ib, len_rad, nparts, region, inflat, rad, freq, ppw, dx);
                iom++;
            }
        }
    }

    // 设置材料属性 - 严格按照MATLAB逻辑

    for (Integer i = 0; i < iom; ++i) {
        auto& dom = OM[i];
        Integer nx = dom.model2dA.nx;
        Integer nz = dom.model2dA.nz;

        Matrix rho_tmp(nx, nz), mu_tmp(nx, nz);

        for (Integer ii = 0; ii < nx; ++ii) {
            for (Integer jj = 0; jj < nz; ++jj) {
                Real x = dom.model2dA.xa(ii, jj);
                Real z = dom.model2dA.za(ii, jj);
                Real r_km = std::sqrt(x*x + z*z) / 1000.0; // 转换为km

                // 使用PREM模型
                rho_tmp(ii, jj) = prem::density_prem(r_km) * 1000.0; // g/cm³ -> kg/m³
                Real vs = prem::vs_prem(r_km) * 1000.0;  // km/s -> m/s
                Real vp_prem = prem::vp_prem(r_km) * 1000.0;  // km/s -> m/s
                mu_tmp(ii, jj) = rho_tmp(ii, jj) * vs * vs;   // Pa
            }
        }

        dom.model2dA.rho = rho_tmp;
        dom.model2dA.mu = mu_tmp;

        // 构建rho12和rho21
        Integer nz2 = std::max(1, nz - 1);
        Integer nx2 = std::max(1, nx - 1);
        Matrix rho12(nx, nz2), rho21(nx2, nz);

        for (Integer ii = 0; ii < nx; ++ii) {
            for (Integer jj = 0; jj < nz2; ++jj) {
                rho12(ii, jj) = 0.5 * (rho_tmp(ii, jj) + rho_tmp(ii, jj + 1));
            }
        }
        for (Integer ii = 0; ii < nx2; ++ii) {
            for (Integer jj = 0; jj < nz; ++jj) {
                rho21(ii, jj) = 0.5 * (rho_tmp(ii, jj) + rho_tmp(ii + 1, jj));
            }
        }

        dom.rho12 = rho12;
        dom.rho21 = rho21;
        dom.mu11 = mu_tmp;

        Matrix mu22(nx2, nz2);
        for (Integer ii = 0; ii < nx2; ++ii) {
            for (Integer jj = 0; jj < nz2; ++jj) {
                mu22(ii, jj) = 0.25 * (mu_tmp(ii, jj) + mu_tmp(ii + 1, jj) +
                                      mu_tmp(ii, jj + 1) + mu_tmp(ii + 1, jj + 1));
            }
        }
        dom.mu22 = mu22;
    }

    // 调整OM大小以匹配实际生成的域数量
    OM.resize(iom);

    validate_mesh(OM);
    return OM;
}