/**
 * @file plot_functions.cpp
 * @brief 绘图函数的C++替代实现 - 数据输出版本
 */

#include "../include/plot_functions.hpp"
#include <iostream>
#include <fstream>
#include <iomanip>
#include <filesystem>
#include <cmath>

void save_domain_plot_data(const std::vector<Domain2dA>& OM, const std::string& output_dir) {
    // 创建输出目录
    std::filesystem::create_directories(output_dir);
    
    // 保存域边界数据
    std::ofstream boundary_file(output_dir + "/domain_boundaries.csv");
    boundary_file << "domain_id,boundary_type,x_km,z_km\n";
    
    // 保存Vp速度场数据
    std::ofstream vp_file(output_dir + "/vp_field.csv");
    vp_file << "domain_id,x_km,z_km,vp_ms,rho_kgm3,mu_pa\n";
    
    Real rmax = 0.0;
    
    for (size_t iom = 0; iom < OM.size(); ++iom) {
        const auto& dom = OM[iom];
        const Matrix& xa = dom.model2dA.xa;
        const Matrix& za = dom.model2dA.za;
        const Matrix& rho = dom.model2dA.rho;
        const Matrix& mu = dom.model2dA.mu;
        
        if (xa.rows == 0 || za.rows == 0) continue;
        
        Integer nx = xa.rows;
        Integer nz = xa.cols;
        
        // 计算最大半径
        for (Integer i = 0; i < nx; ++i) {
            for (Integer j = 0; j < nz; ++j) {
                Real dist = std::sqrt(xa(i,j)*xa(i,j) + za(i,j)*za(i,j));
                rmax = std::max(rmax, dist);
            }
        }
        
        // 保存域边界 - 对应MATLAB中的边界绘制
        // 左边界 (id1)
        for (Integer j = 0; j < nz; ++j) {
            boundary_file << iom << ",left," << xa(0,j)/1e3 << "," << za(0,j)/1e3 << "\n";
        }
        // 右边界 (id2)
        for (Integer j = 0; j < nz; ++j) {
            boundary_file << iom << ",right," << xa(nx-1,j)/1e3 << "," << za(nx-1,j)/1e3 << "\n";
        }
        // 底边界 (id3)
        for (Integer i = 0; i < nx; ++i) {
            boundary_file << iom << ",bottom," << xa(i,0)/1e3 << "," << za(i,0)/1e3 << "\n";
        }
        // 顶边界 (id4)
        for (Integer i = 0; i < nx; ++i) {
            boundary_file << iom << ",top," << xa(i,nz-1)/1e3 << "," << za(i,nz-1)/1e3 << "\n";
        }
        
        // 保存Vp速度场数据
        for (Integer i = 0; i < nx; ++i) {
            for (Integer j = 0; j < nz; ++j) {
                Real vp = (rho(i,j) > 0) ? std::sqrt(mu(i,j) / rho(i,j)) : 0.0;
                vp_file << iom << "," << xa(i,j)/1e3 << "," << za(i,j)/1e3 << "," 
                        << vp << "," << rho(i,j) << "," << mu(i,j) << "\n";
            }
        }
    }
    
    boundary_file.close();
    vp_file.close();
    
    // 保存绘图配置信息
    std::ofstream config_file(output_dir + "/plot_config.txt");
    config_file << "# Domain plot configuration\n";
    config_file << "rmax_km: " << rmax/1e3 << "\n";
    config_file << "num_domains: " << OM.size() << "\n";
    config_file << "colormap: hot\n";
    config_file << "xlabel: km\n";
    config_file << "ylabel: km\n";
    config_file << "title: Vp (m/s)\n";
    config_file.close();
    
    std::cout << "Domain plot data saved to " << output_dir << "/" << std::endl;
    std::cout << "  - domain_boundaries.csv: Domain boundary coordinates" << std::endl;
    std::cout << "  - vp_field.csv: Velocity field data" << std::endl;
    std::cout << "  - plot_config.txt: Plot configuration" << std::endl;
}

void save_wavefield_plot_data(const std::vector<Domain2dA>& OM, Integer it, const std::string& output_dir) {
    // 创建输出目录
    std::filesystem::create_directories(output_dir);
    
    // 创建时间步特定的文件名
    std::string timestep_str = std::to_string(it);
    std::string padded_timestep = std::string(6 - timestep_str.length(), '0') + timestep_str;
    
    // 保存网格边界数据
    std::ofstream boundary_file(output_dir + "/wavefield_boundaries_" + padded_timestep + ".csv");
    boundary_file << "domain_id,boundary_type,x_km,z_km\n";
    
    // 保存位移场数据
    std::ofstream field_file(output_dir + "/wavefield_" + padded_timestep + ".csv");
    field_file << "domain_id,x_km,z_km,displacement\n";
    
    Real Umax = -99999999.0;
    
    for (size_t iom = 0; iom < OM.size(); ++iom) {
        const auto& dom = OM[iom];
        const Matrix& x2d = dom.x2d11;
        const Matrix& z2d = dom.z2d11;
        
        if (x2d.rows == 0 || z2d.rows == 0) continue;
        
        Integer nx = x2d.rows;
        Integer nz = x2d.cols;
        
        // 保存网格边界 - 对应MATLAB中的虚线边界
        // 左边界
        for (Integer j = 0; j < nz; ++j) {
            boundary_file << iom << ",left," << x2d(0,j)/1e3 << "," << z2d(0,j)/1e3 << "\n";
        }
        // 右边界
        for (Integer j = 0; j < nz; ++j) {
            boundary_file << iom << ",right," << x2d(nx-1,j)/1e3 << "," << z2d(nx-1,j)/1e3 << "\n";
        }
        // 底边界
        for (Integer i = 0; i < nx; ++i) {
            boundary_file << iom << ",bottom," << x2d(i,0)/1e3 << "," << z2d(i,0)/1e3 << "\n";
        }
        // 顶边界
        for (Integer i = 0; i < nx; ++i) {
            boundary_file << iom << ",top," << x2d(i,nz-1)/1e3 << "," << z2d(i,nz-1)/1e3 << "\n";
        }
        
        // 保存位移场数据 - 对应MATLAB中的Umid
        // 注意：这里假设位移场存储在state.Umid中，如果结构不同需要调整
        if (dom.state.Umid.size() > static_cast<size_t>(it) && it >= 0) {
            const Matrix& Umid = dom.state.Umid[it];
            for (Integer i = 0; i < nx && i < Umid.rows; ++i) {
                for (Integer j = 0; j < nz && j < Umid.cols; ++j) {
                    Real displacement = Umid(i,j);
                    Umax = std::max(Umax, displacement);
                    field_file << iom << "," << x2d(i,j)/1e3 << "," << z2d(i,j)/1e3 << "," 
                              << displacement << "\n";
                }
            }
        }
    }
    
    boundary_file.close();
    field_file.close();
    
    // 保存时间步信息
    std::ofstream info_file(output_dir + "/timestep_" + padded_timestep + "_info.txt");
    info_file << "# Wavefield plot information\n";
    info_file << "timestep: " << it << "\n";
    info_file << "max_displacement: " << std::scientific << Umax << "\n";
    info_file << "colormap: jet\n";
    info_file << "clim_min: -8e-8\n";
    info_file << "clim_max: 8e-8\n";
    info_file.close();
    
    std::cout << "... time step " << it << " with max(U) = " << std::scientific << Umax << " ..." << std::endl;
    std::cout << "Wavefield data saved to " << output_dir << "/" << std::endl;
}

void print_domain_summary(const std::vector<Domain2dA>& OM) {
    std::cout << "\n========== Domain Summary ==========" << std::endl;
    std::cout << "Total domains: " << OM.size() << std::endl;
    
    for (size_t i = 0; i < OM.size(); ++i) {
        const auto& dom = OM[i];
        std::cout << "Domain " << i << ":" << std::endl;
        std::cout << "  Region: " << dom.region << std::endl;
        std::cout << "  Grid size: " << dom.model2dA.nx << " x " << dom.model2dA.nz << std::endl;
        std::cout << "  Basis functions: Nx1=" << dom.Nx1 << ", Nz1=" << dom.Nz1 << std::endl;
        
        // 计算域的边界
        if (dom.model2dA.xa.rows > 0 && dom.model2dA.za.rows > 0) {
            Real xmin = 1e30, xmax = -1e30, zmin = 1e30, zmax = -1e30;
            for (Integer ii = 0; ii < dom.model2dA.xa.rows; ++ii) {
                for (Integer jj = 0; jj < dom.model2dA.xa.cols; ++jj) {
                    xmin = std::min(xmin, dom.model2dA.xa(ii,jj));
                    xmax = std::max(xmax, dom.model2dA.xa(ii,jj));
                    zmin = std::min(zmin, dom.model2dA.za(ii,jj));
                    zmax = std::max(zmax, dom.model2dA.za(ii,jj));
                }
            }
            std::cout << "  Bounds: x=[" << xmin/1e3 << ", " << xmax/1e3 << "] km, "
                      << "z=[" << zmin/1e3 << ", " << zmax/1e3 << "] km" << std::endl;
        }
        
        // 邻居信息
        std::cout << "  Neighbors: mo=" << dom.iNbr_mo << ", po=" << dom.iNbr_po 
                  << ", om=" << dom.iNbr_om << ", op=" << dom.iNbr_op << std::endl;
    }
    std::cout << "====================================" << std::endl;
}

void create_python_plot_scripts(const std::string& output_dir) {
    std::filesystem::create_directories(output_dir);

    // 创建域绘图脚本
    std::ofstream domain_script(output_dir + "/plot_domains.py");
    domain_script << R"(#!/usr/bin/env python3
"""
Domain plotting script - equivalent to MATLAB plot_domain2d function
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def plot_domains(data_dir='output'):
    # Read data
    boundaries = pd.read_csv(f'{data_dir}/domain_boundaries.csv')
    vp_field = pd.read_csv(f'{data_dir}/vp_field.csv')

    # Read configuration
    config = {}
    with open(f'{data_dir}/plot_config.txt', 'r') as f:
        for line in f:
            if ':' in line and not line.startswith('#'):
                key, value = line.strip().split(':', 1)
                config[key.strip()] = value.strip()

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Plot 1: Domain boundaries
    for domain_id in boundaries['domain_id'].unique():
        domain_bounds = boundaries[boundaries['domain_id'] == domain_id]
        for boundary_type in ['left', 'right', 'bottom', 'top']:
            bound_data = domain_bounds[domain_bounds['boundary_type'] == boundary_type]
            ax1.plot(bound_data['x_km'], bound_data['z_km'], '--k', linewidth=1)

    ax1.set_xlabel('km')
    ax1.set_ylabel('km')
    ax1.set_title('Domain Boundaries')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)

    # Plot 2: Vp field
    for domain_id in vp_field['domain_id'].unique():
        domain_data = vp_field[vp_field['domain_id'] == domain_id]
        scatter = ax2.scatter(domain_data['x_km'], domain_data['z_km'],
                            c=domain_data['vp_ms'], cmap='hot', s=1)

    plt.colorbar(scatter, ax=ax2, label='Vp (m/s)')
    ax2.set_xlabel('km')
    ax2.set_ylabel('km')
    ax2.set_title('Vp (m/s)')
    ax2.axis('equal')

    plt.tight_layout()
    plt.savefig(f'{data_dir}/domain_plot.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == '__main__':
    plot_domains()
)";
    domain_script.close();

    // 创建波场绘图脚本
    std::ofstream wavefield_script(output_dir + "/plot_wavefields.py");
    wavefield_script << R"(#!/usr/bin/env python3
"""
Wavefield plotting script - equivalent to MATLAB plot_wavefields2dA function
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import glob
import os

def plot_wavefield(timestep, data_dir='output'):
    """Plot wavefield for a specific timestep"""
    padded_timestep = f"{timestep:06d}"

    # Read data
    boundary_file = f'{data_dir}/wavefield_boundaries_{padded_timestep}.csv'
    field_file = f'{data_dir}/wavefield_{padded_timestep}.csv'
    info_file = f'{data_dir}/timestep_{padded_timestep}_info.txt'

    if not os.path.exists(field_file):
        print(f"No data found for timestep {timestep}")
        return

    boundaries = pd.read_csv(boundary_file)
    field_data = pd.read_csv(field_file)

    # Read info
    info = {}
    with open(info_file, 'r') as f:
        for line in f:
            if ':' in line and not line.startswith('#'):
                key, value = line.strip().split(':', 1)
                info[key.strip()] = value.strip()

    fig, ax = plt.subplots(figsize=(10, 8))

    # Plot domain boundaries
    for domain_id in boundaries['domain_id'].unique():
        domain_bounds = boundaries[boundaries['domain_id'] == domain_id]
        for boundary_type in ['left', 'right', 'bottom', 'top']:
            bound_data = domain_bounds[domain_bounds['boundary_type'] == boundary_type]
            ax.plot(bound_data['x_km'], bound_data['z_km'], '--k', linewidth=1)

    # Plot displacement field
    scatter = ax.scatter(field_data['x_km'], field_data['z_km'],
                        c=field_data['displacement'], cmap='jet', s=1,
                        vmin=-8e-8, vmax=8e-8)

    plt.colorbar(scatter, ax=ax, label='Displacement')
    ax.set_xlabel('km')
    ax.set_ylabel('km')
    ax.set_title(f'Wavefield - Timestep {timestep}')
    ax.axis('equal')

    max_disp = float(info.get('max_displacement', '0'))
    ax.text(0.02, 0.98, f'Max displacement: {max_disp:.2e}',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{data_dir}/wavefield_{padded_timestep}.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_animation(data_dir='output', output_file='wavefield_animation.gif'):
    """Create animation from all timesteps"""
    import matplotlib.animation as animation

    # Find all wavefield files
    field_files = glob.glob(f'{data_dir}/wavefield_??????.csv')
    timesteps = sorted([int(f.split('_')[-1].split('.')[0]) for f in field_files])

    if not timesteps:
        print("No wavefield data found")
        return

    fig, ax = plt.subplots(figsize=(10, 8))

    def animate(frame):
        ax.clear()
        timestep = timesteps[frame]
        padded_timestep = f"{timestep:06d}"

        # Read data
        boundaries = pd.read_csv(f'{data_dir}/wavefield_boundaries_{padded_timestep}.csv')
        field_data = pd.read_csv(f'{data_dir}/wavefield_{padded_timestep}.csv')

        # Plot boundaries
        for domain_id in boundaries['domain_id'].unique():
            domain_bounds = boundaries[boundaries['domain_id'] == domain_id]
            for boundary_type in ['left', 'right', 'bottom', 'top']:
                bound_data = domain_bounds[domain_bounds['boundary_type'] == boundary_type]
                ax.plot(bound_data['x_km'], bound_data['z_km'], '--k', linewidth=1)

        # Plot field
        scatter = ax.scatter(field_data['x_km'], field_data['z_km'],
                            c=field_data['displacement'], cmap='jet', s=1,
                            vmin=-8e-8, vmax=8e-8)

        ax.set_xlabel('km')
        ax.set_ylabel('km')
        ax.set_title(f'Wavefield - Timestep {timestep}')
        ax.axis('equal')

        return scatter,

    anim = animation.FuncAnimation(fig, animate, frames=len(timesteps),
                                  interval=100, blit=False, repeat=True)

    anim.save(f'{data_dir}/{output_file}', writer='pillow', fps=10)
    print(f"Animation saved as {data_dir}/{output_file}")

if __name__ == '__main__':
    import sys
    if len(sys.argv) > 1:
        timestep = int(sys.argv[1])
        plot_wavefield(timestep)
    else:
        print("Usage: python plot_wavefields.py <timestep>")
        print("Or call create_animation() to make an animation")
)";
    wavefield_script.close();

    std::cout << "Python plotting scripts created in " << output_dir << "/" << std::endl;
    std::cout << "  - plot_domains.py: Plot domain boundaries and Vp field" << std::endl;
    std::cout << "  - plot_wavefields.py: Plot wavefield data" << std::endl;
    std::cout << "Usage:" << std::endl;
    std::cout << "  python " << output_dir << "/plot_domains.py" << std::endl;
    std::cout << "  python " << output_dir << "/plot_wavefields.py <timestep>" << std::endl;
}
