#include "../include/save_waveforms2dA.hpp"
#include <fstream>
#include <iostream>
#include <iomanip>
#include <cstring>  // for strerror
#include <cerrno>   // for errno

// 根据平台选择合适的头文件
#ifdef _WIN32
    #include <direct.h>    // Windows下的mkdir
    #define MKDIR(dir) _mkdir(dir)
#else
    #include <sys/stat.h>  // POSIX系统下的mkdir
    #define MKDIR(dir) mkdir(dir, 0755)
#endif

// 辅助函数：创建目录（跨平台）
bool create_directory(const std::string& path) {
    int result = MKDIR(path.c_str());
    return (result == 0 || errno == EEXIST);  // 成功或目录已存在
}

// 辅助函数：在每个时间步更新接收器位置和波形记录
static void locate_receiver(Receiver2dA &r, const std::vector<Domain2dA>& OM){
    if (r.iom < 0 || r.iom >= static_cast<int>(OM.size())) {
        std::cerr << "Invalid receiver domain index: " << r.iom << std::endl;
        return;
    }

    // 直接使用接收器上已经计算好的B样条系数来计算波场
    const Domain2dA& dom = OM[r.iom];
    
    // 计算最终的物理坐标（如果需要）
    // 这里简单地取接收器所在单元的中心点坐标
    int nx = dom.model2dA.nx;
    int nz = dom.model2dA.nz;
    if (nx > 0 && nz > 0) {
        r.x = dom.model2dA.xa(nx/2, nz/2);
        r.z = dom.model2dA.za(nx/2, nz/2);
    }
}

std::vector<Receiver2dA> save_waveforms2dA(const std::vector<Domain2dA>& OM,
                                          std::vector<Receiver2dA>& rece,
                                          int it) {
    // 实时更新并保存波形数据
    for (size_t ir = 0; ir < rece.size(); ++ir) {
        Receiver2dA& r = rece[ir];
        
        // 确保接收器位置是最新的
        locate_receiver(r, OM);
        
        // 获取接收器所在域
        int iom = r.iom;
        if (iom < 0 || iom >= static_cast<int>(OM.size())) continue;
        
        const Domain2dA& dom = OM[iom];
        
        // 确保有足够的位置存储波形
        if (static_cast<size_t>(it) >= r.trace_u12.size()) {
            r.trace_u12.resize(it + 1, 0.0);
            r.trace_u21.resize(it + 1, 0.0);
        }
        
        // 使用张量积计算投影波场值（类似MATLAB版本中的计算）
        double sum_u12 = 0.0;
        double sum_u21 = 0.0;

        // 计算U12的投影（对应MATLAB中的tensor product）
        for (int i = 0; i < dom.Nx1; ++i) {
            for (int j = 0; j < dom.Nz2; ++j) {
                if (i < dom.state.U12.rows && j < dom.state.U12.cols) {
                    sum_u12 += r.rbx1[i] * r.rbz2[j] * dom.state.U12(i, j);
                }
            }
        }

        // 计算U21的投影
        for (int i = 0; i < dom.Nx2; ++i) {
            for (int j = 0; j < dom.Nz1; ++j) {
                if (i < dom.state.U21.rows && j < dom.state.U21.cols) {
                    sum_u21 += r.rbx2[i] * r.rbz1[j] * dom.state.U21(i, j);
                }
            }
        }
        
        // 存储当前时间步的波形值
        r.trace_u12[it] = sum_u12;
        r.trace_u21[it] = sum_u21;
        
        // MATLAB版本中，ur数组的第二列和第三列分别存储U12和U21
        // 确保ur数组有足够的大小
        if (it + 1 > r.ur.size()) {
            r.ur = Vector(it + 1);
        }
        
        // 更新ur数组 (对应MATLAB中 rece.ur(it,2) = ... 和 rece.ur(it,3) = ...)
        r.ur[it] = sum_u12;  // 简化版本，仅存储U12
    }
    
    // 返回更新后的接收器数组，与MATLAB版本一致
    return rece;
}

void save_waveforms2dA_final(const std::vector<Receiver2dA>& rec, const std::string& filename) {
    // 查找文件路径中的目录部分
    size_t pos = filename.find_last_of("/\\");
    if (pos != std::string::npos) {
        std::string dir = filename.substr(0, pos);
        // 尝试创建目录（如果不存在）
        if (!dir.empty()) {
            if (!create_directory(dir)) {
                std::cerr << "Error creating directory " << dir
                          << ": errno=" << errno << std::endl;
            }
        }
    }
    
    // 打开输出文件
    std::ofstream fout(filename);
    if (!fout) {
        std::cerr << "Error opening output file: " << filename << std::endl;
        return;
    }
    
    // 设置高精度输出
    fout << std::scientific << std::setprecision(10);
    
    // 对于每个接收器，保存其波形数据
    for (size_t ir = 0; ir < rec.size(); ++ir) {
        const Receiver2dA& r = rec[ir];
        
        // 写入接收器信息
        fout << "# Receiver " << ir << " at domain " << r.iom
             << " (x,z) = (" << r.x << ", " << r.z << ")" << std::endl;
        
        // 确定保存多少个时间步
        size_t nt = std::min(r.trace_u12.size(), r.trace_u21.size());
        if (r.time.size() > 0) {
            nt = std::min(nt, r.time.size());
        }
        
        // 写入列名
        fout << "# Time U12 U21" << std::endl;
        
        // 写入数据
        for (size_t t = 0; t < nt; ++t) {
            double time_val = (t < r.time.size()) ? r.time[t] : t * 0.1; // 默认dt=0.1
            fout << time_val << " "
                 << r.trace_u12[t] << " "
                 << r.trace_u21[t] << std::endl;
        }
        
        // 分隔不同接收器的数据
        if (ir < rec.size() - 1) {
            fout << std::endl << std::endl;
        }
    }
    
    fout.close();
    std::cout << "Waveform data saved to " << filename << std::endl;
} 