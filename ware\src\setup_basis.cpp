#include "../include/Get_Knot_Vector.hpp"
#include "../include/setup_basis.hpp"
#include "../include/lgwt.hpp"
#include "../include/bspln.hpp"
#include "../include/debug_utils.hpp"
#include "../include/memory_safe_matrix.hpp"
#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <stdexcept>
#include <limits>
#include <memory>

Basis setup_basis(int N,int p){
    Vector tx = Get_Knot_Vector(N, p+1);
    return {N,p,tx};
}

Matrix inner_product2(const Basis& b1, const Basis& b2) {
    DEBUG_FUNCTION_ENTRY();

    // 输入验证和内存安全检查
    if (b1.nb <= 0 || b2.nb <= 0 || b1.pb < 0 || b2.pb < 0) {
        DEBUG_ERROR("Invalid basis parameters: N1=" << b1.nb << ", M2=" << b2.nb
                   << ", pN1=" << b1.pb << ", pM2=" << b2.pb);
        throw std::invalid_argument("Basis parameters must be positive");
    }

    int N1 = b1.nb;
    int M2 = b2.nb;
    int pN1 = b1.pb;
    int pM2 = b2.pb;

    const Vector& t1 = b1.tx;
    const Vector& t2 = b2.tx;

    int kN1 = pN1 + 1;
    int kM2 = pM2 + 1;
    // 固定使用ord_gi = 6，与MATLAB保持一致
    int ord_gi = 6;

    DEBUG_VERBOSE("Computing inner product: " << N1 << "x" << M2 << " basis functions");
    DEBUG_VERBOSE("Orders: pN1=" << pN1 << ", pM2=" << pM2 << ", ord_gi=" << ord_gi);

    // 验证节点向量大小（应该是N+k个节点对应N个基函数）
    Integer expected_t1_size = N1 + kN1;
    Integer expected_t2_size = M2 + kM2;

    if (static_cast<Integer>(t1.size()) != expected_t1_size || static_cast<Integer>(t2.size()) != expected_t2_size) {
        DEBUG_ERROR("Knot vector size mismatch: t1 expected=" << expected_t1_size
                   << " actual=" << t1.size() << ", t2 expected=" << expected_t2_size
                   << " actual=" << t2.size());
        throw std::runtime_error("Knot vector size mismatch in inner_product2");
    }

    // 检查矩阵大小是否合理（防止内存溢出）
    size_t matrix_elements = static_cast<size_t>(N1) * static_cast<size_t>(M2);
    const size_t MAX_MATRIX_ELEMENTS = 100000000;  // 100M elements limit
    if (matrix_elements > MAX_MATRIX_ELEMENTS) {
        DEBUG_ERROR("Matrix too large: " << N1 << "x" << M2 << " = " << matrix_elements << " elements");
        throw std::runtime_error("Matrix size exceeds memory limits");
    }

    try {
        // 使用内存安全的管理器
        MemorySafe::MatrixManager manager;

        // 创建组合节点向量
        // MATLAB: nodes1 = t1((pN1+1):(N1+kN1-pN1))
        // 其中 kN1 = pN1+1, 所以 N1+kN1-pN1 = N1+1
        // MATLAB 1-based (pN1+1):(N1+1) 转换为 C++ 0-based pN1:N1
        std::vector<double> nodes;
        nodes.reserve(N1 + M2 + 2);  // 预分配内存，避免重复分配

        int end1 = N1;  // MATLAB (N1+kN1-pN1) = N1+1, 转为 0-based 是 N1
        int end2 = M2;  // MATLAB (M2+kM2-pM2) = M2+1, 转为 0-based 是 M2

        DEBUG_VERBOSE("Index ranges - t1[" << pN1 << ":" << end1 << "], t2[" << pM2 << ":" << end2 << "]");

        // 构建组合节点向量，带边界检查
        for(int i = pN1; i <= end1; ++i) {
            if (i < 0 || i >= static_cast<int>(t1.size())) {
                DEBUG_ERROR("t1 index out of bounds: " << i << " (size: " << t1.size() << ")");
                throw std::out_of_range("t1 index out of bounds");
            }
            nodes.push_back(t1.safe_get(i, 0));
        }

        for(int i = pM2; i <= end2; ++i) {
            if (i < 0 || i >= static_cast<int>(t2.size())) {
                DEBUG_ERROR("t2 index out of bounds: " << i << " (size: " << t2.size() << ")");
                throw std::out_of_range("t2 index out of bounds");
            }
            nodes.push_back(t2.safe_get(i, 0));
        }

        // 排序并去重
        std::sort(nodes.begin(), nodes.end());
        nodes.erase(std::unique(nodes.begin(), nodes.end()), nodes.end());

        int NB = static_cast<int>(nodes.size()) - 1;
        if (NB <= 0) {
            DEBUG_ERROR("Invalid number of intervals: " << NB);
            throw std::runtime_error("No valid intervals found");
        }

        DEBUG_VERBOSE("Combined nodes: " << nodes.size() << " unique nodes, " << NB << " intervals");

        // 设置积分点和权重（使用内存管理器）
        Matrix* int12 = manager.create_matrix(NB, ord_gi);
        Matrix* wint12 = manager.create_matrix(NB, ord_gi);

        {
            DEBUG_CRITICAL_SECTION("Quadrature setup");
            for(int kd = 0; kd < NB; ++kd) {
                if (kd >= static_cast<int>(nodes.size()) - 1) {
                    DEBUG_ERROR("Node index out of bounds: " << kd << " (max: " << nodes.size() - 2 << ")");
                    throw std::out_of_range("Node index out of bounds");
                }

                try {
                    std::tuple<Vector, Vector> result = lgwt(ord_gi, nodes[kd], nodes[kd+1]);
                    Vector xg = std::get<0>(result);
                    Vector wg = std::get<1>(result);

                    if (xg.size() != ord_gi || wg.size() != ord_gi) {
                        DEBUG_ERROR("lgwt returned wrong size: expected " << ord_gi
                                   << ", got xg=" << xg.size() << ", wg=" << wg.size());
                        throw std::runtime_error("lgwt size mismatch");
                    }

                    for(int g = 0; g < ord_gi; ++g) {
                        Real x_val = xg.safe_get(g, 0);
                        Real w_val = wg.safe_get(g, 0);

                        if (!std::isfinite(x_val) || !std::isfinite(w_val)) {
                            DEBUG_WARNING("Non-finite quadrature values: x=" << x_val << ", w=" << w_val);
                            x_val = 0.0;
                            w_val = 0.0;
                        }

                        int12->at(kd, g) = x_val;
                        wint12->at(kd, g) = w_val;
                    }
                } catch (const std::exception& e) {
                    DEBUG_ERROR("lgwt failed for interval [" << nodes[kd] << ", " << nodes[kd+1] << "]: " << e.what());
                    throw;
                }
            }
        }

        DEBUG_VERBOSE("Quadrature setup completed successfully");

        // ========================================
        // 关键调试信息输出
        // ========================================
        static int call_count = 0;
        call_count++;

        // 只在前几次调用时输出详细信息
        if (call_count <= 3) {
            std::cout << "\n=== inner_product2 调用 #" << call_count << " 关键数据 ===" << std::endl;
            std::cout << "  N1=" << N1 << ", M2=" << M2 << ", ord_gi=" << ord_gi << ", NB=" << NB << std::endl;
            std::cout << "  int12 size: " << int12->rows << "x" << int12->cols << std::endl;
            std::cout << "  wint12 size: " << wint12->rows << "x" << wint12->cols << std::endl;

            // 检查矩阵尺寸匹配
            if (int12->cols != ord_gi || wint12->cols != ord_gi) {
                std::cout << "  ❌ 矩阵列数与ord_gi不匹配!" << std::endl;
            } else {
                std::cout << "  ✓ 矩阵尺寸正确" << std::endl;
            }

            // 输出前几个积分点
            if (NB > 0 && ord_gi > 2) {
                std::cout << "  int12[0,0:2] = [" << int12->safe_get(0,0) << ", "
                          << int12->safe_get(0,1) << ", " << int12->safe_get(0,2) << "]" << std::endl;
                std::cout << "  wint12[0,0:2] = [" << wint12->safe_get(0,0) << ", "
                          << wint12->safe_get(0,1) << ", " << wint12->safe_get(0,2) << "]" << std::endl;
            }

            std::cout << "  所有积分点和权重都有效" << std::endl;
            std::cout << "=== inner_product2 数据输出完成 ===\n" << std::endl;
        }

        // 创建结果矩阵
        Matrix* T = manager.create_matrix(N1, M2);

        // 主计算循环，带进度跟踪和内存安全检查
        {
            DEBUG_CRITICAL_SECTION("Inner product computation");
            ProgressTracker progress(N1 * M2, "inner_product2");

            for(int ib1 = 1; ib1 <= N1; ++ib1) {
                for(int jb1 = 1; jb1 <= M2; ++jb1) {
                    double sum = 0.0;

                    for(int kd = 0; kd < NB; ++kd) {
                        for(int lpt = 0; lpt < ord_gi; ++lpt) {
                            try {
                                // 安全获取积分点和权重
                                double x_point = int12->safe_get(kd, lpt);
                                double weight = wint12->safe_get(kd, lpt);

                                if (!std::isfinite(x_point) || !std::isfinite(weight)) {
                                    continue;  // 跳过无效的积分点
                                }

                                // 计算B样条基函数值
                                double b1 = bspln(t1, N1, ib1, kN1, x_point);
                                double b2 = bspln(t2, M2, jb1, kM2, x_point);

                                // 增强的数值稳定性检查
                                if (std::isfinite(b1) && std::isfinite(b2)) {
                                    double contribution = b1 * b2 * weight;
                                    if (std::isfinite(contribution) && std::abs(contribution) < 1e30) {
                                        sum += contribution;
                                    }
                                } else {
                                    static int warning_count = 0;
                                    if (warning_count < 5) {
                                        DEBUG_WARNING("Non-finite B-spline values: b1=" << b1 << ", b2=" << b2);
                                        warning_count++;
                                    }
                                }
                            } catch (const std::exception& e) {
                                DEBUG_WARNING("Exception in inner loop: " << e.what());
                                continue;  // 继续计算其他项
                            }
                        }
                    }

                    // 安全设置结果
                    try {
                        T->at(ib1 - 1, jb1 - 1) = sum;
                    } catch (const std::exception& e) {
                        DEBUG_ERROR("Failed to set result at (" << (ib1-1) << ", " << (jb1-1) << "): " << e.what());
                        throw;
                    }

                    progress.update();
                }
            }
        }

        DEBUG_FUNCTION_EXIT();
        return *T;  // 返回拷贝，manager会自动清理原始内存

    } catch (const std::exception& e) {
        DEBUG_ERROR("inner_product2 failed: " << e.what());
        throw;
    } catch (...) {
        DEBUG_ERROR("inner_product2 failed with unknown exception");
        throw std::runtime_error("Unknown error in inner_product2");
    }
} 