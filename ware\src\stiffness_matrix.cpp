#include "../include/stiffness_matrix.hpp"
#include "../include/bspln.hpp"
#include "../include/dbspln.hpp"
#include "../include/mass_matrix.hpp"

Matrix stiffness_matrix(int p,int N1,
                        const Vector& t1,const Vector& t2,
                        const Matrix& xint,const Matrix& wint,
                        int option){

    int kx1 = p + 1;            // order of first basis
    int kx2 = kx1 - 1;          // order of second basis (Nx1-1)

    int NB_intervals = xint.rows;
    int ord_gi       = xint.cols;

    if(option==0){
        Matrix kk(N1, N1-1, 0.0);

        // 完全按照MATLAB逻辑：分块计算+复制优化
        std::cout << "DEBUG: stiffness_matrix option=0, N1=" << N1 << ", p=" << p << std::endl;

        // 第一块：计算前2*p行，前3*p列
        for(int ib1 = 1; ib1 <= 2*p; ++ib1) {
            for(int jb1 = 1; jb1 <= 3*p; ++jb1) {
                double sum = 0.0;
                for(int kd = 0; kd < NB_intervals; ++kd) {
                    for(int lpt = 0; lpt < ord_gi; ++lpt) {
                        double x = xint(kd, lpt);
                        double w = wint(kd, lpt);
                        double b1tmp1 = dbspln(t1, N1, ib1, kx1, x, 1);
                        double b1tmp2 = bspln(t2, N1-1, jb1, kx2, x);
                        sum += b1tmp1 * b1tmp2 * w;
                    }
                }
                kk(ib1-1, jb1-1) = sum;
            }
        }

        // 第二块：复制中间部分 (MATLAB优化)
        for(int i = 2*p+1; i <= N1-p*2; ++i) {
            for(int j = i-p; j <= i+p; ++j) {
                if(j >= 1 && j <= N1-1) {  // 边界检查
                    kk(i-1, j-1) = kk(i-2, j-2);  // MATLAB: kk(i,(i-p):(i+p))=kk(i-1,(i-1-p):(i-1+p))
                }
            }
        }

        // 第三块：计算后面部分
        for(int ib1 = N1-p*2+1; ib1 <= N1; ++ib1) {
            for(int jb1 = N1-3*p; jb1 <= N1-1; ++jb1) {
                if(jb1 >= 1) {  // 边界检查
                    double sum = 0.0;
                    for(int kd = 0; kd < NB_intervals; ++kd) {
                        for(int lpt = 0; lpt < ord_gi; ++lpt) {
                            double x = xint(kd, lpt);
                            double w = wint(kd, lpt);
                            double b1tmp1 = dbspln(t1, N1, ib1, kx1, x, 1);
                            double b1tmp2 = bspln(t2, N1-1, jb1, kx2, x);
                            sum += b1tmp1 * b1tmp2 * w;
                        }
                    }
                    kk(ib1-1, jb1-1) = sum;
                }
            }
        }
        return kk;
    }else{
        Matrix kk(N1-1, N1, 0.0);

        // 完全按照MATLAB逻辑：分块计算+复制优化 (option=1)
        std::cout << "DEBUG: stiffness_matrix option=1, N1=" << N1 << ", p=" << p << std::endl;

        // 第一块：计算前2*p行，前3*p列
        for(int ib1 = 1; ib1 <= 2*p; ++ib1) {
            for(int jb1 = 1; jb1 <= 3*p; ++jb1) {
                double sum = 0.0;
                for(int kd = 0; kd < NB_intervals; ++kd) {
                    for(int lpt = 0; lpt < ord_gi; ++lpt) {
                        double x = xint(kd, lpt);
                        double w = wint(kd, lpt);
                        double b1tmp1 = dbspln(t2, N1-1, ib1, kx2, x, 1);  // 注意：使用t2, kx2
                        double b1tmp2 = bspln(t1, N1, jb1, kx1, x);        // 注意：使用t1, kx1
                        sum += b1tmp1 * b1tmp2 * w;
                    }
                }
                kk(ib1-1, jb1-1) = sum;
            }
        }

        // 第二块：复制中间部分 (MATLAB优化)
        for(int i = 2*p+1; i <= N1-p*2; ++i) {
            for(int j = i-p; j <= i+p; ++j) {
                if(j >= 1 && j <= N1) {  // 边界检查
                    kk(i-1, j-1) = kk(i-2, j-2);  // MATLAB: kk(i,(i-p):(i+p))=kk(i-1,(i-1-p):(i-1+p))
                }
            }
        }

        // 第三块：计算后面部分
        for(int ib1 = N1-p*2+1; ib1 <= N1-1; ++ib1) {  // 注意：到N1-1
            for(int jb1 = N1-3*p; jb1 <= N1; ++jb1) {   // 注意：到N1
                if(jb1 >= 1) {  // 边界检查
                    double sum = 0.0;
                    for(int kd = 0; kd < NB_intervals; ++kd) {
                        for(int lpt = 0; lpt < ord_gi; ++lpt) {
                            double x = xint(kd, lpt);
                            double w = wint(kd, lpt);
                            double b1tmp1 = dbspln(t2, N1-1, ib1, kx2, x, 1);  // 注意：使用t2, kx2
                            double b1tmp2 = bspln(t1, N1, jb1, kx1, x);        // 注意：使用t1, kx1
                            sum += b1tmp1 * b1tmp2 * w;
                        }
                    }
                    kk(ib1-1, jb1-1) = sum;
                }
            }
        }
        return kk;
    }
}