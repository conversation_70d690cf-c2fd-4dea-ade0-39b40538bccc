#include "../include/update_wavefields2dA.hpp"
#include "../include/common_types.hpp"
#include <iostream>
#include <cmath>
#include <omp.h>

std::vector<Domain2dA> update_wavefields2dA(std::vector<Domain2dA>& OM, double dt)
{
    std::cout << "  [DEBUG] update_wavefields2dA: 开始更新，dt = " << dt << std::endl;
    const double dt2 = dt * dt;
    bool found_bad_value = false;

    for(int iom = 0; iom < static_cast<int>(OM.size()); ++iom){
        Domain2dA &d = OM[iom];

        const int nx = d.state.U12.rows;
        const int nz = d.state.U12.cols;
        
        // 打印每个域的矩阵维度
        std::cout << "  [DEBUG] Domain " << iom << ": U12 size = " 
                  << nx << "x" << nz << ", dU2dtt12 size = " 
                  << d.state.dU2dtt12.rows << "x" << d.state.dU2dtt12.cols << std::endl;

        // Umid是3D数组，这里不需要分配
        // 在save_wavefields2dA中会根据需要分配

        // 检查加速度是否有NaN/Inf - 分别检查不同维度的矩阵
        bool has_nan_acc = false;

        // 检查dU2dtt12 (Nx1 x Nz2)
        for(int i = 0; i < d.state.dU2dtt12.rows && !has_nan_acc; ++i) {
            for(int j = 0; j < d.state.dU2dtt12.cols && !has_nan_acc; ++j) {
                if(!std::isfinite(d.state.dU2dtt12(i,j))) {
                    has_nan_acc = true;
                    std::cout << "  [DEBUG] 警告: 发现NaN/Inf在域 " << iom << " 的dU2dtt12加速度场 at ("
                              << i << "," << j << "): dU2dtt12=" << d.state.dU2dtt12(i,j) << std::endl;
                }
            }
        }

        // 检查dU2dtt21 (Nx2 x Nz1) - 使用正确的维度
        for(int i = 0; i < d.state.dU2dtt21.rows && !has_nan_acc; ++i) {
            for(int j = 0; j < d.state.dU2dtt21.cols && !has_nan_acc; ++j) {
                if(!std::isfinite(d.state.dU2dtt21(i,j))) {
                    has_nan_acc = true;
                    std::cout << "  [DEBUG] 警告: 发现NaN/Inf在域 " << iom << " 的dU2dtt21加速度场 at ("
                              << i << "," << j << "): dU2dtt21=" << d.state.dU2dtt21(i,j) << std::endl;
                }
            }
        }

        // -------- 更新 U12 grid (Nx1 x Nz2) --------
        for(int i = 0; i < d.state.U12.rows; ++i){
            for(int k = 0; k < d.state.U12.cols; ++k){
                const double prev1_U12 = d.state.U12_1(i, k); // U12 at t-Δt
                const double prev0_U12 = d.state.U12_0(i, k); // U12 at t-2Δt
                const double acc_U12   = d.state.dU2dtt12(i, k);

                const double newU12 = 2.0 * prev1_U12 - prev0_U12 + dt2 * acc_U12;

                // 检查计算是否产生NaN/Inf
                if(!std::isfinite(newU12) && !found_bad_value) {
                    found_bad_value = true;
                    std::cout << "  [ERROR] 域 " << iom << " 中 U12(" << i << "," << k << ") 计算得到非法值: "
                              << newU12 << std::endl;
                    std::cout << "    输入值: prev1_U12=" << prev1_U12
                              << ", prev0_U12=" << prev0_U12
                              << ", acc_U12=" << acc_U12
                              << ", dt2=" << dt2 << std::endl;
                }

                d.state.U12_0(i, k) = prev1_U12; // roll buffers
                d.state.U12_1(i, k) = newU12;
                d.state.U12(i,  k)  = newU12;
            }
        }

        // -------- 更新 U21 grid (Nx2 x Nz1) - 使用正确的维度 --------
        for(int i = 0; i < d.state.U21.rows; ++i){
            for(int k = 0; k < d.state.U21.cols; ++k){
                const double prev1_U21 = d.state.U21_1(i, k);
                const double prev0_U21 = d.state.U21_0(i, k);
                const double acc_U21   = d.state.dU2dtt21(i, k);

                const double newU21 = 2.0 * prev1_U21 - prev0_U21 + dt2 * acc_U21;

                // 检查计算是否产生NaN/Inf
                if(!std::isfinite(newU21) && !found_bad_value) {
                    found_bad_value = true;
                    std::cout << "  [ERROR] 域 " << iom << " 中 U21(" << i << "," << k << ") 计算得到非法值: "
                              << newU21 << std::endl;
                    std::cout << "    输入值: prev1_U21=" << prev1_U21
                              << ", prev0_U21=" << prev0_U21
                              << ", acc_U21=" << acc_U21
                              << ", dt2=" << dt2 << std::endl;
                }

                d.state.U21_0(i, k) = prev1_U21;
                d.state.U21_1(i, k) = newU21;
                d.state.U21(i,  k)  = newU21;
            }
        }

        // After using the accelerations to update displacement we reset them to zero
        // so that the next time step starts with fresh values (like MATLAB implementation).

        // 重置 dU2dtt12 (Nx1 x Nz2)
        for(int i = 0; i < d.state.dU2dtt12.rows; ++i){
            for(int k = 0; k < d.state.dU2dtt12.cols; ++k){
                d.state.dU2dtt12(i, k) = 0.0;
            }
        }

        // 重置 dU2dtt21 (Nx2 x Nz1)
        for(int i = 0; i < d.state.dU2dtt21.rows; ++i){
            for(int k = 0; k < d.state.dU2dtt21.cols; ++k){
                d.state.dU2dtt21(i, k) = 0.0;
            }
        }
    }

    if(found_bad_value) {
        std::cerr << "[update_wavefields2dA] 警告: 在更新波场时发现NaN/Inf" << std::endl;
    } else {
        std::cout << "  [DEBUG] update_wavefields2dA: 所有域波场更新完成" << std::endl;
    }

    return OM;
} 